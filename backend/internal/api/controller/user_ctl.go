package controller

import (
	"bdb-backend/internal/service"
	"bdb-backend/internal/types"
	"bdb-backend/pkg/logger"
	"bdb-backend/pkg/response"
	"bdb-backend/pkg/validator"

	"github.com/gin-gonic/gin"
)

// UserController 用户控制器
type UserController struct {
	userService service.UserService
	validator   validator.Validator
}

// NewUserController 创建用户控制器
func NewUserController(
	userService service.UserService,
	validator validator.Validator,
) *UserController {
	return &UserController{
		userService: userService,
		validator:   validator,
	}
}

// GetProfile 获取用户资料
func (c *UserController) GetProfile(ctx *gin.Context) {
	userID := GetUserID(ctx)

	profile, err := c.userService.GetUserProfile(ctx, userID)
	if err != nil {
		logger.ErrorCtx(ctx, "获取用户资料失败", err, "user_id", userID)
		response.ServerError(ctx, "获取用户资料失败")
		return
	}

	response.OK(ctx, profile)
}

// UpdateProfile 更新用户资料
func (c *UserController) UpdateProfile(ctx *gin.Context) {
	userID := GetUserID(ctx)

	var req types.UpdateUserProfileRequest
	if !c.validator.CheckJSON(ctx, &req) {
		return
	}

	if err := c.userService.UpdateUserProfile(ctx, userID, &req); err != nil {
		logger.ErrorCtx(ctx, "更新用户资料失败", err, "user_id", userID)
		response.ServerError(ctx, err.Error())
		return
	}

	response.OK(ctx, gin.H{"message": "用户资料更新成功"})
}

// UploadAvatar 上传头像
func (c *UserController) UploadAvatar(ctx *gin.Context) {
	userID := GetUserID(ctx)

	file, err := ctx.FormFile("avatar")
	if err != nil {
		response.BadRequest(ctx, "未提供头像文件")
		return
	}

	// TODO: 实现头像上传逻辑
	// 这里需要添加文件上传和处理逻辑
	_ = userID
	_ = file

	response.OK(ctx, gin.H{"message": "头像上传功能待实现"})
}

// GetUserInfo 获取指定用户信息
func (c *UserController) GetUserInfo(ctx *gin.Context) {
	userID := GetUserID(ctx)
	userInfo, err := c.userService.GetUserProfile(ctx, userID)
	if err != nil {
		response.ServerError(ctx, "获取用户信息失败")
		return
	}

	response.OK(ctx, userInfo)
}

// ChangePassword 修改密码
func (c *UserController) ChangePassword(ctx *gin.Context) {
	// TODO: 实现密码修改逻辑
	// 目前项目使用微信登录，可能不需要密码功能
	response.OK(ctx, gin.H{"message": "密码功能待实现"})
}

// GetMyProfile 获取当前登录用户的个人资料
func (c *UserController) GetMyProfile(ctx *gin.Context) {
	userID := GetUserID(ctx)
	if userID == 0 {
		return // GetUserID 内部已处理响应
	}

	profile, err := c.userService.GetUserProfile(ctx, userID)
	if err != nil {
		logger.ErrorCtx(ctx, "获取用户资料失败", err, "user_id", userID)
		response.ServerError(ctx, "获取用户资料失败")
		return
	}
	response.OK(ctx, profile)
}

// UpdateMyProfile 更新当前登录用户的个人资料
func (c *UserController) UpdateMyProfile(ctx *gin.Context) {
	userID := GetUserID(ctx)

	var req types.UpdateUserProfileRequest
	if !c.validator.CheckJSON(ctx, &req) {
		return
	}

	if err := c.userService.UpdateUserProfile(ctx, userID, &req); err != nil {
		response.Fail(ctx, "更新用户资料失败")
		return
	}
	response.OK(ctx, gin.H{"message": "用户资料更新成功"})
}

// UpdateUserSettings 更新用户设置
func (c *UserController) UpdateUserSettings(ctx *gin.Context) {
	userID := GetUserID(ctx)

	var req types.UpdateUserSettingsRequest
	if !c.validator.CheckJSON(ctx, &req) {
		return
	}

	if err := c.userService.UpdateUserSettings(ctx, userID, &req); err != nil {
		logger.ErrorCtx(ctx, "更新用户设置失败", err, "user_id", userID)
		response.ServerError(ctx, err.Error())
		return
	}

	response.OK(ctx, gin.H{"message": "用户设置更新成功"})
}

// GetUserSettings 获取用户设置
func (c *UserController) GetUserSettings(ctx *gin.Context) {
	userID := GetUserID(ctx)

	settings, err := c.userService.GetUserSettings(ctx, userID)
	if err != nil {
		logger.ErrorCtx(ctx, "获取用户设置失败", err, "user_id", userID)
		response.ServerError(ctx, "获取用户设置失败")
		return
	}

	response.OK(ctx, settings)
}

// BindPhone 绑定手机号
func (c *UserController) BindPhone(ctx *gin.Context) {
	userID := GetUserID(ctx)

	var req types.BindPhoneRequest
	if !c.validator.CheckJSON(ctx, &req) {
		return
	}

	resp, err := c.userService.BindPhone(ctx, userID, &req)
	if err != nil {
		logger.ErrorCtx(ctx, "绑定手机号失败", err, "user_id", userID)
		response.ServerError(ctx, err.Error())
		return
	}

	response.OK(ctx, resp)
}

// ChangePhoneBySMS 通过短信更换手机号
func (c *UserController) ChangePhoneBySMS(ctx *gin.Context) {
	userID := GetUserID(ctx)

	var req types.ChangePhoneBySMSRequest
	if !c.validator.CheckJSON(ctx, &req) {
		return
	}

	err := c.userService.ChangePhoneBySMS(ctx, userID, &req)
	if err != nil {
		logger.ErrorCtx(ctx, "更换手机号失败", err, "user_id", userID)
		response.ServerError(ctx, err.Error())
		return
	}

	response.OK(ctx, gin.H{"message": "手机号更换成功"})
}

// ChangePhoneByWechat 通过微信更换手机号
func (c *UserController) ChangePhoneByWechat(ctx *gin.Context) {
	userID := GetUserID(ctx)

	var req types.ChangePhoneByWechatRequest
	if !c.validator.CheckJSON(ctx, &req) {
		return
	}

	err := c.userService.ChangePhoneByWechat(ctx, userID, &req)
	if err != nil {
		logger.ErrorCtx(ctx, "更换手机号失败", err, "user_id", userID)
		response.ServerError(ctx, err.Error())
		return
	}

	response.OK(ctx, gin.H{"message": "手机号更换成功"})
}

// GetMyFullProfile 获取当前登录用户的完整资料
// func (c *UserController) GetMyFullProfile(ctx *gin.Context) {
// 	userID := GetUserID(ctx)

// 	user, err := c.userService.GetFullProfile(ctx, userID)
// 	if err != nil {
// 		logger.ErrorCtx(ctx, "获取用户完整资料失败", err, "user_id", userID)
// 		response.ServerError(ctx, "获取用户完整资料失败")
// 		return
// 	}

// 	response.OK(ctx, user)
// }

// // FollowUser 关注用户
// func (c *UserController) FollowUser(ctx *gin.Context) {
// 	userID := GetUserID(ctx)
// 	if userID == 0 {
// 		return
// 	}

// 	var req types.FollowRequest
// 	if err := c.validator.ValidateJSON(ctx, &req); err != nil {
// 		response.BadRequest(ctx, err.Error())
// 		return
// 	}

// 	if err := c.userService.Follow(ctx, userID, req.TargetUserID); err != nil {
// 		logger.ErrorCtx(ctx, "关注用户失败", err, "user_id", userID, "target_user_id", req.TargetUserID)
// 		response.ServerError(ctx, err.Error())
// 		return
// 	}
// 	response.OK(ctx, gin.H{"message": "操作成功"})
// }

// // GetUserByID 获取指定用户的公开信息
// func (c *UserController) GetUserByID(ctx *gin.Context) {
// 	targetUserIDStr := ctx.Param("user_id")
// 	targetUserID, err := strconv.ParseUint(targetUserIDStr, 10, 32)
// 	if err != nil {
// 		response.BadRequest(ctx, "无效的用户ID")
// 		return
// 	}

// 	userInfo, err := c.userService.GetProfile(ctx, uint(targetUserID))
// 	if err != nil {
// 		logger.ErrorCtx(ctx, "获取用户信息失败", err, "target_user_id", targetUserID)
// 		response.ServerError(ctx, "获取用户信息失败")
// 		return
// 	}
// 	response.OK(ctx, userInfo)
// }

// // UpdateLastLoginAt 更新用户最后登录时间
// func (c *UserController) UpdateLastLoginAt(ctx *gin.Context) {
// 	userID := GetUserID(ctx)
// 	if userID == 0 {
// 		return
// 	}

// 	if err := c.userService.UpdateLastLoginAt(ctx, userID); err != nil {
// 		response.ServerError(ctx, "Failed to update last login time")
// 		return
// 	}
// 	response.OK(ctx, nil)
// }
