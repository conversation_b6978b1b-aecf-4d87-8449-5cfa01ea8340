package router

import (
	"bdb-backend/internal/api/controller"
	"bdb-backend/internal/api/middleware"
	"bdb-backend/pkg/config"
	"bdb-backend/pkg/jwt"

	"github.com/gin-gonic/gin"
)

func SetupRouter(
	cfg *config.Config,
	authCtl *controller.AuthController,
	userCtl *controller.UserController,
	userAddressCtl *controller.UserAddressController,
	gigCtl *controller.GigController,
	messageCtl *controller.MessageController,
	chatCtl *controller.ChatController,
	notificationCtl *controller.NotificationController,
	commonCtl *controller.CommonController,
	jwtService jwt.JWTService,
) *gin.Engine {
	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	r := gin.New()

	// 全局中间件
	r.Use(middleware.Logger())
	r.Use(gin.Recovery()) // 使用gin内置的recovery中间件

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	// API路由组
	v1 := r.Group("/api/v1")
	{
		// 认证路由（无需token验证）
		auth := v1.Group("/auth")
		{
			auth.POST("/login", authCtl.Login)
			auth.POST("/wechat-login", authCtl.WechatLogin)
			auth.POST("/sms", authCtl.SendSmsCode)
			auth.POST("/check-user-by-code", authCtl.CheckUserByCode)
			auth.POST("/refresh", authCtl.RefreshToken)
			// 开发测试登录 - 仅在开发环境启用
			if cfg.DevLogin.Enabled {
				auth.POST("/dev-login", authCtl.DevLogin)
			}
		}

		// 无需认证的路由
		apiV1 := v1.Group("/")
		{
			apiV1.GET("/gigs", gigCtl.List)

		}

		// 需要认证的路由
		authorized := v1.Group("")
		authorized.Use(middleware.AuthMiddleware(jwtService))
		{
			// 公共接口
			common := authorized.Group("/common")
			{
				common.POST("/upload-token", commonCtl.GetUploadToken)
			}

			// 用户相关
			users := authorized.Group("/users")
			{
				users.GET("/profile", userCtl.GetProfile)
				users.POST("/profile", userCtl.UpdateProfile)
				users.POST("/avatar", userCtl.UploadAvatar)
				users.GET("/:user_id", userCtl.GetUserInfo)
				users.POST("/password", userCtl.ChangePassword)

				// 手机号绑定和更换
				users.POST("/bind-phone", userCtl.BindPhone)
				users.POST("/phone/change-by-sms", userCtl.ChangePhoneBySMS)
				users.POST("/phone/change-by-wechat", userCtl.ChangePhoneByWechat)
			}

			// 用户地址管理（独立路由组）
			addresses := authorized.Group("/addresses")
			{
				addresses.POST("", userAddressCtl.CreateAddress)
				addresses.GET("", userAddressCtl.GetAddressList)
				addresses.GET("/:id", userAddressCtl.GetAddressDetail)
				addresses.PUT("/:id", userAddressCtl.UpdateAddress)
				addresses.DELETE("/:id", userAddressCtl.DeleteAddress)
				addresses.PUT("/:id/default", userAddressCtl.SetDefaultAddress)
				addresses.PUT("/:id/use", userAddressCtl.UseAddress)
			}

			// --- Gig (零工) 模块 - Go-Live RESTful 路由 ---
			gigAuthGroup := authorized.Group("/gigs")
			{
				gigAuthGroup.POST("", gigCtl.Publish) // 发布零工
				apiV1.GET("/gigs/:id", gigCtl.Detail)
				gigAuthGroup.DELETE("/:id", gigCtl.Delete)             // 删除零工
				gigAuthGroup.GET("/manage", gigCtl.ListManageableGigs) // 获取我发布的零工

				gigAuthGroup.POST("/applications", gigCtl.ApplyForGig)                     // 申请零工
				gigAuthGroup.POST("/applications/review", gigCtl.ReviewApplication)        // 审核零工申请
				gigAuthGroup.GET("/applications/my", gigCtl.GetUserApplications)           // 获取用户零工申请
				gigAuthGroup.GET("/:id/applications", gigCtl.GetGigApplications)           // 获取零工申请
				gigAuthGroup.GET("/:id/application-status", gigCtl.CheckApplicationStatus) // 检查用户申请状态

				gigAuthGroup.GET("/calendar/monthly", gigCtl.GetMonthlyStats) // 获取月度统计
				gigAuthGroup.GET("/calendar/daily", gigCtl.GetDailyGigs)      // 获取每日零工
			}

			// 聊天相关（仅私聊功能）
			chat := authorized.Group("/chat")
			{
				// 会话管理
				chat.GET("/conversations", chatCtl.GetConversations)
				chat.POST("/conversations", chatCtl.CreateSingleConversation)
				chat.GET("/conversations/:id", chatCtl.GetConversationByID)
				chat.DELETE("/conversations/:id", chatCtl.DeleteConversation)
				chat.PUT("/conversations/:id/pin", chatCtl.PinConversation)
				chat.PUT("/conversations/:id/mute", chatCtl.MuteConversation)

				// 会话消息管理
				chat.GET("/conversations/:id/messages", messageCtl.GetMessages)

				// 消息操作
				messages := chat.Group("/messages")
				{
					messages.POST("", messageCtl.SendMessage)
					messages.PUT("/read", messageCtl.MarkMessagesAsRead)
					messages.DELETE("/:id/revoke", messageCtl.RevokeMessage)
				}
			}

			// 通知相关
			notifications := authorized.Group("/notifications")
			{
				notifications.POST("", notificationCtl.SendNotification)
				notifications.GET("", notificationCtl.GetNotifications)
				notifications.PUT("/:id/read", notificationCtl.MarkNotificationAsRead)
				notifications.PUT("/read-all", notificationCtl.MarkAllAsRead)
			}
		}
	}

	return r
}
