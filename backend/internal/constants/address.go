package constants

// 地址相关常量
const (
	// MaxAddressesPerUser 每个用户最多可以创建的地址数量
	MaxAddressesPerUser = 20

	// DefaultPageSize 地址列表查询默认分页大小
	DefaultPageSize = 20

	// MaxPageSize 地址列表查询最大分页大小
	MaxPageSize = 100

	// 坐标范围常量（中国境内）
	MinLatitude  = 3.86   // 中国最南端
	MaxLatitude  = 53.55  // 中国最北端
	MinLongitude = 73.66  // 中国最西端
	MaxLongitude = 135.05 // 中国最东端
)

// 地址标签常量
const (
	LabelHome    = "家"
	LabelCompany = "公司"
	LabelSchool  = "学校"
	LabelOther   = "其他"
)

// 地址相关错误消息
const (
	ErrAddressNotFound     = "地址不存在"
	ErrAddressNotBelongs   = "无权访问该地址"
	ErrAddressLimitReached = "地址数量已达上限"
	ErrInvalidCoordinates  = "坐标信息无效"
)
