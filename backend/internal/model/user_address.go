package model

import (
	"time"
)

// UserAddress 用户地址模型
type UserAddress struct {
	BaseModel
	SoftDelete
	UserID        uint       `gorm:"not null;index:idx_user_addresses_user_id" json:"user_id"`
	Name          string     `gorm:"type:varchar(100);not null" json:"name"`
	Address       string     `gorm:"type:varchar(255);not null" json:"address"`
	DetailAddress string     `gorm:"type:varchar(255)" json:"detail_address"`
	FullAddress   string     `gorm:"type:varchar(500);not null" json:"full_address"`
	Latitude      float64    `gorm:"type:decimal(9,6);default:0" json:"latitude"`
	Longitude     float64    `gorm:"type:decimal(10,6);default:0" json:"longitude"`
	IsDefault     bool       `gorm:"default:false;index:idx_user_addresses_is_default" json:"is_default"`
	LastUsedAt    *time.Time `gorm:"type:timestamp(0)" json:"last_used_at"`
}

func (ua UserAddress) TableName() string {
	return "user_addresses"
}
