/**
 * 零工模块通用样式规范
 * 统一颜色系统、字体层级、布局规范
 */

// ==================== 颜色系统 ====================
// 使用 app.css 中定义的颜色变量，不重复定义
// 主色系使用 --primary 系列
// 状态色使用 --text-* 系列
// 背景色使用 --bg-* 系列
// 文本色使用 --text-* 系列

// ==================== 字体层级 ====================
.gig-text {
  // 标题层级 - 使用 app.css 中的字体大小变量
  &-title-xl {
    font-size: 48rpx;
    font-weight: 600;
    line-height: var(--line-height-small);
  }
  &-title-lg {
    font-size: 40rpx;
    font-weight: 500;
    line-height: var(--line-height-small);
  }
  &-title-md {
    font-size: 36rpx;
    font-weight: 500;
    line-height: var(--line-height-normal);
  }
  &-title-sm {
    font-size: 32rpx;
    font-weight: 500;
    line-height: var(--line-height-normal);
  }

  // 正文层级
  &-body-lg {
    font-size: 30rpx;

    line-height: var(--line-height-large);
  }
  &-body-md {
    line-height: var(--line-height-large);
  }
  &-body-sm {
    font-size: 26rpx;

    line-height: var(--line-height-large);
  }

  // 辅助文本
  &-caption {
    font-size: 24rpx;

    line-height: var(--line-height-normal);
  }
  &-label {
    font-size: 22rpx;
    font-weight: 500;
    line-height: var(--line-height-normal);
  }

  // 颜色变体 - 使用 app.css 中的颜色变量
  &-primary {
  }
  &-secondary {
    color: var(--text-secondary);
  }
  &-tertiary {
    color: var(--text-info);
  }
  &-accent {
    color: var(--primary);
  }
  &-success {
    color: var(--text-green);
  }
  &-warning {
    color: var(--text-yellow);
  }
  &-danger {
    color: var(--text-red);
  }
  &-info {
    color: var(--text-blue);
  }
}

// ==================== 间距系统 ====================
.gig-spacing {
  // 内边距 - 使用 app.css 中的间距变量
  &-p-xs {
    padding: var(--spacing);
  }
  &-p-sm {
    padding: 12rpx;
  }
  &-p-md {
    padding: var(--spacing-2);
  }
  &-p-lg {
    padding: var(--spacing-3);
  }
  &-p-xl {
    padding: var(--spacing);
  }
  &-p-2xl {
    padding: 40rpx;
  }

  // 外边距
  &-m-xs {
    margin: var(--spacing);
  }
  &-m-sm {
    margin: 12rpx;
  }
  &-m-md {
    margin: var(--spacing-2);
  }
  &-m-lg {
    margin: var(--spacing-3);
  }
  &-m-xl {
    margin: var(--spacing);
  }
  &-m-2xl {
    margin: 40rpx;
  }

  // 垂直间距
  &-mb-xs {
    margin-bottom: var(--spacing);
  }
  &-mb-sm {
    margin-bottom: 12rpx;
  }
  &-mb-md {
    margin-bottom: var(--spacing-2);
  }
  &-mb-lg {
    margin-bottom: var(--spacing-3);
  }
  &-mb-xl {
    margin-bottom: var(--spacing);
  }
  &-mb-2xl {
    margin-bottom: 40rpx;
  }
}

// ==================== 卡片组件 ====================
.gig-card {
  background: var(--bg-card);
  border-radius: var(--radius-3);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s ease;


  // 卡片变体
  &--elevated {
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  }

  &--flat {
    box-shadow: none;
    border: 1rpx solid var(--border-color);
  }

  // 卡片内容区域
  &__header {
    margin-bottom: 12rpx;
  }

  &__body {
    margin-bottom: 12rpx;
  }

  &__footer {
    margin-bottom: 12rpx;
    border-top: 1rpx solid var(--border-color);
  }
}

// ==================== 按钮组件 ====================
.gig-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing);
  border: none;
  border-radius: var(--radius-4);
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;

  // 尺寸变体
  &--sm {
    padding: 12rpx var(--spacing-3);
    font-size: 24rpx;
    border-radius: var(--radius-3);
  }

  &--md {
    padding: var(--spacing-2) var(--spacing);
    font-size: 26rpx;
  }

  &--lg {
    padding: 20rpx 40rpx;

    border-radius: 44rpx;
  }

  // 颜色变体
  &--primary {
    background: linear-gradient(135deg, var(--primary), var(--primary-400));
    color: var(--text-inverse);
    box-shadow: 0 4rpx 16rpx rgba(255, 109, 0, 0.3);

  }

  &--secondary {
    background: var(--bg-tag);


  }

  &--success {
    background: linear-gradient(135deg, var(--text-green), #4ade80);
    color: var(--text-inverse);
    box-shadow: 0 4rpx 16rpx rgba(34, 197, 94, 0.3);
  }

  &--danger {
    background: var(--bg-danger-light);
    color: var(--text-red);

  }

  &--outline {
    background: transparent;
    border: 2rpx solid var(--primary);
    color: var(--primary);

  }

  // 禁用状态
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
  }
}

// ==================== 标签组件 ====================
.gig-tag {
  display: inline-flex;
  align-items: center;
  padding: 12rpx 12rpx;
  border-radius: var(--radius-2);
  font-size: 22rpx;
  font-weight: 500;
  background: var(--bg-tag);
  color: var(--text-secondary);

  // 颜色变体
  &--primary {
    background: var(--bg-primary-light);
    color: var(--primary);
  }

  &--success {
    background: var(--bg-success-light);
    color: var(--text-green);
  }

  &--warning {
    background: var(--bg-warning-light);
    color: var(--text-yellow);
  }

  &--danger {
    background: var(--bg-danger-light);
    color: var(--text-red);
  }

  &--urgent {
    background: linear-gradient(135deg, var(--text-red), #ff6b6b);
    color: var(--text-inverse);
    font-weight: 500;
    box-shadow: 0 2rpx 8rpx rgba(245, 44, 55, 0.3);
  }
}

// ==================== 状态徽章 ====================
.gig-status {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing) var(--spacing-2);
  border-radius: var(--radius-3);
  font-size: 24rpx;
  font-weight: 500;

  &--recruiting {
    background: var(--bg-success-light);
    color: var(--text-green);
  }

  &--paused {
    background: var(--bg-warning-light);
    color: var(--text-yellow);
  }

  &--completed {
    background: var(--bg-primary-light);
    color: var(--primary);
  }

  &--closed {
    background: var(--bg-tag);
    color: var(--text-info);
  }
}

// ==================== 布局工具类 ====================
.gig-flex {
  display: flex;

  &--center {
    align-items: center;
    justify-content: center;
  }

  &--between {
    justify-content: space-between;
  }

  &--column {
    flex-direction: column;
  }

  &--wrap {
    flex-wrap: wrap;
  }
}

.gig-grid {
  display: grid;

  &--2 {
    grid-template-columns: repeat(2, 1fr);
  }
  &--3 {
    grid-template-columns: repeat(3, 1fr);
  }
  &--4 {
    grid-template-columns: repeat(4, 1fr);
  }

  &--gap-sm {
    gap: 12rpx;
  }
  &--gap-md {
    gap: var(--spacing-2);
  }
  &--gap-lg {
    gap: var(--spacing-3);
  }
}

// ==================== 响应式断点 ====================
@media (max-width: 750rpx) {
  .gig-grid--responsive {
    grid-template-columns: 1fr !important;
  }

  .gig-flex--responsive {
    flex-direction: column;
    align-items: stretch;
  }
}
