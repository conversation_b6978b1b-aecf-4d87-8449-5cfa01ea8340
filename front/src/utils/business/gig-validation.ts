/**
 * 零工发布表单验证工具
 * 统一管理表单验证规则，简化冗长的验证函数
 */

import { showToast } from '@/utils/ui/feedback'
import type { CreateGigRequest } from '@/types/gig'

// 通用验证规则接口
interface ValidationRule {
  validate: (value: any, formData?: CreateGigRequest) => boolean
  message: string
}

// 通用验证规则集合
const rules = {
  // 必填验证
  required: (message: string = '此字段为必填项'): ValidationRule => ({
    validate: (value) => value != null && value !== '' && String(value).trim() !== '',
    message
  }),

  // 长度范围验证
  length: (min: number, max: number, fieldName: string): ValidationRule => ({
    validate: (value) => {
      const str = String(value).trim()
      return str.length >= min && str.length <= max
    },
    message: `${fieldName}长度应在${min}-${max}个字符之间`
  }),

  // 数值范围验证
  range: (min: number, max: number, fieldName: string): ValidationRule => ({
    validate: (value) => Number(value) >= min && Number(value) <= max,
    message: `${fieldName}应在${min}-${max}之间`
  }),

  // 手机号验证
  phone: (): ValidationRule => ({
    validate: (value) => /^1[3-9]\d{9}$/.test(String(value)),
    message: '请输入正确的手机号码'
  }),

  // 工作时长验证
  workDuration: (): ValidationRule => ({
    validate: (value, formData) => {
      if (!formData?.start_time || !formData?.end_time) return false
      
      const startTime = formData.start_time.split(' ')[1] || '00:00:00'
      const endTime = formData.end_time.split(' ')[1] || '00:00:00'
      
      const [startHour, startMin] = startTime.split(':').map(Number)
      const [endHour, endMin] = endTime.split(':').map(Number)
      
      let startMinutes = startHour * 60 + startMin
      let endMinutes = endHour * 60 + endMin
      
      // 处理跨日情况
      if (endMinutes <= startMinutes) {
        endMinutes += 24 * 60
      }
      
      const totalHours = (endMinutes - startMinutes) / 60
      return totalHours >= 1 && totalHours <= 16
    },
    message: '工作时长应在1-16小时之间'
  }),

  // 年龄范围逻辑验证
  ageRange: (): ValidationRule => ({
    validate: (value, formData) => {
      if (!formData) return false
      return formData.age_min < formData.age_max && 
             formData.age_min >= 16 && 
             formData.age_max <= 65
    },
    message: '年龄设置不合理，最小年龄16岁，最大年龄65岁'
  }),

  // 薪酬范围验证
  salaryRange: (): ValidationRule => ({
    validate: (value, formData) => {
      if (!formData?.salary_unit) return false
      const salary = Number(value)
      
      if (formData.salary_unit === 1) { // 按小时
        return salary >= 15 && salary <= 200
      } else if (formData.salary_unit === 2) { // 按天
        return salary >= 100 && salary <= 2000
      }
      return false
    },
    message: '薪酬设置超出合理范围'
  }),

  // 禁止联系方式验证
  noContact: (): ValidationRule => ({
    validate: (value) => {
      const contactPatterns = [
        /1[3-9]\d{9}/,           // 手机号
        /\d{3,4}-?\d{7,8}/,      // 座机号
        /[a-zA-Z0-9]+@[a-zA-Z0-9]+\.[a-zA-Z]{2,}/,  // 邮箱
        /微信|WeChat|QQ|qq/,      // 常见社交软件
      ]
      return !contactPatterns.some(pattern => pattern.test(String(value)))
    },
    message: '描述中不能包含联系方式信息'
  })
}

// 字段验证配置接口
interface FieldValidationConfig {
  field: string
  rules: ValidationRule[]
  optional?: boolean
}

// 字段验证配置
const validationConfig: {
  step1: FieldValidationConfig[]
  step2: FieldValidationConfig[]
} = {
  step1: [
    { field: 'title', rules: [rules.required('请填写工作标题'), rules.length(2, 20, '工作标题')] },
    { field: 'start_time', rules: [rules.required('请选择开始时间')] },
    { field: 'end_time', rules: [rules.required('请选择结束时间')] },
    { field: 'workDuration', rules: [rules.workDuration()] }, // 特殊验证，使用整个formData
    { field: 'address', rules: [rules.required('请选择工作地点')] },
    { field: 'detail_address', rules: [rules.required('请填写详细地址'), rules.length(5, 100, '详细地址')] },
    { field: 'people_count', rules: [rules.required('请设置招聘人数'), rules.range(1, 99, '招聘人数')] },
    { field: 'salary', rules: [rules.required('请设置薪酬金额'), rules.salaryRange()] },
  ],
  step2: [
    { field: 'contact_name', rules: [rules.required('请填写联系人姓名'), rules.length(2, 10, '联系人姓名')] },
    { field: 'contact_phone', rules: [rules.required('请填写联系电话'), rules.phone()] },
    { field: 'ageRange', rules: [rules.ageRange()] }, // 特殊验证
    { field: 'description', rules: [rules.length(0, 200, '工作描述'), rules.noContact()], optional: true },
  ]
}

/**
 * 执行表单验证
 * @param formData 表单数据
 * @param step 验证步骤 ('step1' | 'step2')
 * @returns 验证结果
 */
export function validateGigForm(formData: CreateGigRequest, step: 'step1' | 'step2'): boolean {
  const config = validationConfig[step]
  
  for (const fieldConfig of config) {
    const { field, rules: fieldRules, optional } = fieldConfig
    const value = field === 'workDuration' || field === 'ageRange' ? formData : formData[field as keyof CreateGigRequest]
    
    // 可选字段且为空则跳过验证
    if (optional && (!value || String(value).trim() === '')) {
      continue
    }
    
    for (const rule of fieldRules) {
      if (!rule.validate(value, formData)) {
        showToast(rule.message)
        return false
      }
    }
  }
  
  return true
}

/**
 * 获取字段显示名称
 * @param field 字段名
 * @returns 显示名称
 */
export function getFieldLabel(field: string): string {
  const labels: Record<string, string> = {
    title: '工作标题',
    start_time: '开始时间',
    end_time: '结束时间',
    address: '工作地点',
    detail_address: '详细地址',
    people_count: '招聘人数',
    salary: '薪酬金额',
    contact_name: '联系人',
    contact_phone: '联系电话'
  }
  return labels[field] || field
}

/**
 * 快速验证单个字段
 * @param field 字段名
 * @param value 字段值
 * @param formData 完整表单数据（某些验证需要）
 * @returns 验证结果
 */
export function validateField(field: string, value: any, formData?: CreateGigRequest): { valid: boolean, message?: string } {
  // 查找字段配置
  const allConfig = [...validationConfig.step1, ...validationConfig.step2]
  const fieldConfig = allConfig.find(config => config.field === field)
  
  if (!fieldConfig) {
    return { valid: true }
  }
  
  for (const rule of fieldConfig.rules) {
    if (!rule.validate(value, formData)) {
      return { valid: false, message: rule.message }
    }
  }
  
  return { valid: true }
}