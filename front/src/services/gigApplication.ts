/**
 * 零工申请相关服务
 */
import { useRequest } from "alova/client";
import GigApi from "@/api/gig";
import type {
  GigApplication,
  ApplyGigRequest,
  UpdateApplicationStatusRequest,
} from "@/types/gig";
import { useUserStore } from "@/stores/user";
import { showToast, showSuccessToast } from "@/utils/ui/feedback";
import {
  validatePhone,
  validateChineseName,
  validateLength,
} from "@/utils/core/validation";

/**
 * 申请零工的业务逻辑 - 精简版
 */
export function useGigApplication() {
  // 提交零工报名申请方法
  const {
    loading: onApplyLoading,
    error: applyError,
    send: sendApply,
  } = useRequest((params) => GigApi.apply(params), {
    immediate: false,
  })
    .onSuccess(() => {
      showSuccessToast("申请提交成功，请等待雇主确认");
    })
    .onError((error) => {
      showToast(error.error ?? "申请失败，请稍后重试");
    });

  // 撤销申请
  const {
    loading: isWithdrawing,
    error: withdrawError,
    send: withdrawApplication,
  } = useRequest(
    (applicationId: number) => GigApi.deleteApplication(applicationId),
    { immediate: false }
  )
    .onSuccess(() => {
      showSuccessToast("申请已撤销");
    })
    .onError((error) => {
      console.error("撤销申请失败:", error);
      showToast("撤销失败，请重试");
    });

  // 更新申请状态（雇主操作）
  const {
    loading: isUpdatingStatus,
    error: updateStatusError,
    send: updateApplicationStatus,
  } = useRequest(
    (request: UpdateApplicationStatusRequest) =>
      GigApi.updateApplicationStatus(request),
    { immediate: false }
  )
    .onSuccess(() => {
      showSuccessToast("操作成功");
    })
    .onError((error) => {
      console.error("更新申请状态失败:", error);
      showToast("操作失败，请重试");
    });

  return {
    // 申请相关
    onApplyLoading,
    applyError,
    sendApply,

    // 撤销相关
    isWithdrawing,
    withdrawError,
    withdrawApplication,

    // 状态更新相关（雇主用）
    isUpdatingStatus,
    updateStatusError,
    updateApplicationStatus,
  };
}

/**
 * 获取用户的申请列表
 */
export function useMyApplications() {
  const {
    data: applications,
    loading: isLoading,
    error: fetchError,
    send: refresh,
  } = useRequest(() => GigApi.getMyApplications(), { immediate: true }).onError(
    (error) => {
      console.error("获取申请列表失败:", error);
    }
  );

  return {
    applications: applications ?? ref([]),
    isLoading,
    fetchError,
    refresh,
  };
}

/**
 * 获取零工的申请列表（雇主用）
 */
export function useGigApplications(gigId: number) {
  const {
    data: applications,
    loading: isLoading,
    error: fetchError,
    send: refresh,
  } = useRequest(() => GigApi.getGigApplications(gigId), {
    immediate: true,
  }).onError((error) => {
    console.error("获取零工申请列表失败:", error);
  });

  return {
    applications: applications ?? ref([]),
    isLoading,
    fetchError,
    refresh,
  };
}

/**
 * 检查用户对特定零工的申请状态
 * @param gigId - 可以是number或ref<number>
 *
 * 注意：此函数返回的是CheckApplicationStatusResponse，包含has_applied等信息
 * 不是完整的申请对象，如需完整申请信息，请使用getUserApplicationByGigId
 */
export function checkAppStatus(gigId: Ref<number>) {
  const {
    data: statusInfo,
    loading: isLoading,
    error: fetchError,
    send: checkStatus,
  } = useRequest(() => GigApi.checkApplicationStatus(gigId.value), {
    immediate: false, // 手动控制调用时机
    cacheFor: 0, // 禁用缓存，确保每次都获取最新数据
  });

  return {
    statusInfo,
    isLoading,
    fetchError,
    checkStatus,
  };
}
