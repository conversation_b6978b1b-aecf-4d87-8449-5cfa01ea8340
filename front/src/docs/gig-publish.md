@/Users/<USER>/Workspace/fnbdb-mini/front/src/pages/gig/publish/ @/Users/<USER>/Workspace/fnbdb-mini/backend/internal/api/controller/gig_ctl.go 
@你现在以高级go语言专家、高级前端专家、高级UI设计师、经验丰富的vue工程师身份角色，根据下面的描述和图片示例进行分析与思考，完成任务：

## 零工发布页面改版重构：
- 目前发布页面第一眼感觉有很多字段需要填写，影响用户使用体验。而且整个屏幕全是表单，从产品角度来讲会劝退用户

- 没有对表单信息进行分组，减少发布首页的信息量，这会很有帮助

- 我上传了一些行业app的截图，我认为很有参考价值，你可以仔细读取并分析图片，有哪些值得学习和借鉴，并应用到当前的模块中


## 功能细节：
 - 表单中去掉经验要求的选择，而是增加照片、视频增加工作内容的了解与场地信息
- 增加标签，例如：只要熟手、中午包饭、禁止吸烟、可无经验、不磨洋工等等
- 工作日期时间变更：先选择日期，然后选择具体工作时间段，并在选择完后自动计算出工作时长；工作日期只展示未来7天，可参考图片中的方式，例如：今、明、后....，带有周几和具体日期

- 干活地点：支持保存和新增，帮助用户快速选择长期使用的地点

## 提示或者建议：
- 附件的图片中可以借鉴其中的优点，并结合你的经验让本应用中更优秀，更能方便用户使用；要取其精华，去其糟粕
- 因为有一部分功能是新增的，所以你需要读取数据表字段以及model，你才能更快速的了解业务。可能会涉及到新增/修改字段

-- 功能重构以及新增功能需要你进行分析与思考，以最佳的方案和执行计划；先谈论方案与计划，我确认后在进行开发工作


