<template>
  <PageLayout
    :loading="isLoading && filteredApplicants.length === 0"
    :error="fetchError"
    :isEmpty="!isLoading && filteredApplicants.length === 0"
    emptyType="data"
    :texts="{ empty: '暂无申请者', loading: '加载申请者中...' }"
    @retry="refresh"
  >
    <template #navbar>
      <view class="custom-navbar">
        <view class="navbar-content">
          <text class="back-btn i-carbon-chevron-left" @tap="handleBack"></text>
          <text class="navbar-title">报名者管理</text>
          <text class="menu-btn i-carbon-overflow-menu-horizontal"></text>
        </view>
      </view>
    </template>

    <!-- 工作信息头部 -->
    <GigInfoHeader
      :gig-title="gigInfo?.title"
      :salary="gigInfo?.salary"
      :salary-unit="gigInfo?.salary_unit"
      :salary-unit-text="gigInfo?.salary_unit_text"
    />

    <!-- 状态筛选 -->
    <StatusFilter
      :active-status="activeStatus"
      :applicants="allApplicants"
      @change="handleStatusChange"
    />

    <!-- 申请者列表 -->
    <scroll-view
      class="applicants-scroll"
      scroll-y
      :show-scrollbar="false"
      @scrolltolower="loadMore"
    >
      <view class="applicants-list">
        <ApplicantCard
          v-for="applicant in filteredApplicants"
          :key="applicant.id"
          :applicant="applicant"
          :is-processing="isReviewing"
          @contact="handleContact"
          @approve="handleApprove"
          @reject="handleReject"
        />

        <!-- 加载更多指示器 -->
        <view v-if="hasMore && !isLoading" class="load-more" @tap="loadMore">
          <text class="load-more-text">点击加载更多</text>
        </view>
        <view v-else-if="isLoading && filteredApplicants.length > 0" class="loading-more">
          <text class="loading-text">加载中...</text>
        </view>
      </view>
    </scroll-view>

    <!-- 拒绝原因弹窗 -->
    <RejectModal
      :show="showRejectModal"
      :applicant="currentApplicant"
      :is-submitting="isReviewing"
      @close="handleRejectModalClose"
      @confirm="handleRejectConfirm"
    />
  </PageLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad, onReady } from '@dcloudio/uni-app'
import type { GigApplication } from '@/types/gig'
import { GigApplicationStatus } from '@/constants/gig'
import { useGigApplicants } from '@/services/gig'
import { callApi } from '@/utils/network/helpers'
import { showToast } from '@/utils'
import GigApi from '@/api/gig'

// 导入组件
import PageLayout from '@/components/PageLayout.vue'
import GigInfoHeader from './components/GigInfoHeader.vue'
import StatusFilter from './components/StatusFilter.vue'
import ApplicantCard from './components/ApplicantCard.vue'
import RejectModal from './components/RejectModal.vue'

const gigId = ref<number>(0)
const activeStatus = ref<string>('all')
const showRejectModal = ref(false)
const currentApplicant = ref<GigApplication | null>(null)

// 使用现有的 Hook
const { applicants, isLoading, fetchError, isReviewing, review, refresh } = useGigApplicants(gigId)

// 模拟工作信息 - 实际应该从API获取
const gigInfo = ref({
  title: '小区快递分拣',
  salary: 180,
  salary_unit: 1,
  salary_unit_text: '小时'
})

// 计算属性
const allApplicants = computed(() => applicants.value?.list || [])

const filteredApplicants = computed(() => {
  if (activeStatus.value === 'all') {
    return allApplicants.value
  }
  return allApplicants.value.filter(app => app.status === activeStatus.value)
})

const hasMore = computed(() => {
  // 这里应该根据实际的分页信息判断
  return false
})

// 页面加载
onLoad((options) => {
  const id = Number(options?.id)
  if (id) {
    gigId.value = id
  } else {
    showToast('参数错误')
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})

onReady(() => {
  // 设置导航栏样式
  uni.setNavigationBarColor({
    frontColor: '#000000',
    backgroundColor: '#ffffff'
  })
})

// 事件处理
const handleBack = () => {
  uni.navigateBack()
}

const handleStatusChange = (status: string) => {
  activeStatus.value = status
}

const handleContact = (applicant: GigApplication) => {
  if (!applicant.applicant_phone) {
    showToast('暂无联系方式')
    return
  }
  uni.makePhoneCall({ 
    phoneNumber: applicant.applicant_phone,
    fail: () => {
      showToast('拨打电话失败')
    }
  })
}

const handleApprove = async (applicant: GigApplication) => {
  const result = await callApi(
    () => GigApi.reviewApplication({
      application_id: applicant.id,
      new_status: GigApplicationStatus.Confirmed
    }),
    {
      showLoading: true,
      loadingText: '处理中...',
      showSuccessToast: true,
      successText: '录用成功'
    }
  )
  
  if (result.success) {
    // 刷新列表
    refresh()
  }
}

const handleReject = (applicant: GigApplication) => {
  currentApplicant.value = applicant
  showRejectModal.value = true
}

const handleRejectModalClose = () => {
  showRejectModal.value = false
  currentApplicant.value = null
}

const handleRejectConfirm = async (data: { applicant: GigApplication; reason: string }) => {
  const result = await callApi(
    () => GigApi.reviewApplication({
      application_id: data.applicant.id,
      new_status: GigApplicationStatus.Rejected,
      reason: data.reason
    }),
    {
      showLoading: true,
      loadingText: '处理中...',
      showSuccessToast: true,
      successText: '已拒绝申请'
    }
  )
  
  if (result.success) {
    handleRejectModalClose()
    refresh()
  }
}

const loadMore = () => {
  if (hasMore.value && !isLoading.value) {
    // 加载更多逻辑
    console.log('加载更多')
  }
}
</script>

<style lang="scss" scoped>
// 自定义导航栏
.custom-navbar {
  background: var(--bg-card);
  padding-top: var(--status-bar-height);
  border-bottom: 1rpx solid var(--border-color);
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
}

.back-btn,
.menu-btn {
  font-size: 40rpx;
  color: var(--text-primary);
  padding: 12rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
}

// 滚动区域
.applicants-scroll {
  flex: 1;
  height: 0;
}

.applicants-list {
  padding: 24rpx;
}

// 加载更多
.load-more {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;
  
  .load-more-text {
    color: var(--text-info);
    font-size: 28rpx;
  }
}

.loading-more {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;
  
  .loading-text {
    color: var(--text-info);
    font-size: 28rpx;
  }
}
</style>