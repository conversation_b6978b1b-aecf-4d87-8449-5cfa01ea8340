<template>
  <view class="gig-info-header">
    <view class="gig-title">{{ gigTitle || '零工信息' }}</view>
    <view class="gig-salary">{{ formatSalary() }}</view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  gigTitle?: string
  salary?: number
  salaryUnit?: number
  salaryUnitText?: string
}

const props = withDefaults(defineProps<Props>(), {
  gigTitle: '',
  salary: 0,
  salaryUnit: 1,
  salaryUnitText: '小时'
})

const formatSalary = () => {
  if (!props.salary) return '面议'
  return `¥${props.salary}/${props.salaryUnitText}`
}
</script>

<style lang="scss" scoped>
.gig-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx;
  background: var(--bg-card);
  border-bottom: 1rpx solid var(--border-color);
}

.gig-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
}

.gig-salary {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--primary);
}
</style>