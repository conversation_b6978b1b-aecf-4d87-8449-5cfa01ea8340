<template>
  <view class="status-filter">
    <view
      v-for="filter in filters"
      :key="filter.value"
      class="filter-item"
      :class="{ active: activeStatus === filter.value }"
      @tap="handleFilterChange(filter.value)"
    >
      <text class="filter-text">{{ filter.label }}</text>
      <text v-if="filter.count > 0" class="filter-count">{{ filter.count }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { GigApplication } from '@/types/gig'
import { GigApplicationStatus } from '@/constants/gig'

interface Props {
  activeStatus: string
  applicants: GigApplication[]
}

interface Emits {
  (e: 'change', status: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const filters = computed(() => {
  const all = props.applicants?.length || 0
  const pending = props.applicants?.filter(app => app.status === GigApplicationStatus.Pending).length || 0
  const confirmed = props.applicants?.filter(app => app.status === GigApplicationStatus.Confirmed).length || 0
  const rejected = props.applicants?.filter(app => app.status === GigApplicationStatus.Rejected).length || 0

  return [
    { label: '全部', value: 'all', count: all },
    { label: '待处理', value: GigApplicationStatus.Pending, count: pending },
    { label: '已通过', value: GigApplicationStatus.Confirmed, count: confirmed },
    { label: '已拒绝', value: GigApplicationStatus.Rejected, count: rejected }
  ]
})

const handleFilterChange = (status: string) => {
  emit('change', status)
}
</script>

<style lang="scss" scoped>
.status-filter {
  display: flex;
  background: var(--bg-card);
  padding: 24rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.filter-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 32rpx;
  margin-right: 32rpx;
  border-radius: 48rpx;
  background: var(--bg-tag);
  transition: all 0.3s ease;
  
  &.active {
    background: var(--primary);
    
    .filter-text {
      color: white;
    }
    
    .filter-count {
      color: white;
    }
  }
  
  &:last-child {
    margin-right: 0;
  }
}

.filter-text {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
}

.filter-count {
  margin-left: 8rpx;
  font-size: 24rpx;
  font-weight: 600;
  color: var(--text-info);
}
</style>