<template>
  <view class="applicant-card">
    <!-- 申请者信息头部 -->
    <view class="applicant-header">
      <view class="applicant-info">
        <image
          :src="applicant.applicant_info?.avatar || defaultAvatar"
          class="applicant-avatar"
        />
        <view class="applicant-details">
          <view class="name-section">
            <text class="applicant-name">
              {{ applicant.applicant_info?.nickname || '匿名用户' }}
            </text>
            <text class="gender-icon" :class="getGenderIcon()"></text>
            <text class="applicant-age">{{ getAge() }}岁</text>
          </view>
          <text class="work-experience">{{ getWorkExperience() }}</text>
        </view>
      </view>
      
      <!-- 右上角时间和状态 -->
      <view class="header-right">
        <text class="apply-time">{{ getApplyTime() }}</text>
        <view
          v-if="applicant.status !== GigApplicationStatus.Pending"
          class="status-badge"
          :class="getStatusClass()"
        >
          {{ getStatusText() }}
        </view>
      </view>
    </view>

    <!-- 申请留言 -->
    <view v-if="applicant.message" class="application-message">
      {{ applicant.message }}
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn contact-btn" @click="handleContact">
        <text class="i-carbon-phone btn-icon"></text>
        联系
      </button>
      
      <template v-if="applicant.status === GigApplicationStatus.Pending">
        <button
          class="action-btn approve-btn"
          :disabled="isProcessing"
          @click="handleApprove"
        >
          通过
        </button>
        <button
          class="action-btn reject-btn"
          :disabled="isProcessing"
          @click="handleReject"
        >
          拒绝
        </button>
      </template>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { GigApplication } from '@/types/gig'
import { GigApplicationStatus } from '@/constants/gig'
import { formatDate, DateFormat } from '@/utils/core/date'
import { getGigApplicationStatusDetails } from '@/utils/business/gig'
import defaultAvatar from '@/static/images/default-avatar.png'

interface Props {
  applicant: GigApplication
  isProcessing?: boolean
}

interface Emits {
  (e: 'contact', applicant: GigApplication): void
  (e: 'approve', applicant: GigApplication): void
  (e: 'reject', applicant: GigApplication): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const getAge = () => {
  // 这里应该根据实际的用户信息计算年龄
  // 暂时返回一个示例值
  return props.applicant.applicant_info?.age || '未知'
}

const getGenderIcon = () => {
  const gender = props.applicant.applicant_info?.gender
  if (gender === 1) return 'i-carbon-male' // 男性
  if (gender === 2) return 'i-carbon-female' // 女性
  return ''
}

const getWorkExperience = () => {
  // 根据申请者信息返回工作经验描述
  return props.applicant.applicant_info?.work_experience || '2年快递经验'
}

const getApplyTime = () => {
  const now = new Date()
  const applyTime = new Date(props.applicant.created_at)
  const diffMs = now.getTime() - applyTime.getTime()
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffHours / 24)
  
  if (diffDays > 0) {
    return `${diffDays}天前`
  } else if (diffHours > 0) {
    return `${diffHours}小时前`
  } else {
    return '刚刚'
  }
}

const getStatusText = () => {
  return getGigApplicationStatusDetails(props.applicant.status, 'recruiter').text
}

const getStatusClass = () => {
  return getGigApplicationStatusDetails(props.applicant.status, 'recruiter').class
}

const handleContact = () => {
  emit('contact', props.applicant)
}

const handleApprove = () => {
  emit('approve', props.applicant)
}

const handleReject = () => {
  emit('reject', props.applicant)
}
</script>

<style lang="scss" scoped>
.applicant-card {
  background: var(--bg-card);
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.applicant-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.applicant-info {
  display: flex;
  flex: 1;
}

.applicant-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  background: var(--bg-tag);
}

.applicant-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.name-section {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.applicant-name {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-right: 12rpx;
}

.gender-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
  
  &.i-carbon-male {
    color: #2979ff;
  }
  
  &.i-carbon-female {
    color: #ff1744;
  }
}

.applicant-age {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.work-experience {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

.header-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.apply-time {
  font-size: 24rpx;
  color: var(--text-info);
}

.status-badge {
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
  
  &.status-confirmed {
    background: var(--bg-success-light);
    color: var(--text-green);
  }
  
  &.status-rejected {
    background: var(--bg-danger-light);
    color: var(--text-red);
  }
}

.application-message {
  background: var(--bg-tag);
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 24rpx;
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.5;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 32rpx;
  padding: 16rpx 28rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &:disabled {
    opacity: 0.6;
  }
}

.btn-icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}

.contact-btn {
  background: var(--bg-primary-light);
  color: var(--primary);
  flex: 1;
}

.approve-btn {
  background: var(--primary);
  color: white;
  flex: 1;
}

.reject-btn {
  background: var(--bg-danger-light);
  color: var(--text-red);
  flex: 1;
}
</style>