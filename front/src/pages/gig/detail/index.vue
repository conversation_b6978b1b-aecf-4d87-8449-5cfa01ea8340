<template>
  <view class="container">
    <PageLayout
      :loading="isLoading"
      :error="fetchError"
      :isEmpty="!gigDetail"
      :onRetry="refreshGigDetail"
    >
      <!-- 页面内容 -->
      <scroll-view
        scroll-y
        enable-flex
        class="detail-scroll-view"
        @refresherrefresh="onPullDownRefresh"
        :refresher-triggered="isLoading"
      >
        <!-- 主要信息卡片 -->
        <view class="card main-info-card">
          <view class="gig-card__header">
            <view class="title-section">
              <text class="text-title">{{ gigDetail?.title }}</text>
              <view v-if="canApply" class="gig-status gig-status--recruiting">
                <text>可报名</text>
              </view>
            </view>
            <text class="gig-text-body-md gig-text-secondary description">{{
              gigDetail?.description || ""
            }}</text>
          </view>

          <view class="salary-section mb-3 mt-2">
            <text class="salary-text text-2xl font-500 text-primary">{{
              formatGigSalary(gigDetail?.salary, gigDetail?.salary_unit, true)
            }}</text>
            <text class="gig-text-body-sm gig-text-secondary">{{
              formatDurationMinutes(gigDetail?.work_duration)
            }}</text>
          </view>

          <view class="info-grid">
            <view class="info-item">
              <text class="i-solar:calendar-linear info-icon"></text>
              <text class="gig-text-body-md gig-text-primary">{{
                formatTimeRange(gigDetail?.start_time, gigDetail?.end_time)
              }}</text>
            </view>

            <view class="info-item">
              <text class="i-solar:map-point-linear info-icon"></text>
              <text class="text-secondary"
                >{{ gigDetail?.address }}·{{ gigDetail?.distance }}km</text
              >
            </view>
          </view>
        </view>

        <!-- 任务说明卡片 -->
        <view class="card task-card">
          <view class="gig-card__header">
            <text class="gig-text-title-sm gig-text-primary">任务说明</text>
          </view>
          <view class="task-content">
            <text class="text-secondary">{{
              gigDetail?.description || "暂无任务说明"
            }}</text>
          </view>
        </view>

        <!-- 工作要求卡片 -->
        <view class="card requirements-card">
          <view class="gig-card__header">
            <view class="header-with-icon">
              <text class="i-solar:settings-linear header-icon"></text>
              <text class="gig-text-title-sm gig-text-primary">工作要求</text>
            </view>
          </view>
          <view class="gig-card__body">
            <view class="requirement-item">
              <view class="requirement-icon-wrapper">
                <text class="i-solar:gps-linear requirement-icon"></text>
              </view>
              <view class="requirement-content">
                <text class="requirement-title">GPS定位打卡</text>
                <text class="requirement-desc">确保工作地点准确性</text>
              </view>
            </view>
            <view class="requirement-item">
              <view class="requirement-icon-wrapper">
                <text
                  class="i-solar:clock-circle-linear requirement-icon"
                ></text>
              </view>
              <view class="requirement-content">
                <text class="requirement-title">24小时内核实</text>
                <text class="requirement-desc">通过后发放薪酬</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 安全提醒卡片 -->
        <view class="card safety-card">
          <view class="gig-card__header safety-header">
            <view class="header-with-icon">
              <text class="i-solar:danger-triangle-linear safety-icon"></text>
              <text class="gig-text-title-sm safety-title">安全提醒</text>
            </view>
          </view>
          <view class="gig-card__body safety-content">
            <view
              v-for="(tip, index) in safetyTips"
              :key="index"
              class="safety-item"
            >
              <view class="safety-number">
                <text class="gig-text-caption gig-text-warning">{{
                  index + 1
                }}</text>
              </view>
              <text class="gig-text-body-sm gig-text-secondary">{{ tip }}</text>
            </view>
          </view>
        </view>

        <!-- 发布者信息卡片 -->
        <view class="card publisher-card">
          <view class="gig-card__body publisher-info">
            <image
              :src="
                gigDetail.publisher?.avatar ||
                '/static/images/default-avatar.png'
              "
              class="publisher-avatar"
            />
            <view class="publisher-details">
              <view class="publisher-name-section">
                <text class="gig-text-title-sm gig-text-primary">{{
                  gigDetail.publisher?.nickname || "张先生"
                }}</text>
                <view class="gig-tag gig-tag--success">
                  <text>已认证雇主</text>
                </view>
              </view>
              <text class="gig-text-caption gig-text-tertiary"
                >发布零工146次</text
              >
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 底部操作栏 -->
      <BottomActionBar>
        <GigDetailActions
          v-if="gigDetail"
          :gig="gigDetail"
          :action-state="actionButtonState"
          :application-count="0"
          @apply="handleApply"
          @contact="handleContact"
          @withdraw="handleWithdraw"
          @manage-applications="handleManageApplications"
          @pause-resume="handlePauseResume"
          @close="handleClose"
        />
      </BottomActionBar>

      <!-- 申请零工模态框 -->
      <ApplyGigModal
        :show="showApplyModal"
        :gig="gigDetail"
        @close="showApplyModal = false"
        @success="handleApplySuccess"
      />
    </PageLayout>
  </view>
</template>

<script setup lang="ts">
import { onLoad, onPullDownRefresh } from "@dcloudio/uni-app";
import { useGigDetail } from "@/services/gig";
import { useUserStore, useGlobalStore } from "@/stores";
import { formatGigSalary } from "@/utils/business/gig";
import { formatTimeRange } from "@/utils/core/date";
import { showToast, formatDurationMinutes, navigateTo } from "@/utils";
import PageLayout from "@/components/PageLayout.vue";
import BottomActionBar from "@/components/common/BottomActionBar.vue";
import GigDetailActions from "./components/GigDetailActions.vue";
import ApplyGigModal from "./components/ApplyGigModal.vue";

const userStore = useUserStore();
const globalStore = useGlobalStore();

// 响应式地存储从 URL 获取的 gigId
const gigId = ref<number>(0);

// =================================================================
// 1. 调用核心业务 Hook，传入 gigId
//    所有数据和状态都从统一的服务层获取
// =================================================================
const {
  gigDetail,
  isLoading,
  fetchError,
  isPublisher,
  hasApplied,
  canApply,
  actionButtonState,
  applicationStatusInfo,
  refreshGigDetail,
  forceRefreshApplicationStatus,
} = useGigDetail(gigId);

// 页面状态
const showApplyModal = ref(false);

// 安全提醒列表
const safetyTips = ref([
  "首次见面请选择公共场所，避免偏僻地点",
  "收到转账要求请务必核实对方身份",
  "遇到异常情况，请立即联系平台客服",
]);

// =================================================================
// 4. 生命周期钩子
// =================================================================
onLoad((options) => {
  if (options && options.id) {
    const newGigId = parseInt(options.id, 10);
    // 清除之前的数据，避免缓存问题
    if (gigId.value !== newGigId) {
      gigId.value = newGigId;
      // 强制刷新数据
      setTimeout(() => {
        refreshGigDetail();
      }, 100);
    }
  } else {
    showToast("无效的零工ID");
  }
});

// 下拉刷新逻辑
onPullDownRefresh(async () => {
  await refreshGigDetail(); // 调用 hook 返回的刷新函数
  // 如果需要检查申请状态，也一并刷新
  if (!isPublisher.value && gigId.value > 0) {
    await forceRefreshApplicationStatus();
  }
  uni.stopPullDownRefresh();
});

/**
 * 处理申请按钮点击事件
 */
const handleApply = () => {
  showApplyModal.value = true;
};

/**
 * 处理申请成功事件 - 使用服务层统一管理
 */
async function handleApplySuccess() {
  try {
    // 刷新数据：申请状态和零工详情
    await Promise.all([
      forceRefreshApplicationStatus(), // 使用服务层的方法
      refreshGigDetail(),
    ]);
  } catch (error) {
    console.error("刷新数据失败:", error);
    // 静默失败，不影响用户体验
  }
}

/**
 * 联系雇主
 */
function handleContact() {
  const phone = gigDetail.value?.contact_phone;
  if (!phone) {
    showToast("暂无联系方式");
    return;
  }
  uni.makePhoneCall({ phoneNumber: phone });
}

/**
 * 撤销申请
 */
async function handleWithdraw() {
  // TODO: 实现撤销申请逻辑
  showToast("撤销申请功能待实现");
}

/**
 * 管理申请
 */
function handleManageApplications() {
  navigateTo(`/pages/gig/applicants?gigId=${gigId.value}`);
}

/**
 * 暂停/恢复招聘
 */
async function handlePauseResume() {
  // TODO: 实现暂停/恢复招聘逻辑
  showToast("暂停/恢复招聘功能待实现");
}

/**
 * 结束招聘
 */
async function handleClose() {
  // TODO: 实现结束招聘逻辑
  showToast("结束招聘功能待实现");
}
</script>

<style lang="scss" scoped>
@import "@/styles/gig-common.scss";

/* ==================== 滚动容器 ==================== */
.detail-scroll-view {
  flex: 1;
  padding: var(--spacing-3);
  /* 增加底部内边距，以避免内容被固定的操作栏遮挡 */
  padding-bottom: 180rpx;
  box-sizing: border-box;
}

/* ==================== 主要信息卡片 ==================== */
.main-info-card {
  margin-bottom: var(--spacing-3);

  .title-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-2);
    gap: var(--spacing-2);
  }

  .description {
    line-height: var(--line-height-large);
  }

  .salary-section {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing);
    background: var(--primary-50);
    padding: 24rpx;
    border-radius: 12rpx;
  }

  .info-grid {
    display: flex;
    flex-direction: column;
    gap: 12rpx;
  }

  .info-item {
    display: flex;
    align-items: center;
    gap: 8rpx;
    color: var(--text-secondary);
  }

  .info-icon {
    font-size: 32rpx;
    width: 32rpx;
    text-align: center;
    flex-shrink: 0;
  }
}

/* ==================== 工作要求卡片 ==================== */
.requirements-card {
  margin-bottom: var(--spacing-3);
  border: 1rpx solid var(--border-light);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .header-with-icon {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
  }

  .header-icon {
    font-size: 32rpx;
    color: var(--primary);
  }

  .requirement-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-3);
    padding: 24rpx;
    background: var(--bg-card);
    border-radius: 16rpx;
    border: 1rpx solid var(--border-light);

    &:last-child {
      margin-bottom: 0;
    }
  }

  .requirement-icon-wrapper {
    width: 64rpx;
    height: 64rpx;
    background: linear-gradient(
      135deg,
      var(--primary-light),
      var(--primary-50)
    );
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .requirement-icon {
    font-size: 28rpx;
    color: var(--primary);
  }

  .requirement-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 6rpx;
  }

  .requirement-title {
    font-size: 28rpx;
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1.2;
  }

  .requirement-desc {
    font-size: 24rpx;
    color: var(--text-secondary);
    line-height: 1.3;
  }
}

/* ==================== 安全提醒卡片 ==================== */
.safety-card {
  background: linear-gradient(135deg, #fff8f0, #fef3e2);
  border: 1rpx solid #f59e0b;
  margin-bottom: var(--spacing-3);
  box-shadow: 0 4rpx 16rpx rgba(245, 158, 11, 0.1);

  .safety-header {
    .header-with-icon {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }
  }

  .safety-icon {
    font-size: 32rpx;
    color: #f59e0b;
  }

  .safety-title {
    color: #f59e0b;
    font-weight: 600;
  }

  .safety-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .safety-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .safety-number {
    width: 32rpx;
    height: 32rpx;
    border-radius: 50%;
    background: linear-gradient(135deg, #f59e0b, #fbbf24);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-top: 2rpx;
    box-shadow: 0 2rpx 8rpx rgba(245, 158, 11, 0.3);

    text {
      color: white;
      font-size: 20rpx;
      font-weight: 700;
    }
  }
}

/* ==================== 发布者信息卡片 ==================== */
.publisher-card {
  margin-bottom: var(--spacing-3);

  .publisher-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
  }

  .publisher-avatar {
    width: 96rpx;
    height: 96rpx;
    border-radius: 50%;
    background: var(--bg-tag);
    flex-shrink: 0;
  }

  .publisher-details {
    flex: 1;
  }

  .publisher-name-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing);
  }
}
</style>
