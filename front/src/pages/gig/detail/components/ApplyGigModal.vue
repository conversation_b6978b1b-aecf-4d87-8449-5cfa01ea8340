<template>
  <!-- <uni-popup ref="popup" type="bottom" :zIndex="9999" :mask-click="false"> -->
  <tui-bottom-popup :zIndex="1002" :maskZIndex="1001" :show="show">
    <view class="apply-modal">
      <!-- 模态框头部 -->
      <view class="modal-header">
        <view class="header-left">
          <text class="modal-title">申请零工</text>
          <text class="modal-subtitle">完善信息提高通过率</text>
        </view>
        <view class="close-btn" @tap="handleClose">
          <text class="i-carbon-close"></text>
        </view>
      </view>

      <!-- 申请表单 -->
      <scroll-view class="form-scroll" scroll-y>
        <view class="form-section">
          <!-- 联系信息 -->
          <view class="form-group">
            <text class="group-title">
              <text class="required-mark">*</text>
              联系信息
            </text>

            <view class="form-item">
              <text class="item-label">姓名</text>
              <input
                v-model="formData.contact_name"
                class="form-input"
                placeholder="请输入您的真实姓名"
                placeholder-class="input-placeholder"
                maxlength="20"
              />
            </view>

            <view class="form-item">
              <text class="item-label">手机号</text>
              <input
                v-model="formData.contact_phone"
                class="form-input"
                type="number"
                placeholder="请输入手机号码"
                placeholder-class="input-placeholder"
                maxlength="11"
              />
            </view>
          </view>

          <!-- 工作经验 -->
          <view class="form-group">
            <text class="group-title">工作经验</text>
            <view class="experience-options">
              <view
                v-for="(exp, index) in experienceOptions"
                :key="index"
                class="experience-item"
                :class="{ selected: formData.has_experience === exp.value }"
                @tap="selectExperience(exp.value)"
              >
                <text class="experience-text">{{ exp.label }}</text>
                <text
                  class="experience-icon"
                  :class="
                    formData.has_experience === exp.value
                      ? 'i-carbon-checkmark-filled'
                      : 'i-carbon-radio-button'
                  "
                ></text>
              </view>
            </view>
          </view>

          <!-- 备注信息 -->
          <view class="form-group">
            <text class="group-title">备注信息</text>
            <textarea
              v-model="formData.message"
              class="form-textarea"
              placeholder="可选填写相关经验、优势等补充信息"
              placeholder-class="input-placeholder"
              maxlength="500"
            />
          </view>
        </view>
      </scroll-view>

      <!-- 底部操作区域 -->
      <view class="modal-footer">
        <view class="agreement-section">
          <view class="agreement-checkbox" @tap="toggleAgreement">
            <text
              class="checkbox-icon"
              :class="
                formData.agreed_to_terms
                  ? 'i-carbon-checkmark-filled'
                  : 'i-carbon-checkbox'
              "
            ></text>
            <text class="agreement-text">
              我已阅读并同意
              <text class="agreement-link" @tap.stop="showAgreement"
                >《申请服务协议》</text
              >
            </text>
          </view>
        </view>

        <view class="action-buttons">
          <view class="cancel-btn box-shadow" @tap="handleClose">
            <text class="btn-text">取消</text>
          </view>
          <view
            class="submit-btn box-shadow interactive-scale"
            :class="{ disabled: !canSubmit }"
            @tap="handleSubmit"
          >
            <text class="btn-text">提交申请</text>
          </view>
        </view>
      </view>
      <tui-loading
        v-show="onApplyLoading"
        text="提交中..."
        isMask
      ></tui-loading>
    </view>
    <!-- </uni-popup> -->
  </tui-bottom-popup>
</template>

<script setup lang="ts">
import tuiLoading from "@/components/thorui/tui-loading/tui-loading.vue";
import { ref, computed, watch, reactive, unref } from "vue";
import { useUserStore } from "@/stores/user";
import { useGigApplication } from "@/services/gigApplication";
import { showToast } from "@/utils";

interface Props {
  show: boolean;
  gigId: number;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  gigId: 0,
});

const emit = defineEmits<{
  close: [];
  success: []; // 提交成功事件
}>();

const userStore = useUserStore();

const defaultValue = () => ({
  contact_name: userStore.user?.real_name || "",
  contact_phone: userStore.user?.phone || "",
  has_experience: false,
  message: "",
  agreed_to_terms: false,
});

// 表单数据
let formData = reactive(defaultValue());

// 经验选项
const experienceOptions = [
  { label: "有经验", value: true },
  { label: "无经验", value: false },
];

// 监听显示状态，重置表单
watch(
  () => props.show,
  (newShow) => {
    if (newShow) {
      resetForm();
    }
  }
);

const { onApplyLoading, sendApply } = useGigApplication();

// 重置表单
const resetForm = () => {
  formData = Object.assign(unref(formData), defaultValue());
};

// 是否可以提交
const canSubmit = computed(() => {
  return (
    formData.agreed_to_terms &&
    formData.contact_name?.trim() &&
    formData.contact_phone?.trim() &&
    !onApplyLoading.value
  );
});

// 切换协议同意状态
const toggleAgreement = () => {
  formData.agreed_to_terms = !formData.agreed_to_terms;
};

// 表单交互方法
const selectExperience = (value: boolean) => {
  formData.has_experience = value;
};

// 显示协议
const showAgreement = () => {
  // TODO: 显示协议内容
  uni.showToast({ title: "协议内容待完善", icon: "none" });
};

// 处理关闭
const handleClose = () => {
  emit("close");
};

// 处理提交
const handleSubmit = async () => {
  if (!canSubmit.value) return;

  // 详细的字段验证和错误提示
  if (!formData.contact_name?.trim()) {
    uni.showToast({ title: "请输入联系人姓名", icon: "none" });
    return;
  }

  if (!formData.contact_phone?.trim()) {
    uni.showToast({ title: "请输入联系电话", icon: "none" });
    return;
  }

  // 简单验证手机号格式
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(formData.contact_phone.trim())) {
    uni.showToast({ title: "请输入正确的手机号码", icon: "none" });
    return;
  }

  if (!formData.agreed_to_terms) {
    uni.showToast({ title: "请同意申请服务协议", icon: "none" });
    return;
  }

  try {
    await sendApply({
      ...formData,
      gig_id: props.gigId,
    });
    // 申请成功后关闭模态框并触发成功事件
    emit("success");
    handleClose();
  } catch (error) {
    console.error("申请提交失败:", error);
    // 错误处理已在 useGigApplication 中处理
  }
};
</script>

<style scoped lang="scss">
.apply-modal {
  width: 100%;
  height: 70vh;
  background-color: var(--bg-card);
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 0 28rpx;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28rpx var(--spacing) var(--spacing-3);
  border-bottom: 1rpx solid var(--border-color);
}

.header-left {
  flex: 1;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;

  display: block;
  margin-bottom: var(--spacing);
}

.modal-subtitle {
  font-size: 26rpx;
  color: var(--text-info);
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: var(--bg-tag);
  transition: all 0.3s ease;

  text {
    font-size: 28rpx;
    color: var(--text-secondary);
  }
}

.form-scroll {
  flex: 1;
  height: 0; /* 配合flex:1使用，确保可滚动 */
  padding: 0 var(--spacing);
  box-sizing: border-box;
}

.form-section {
  padding-bottom: 40rpx;
}

.form-group {
  margin-bottom: 48rpx;
}

.group-title {
  font-weight: 600;
  margin-bottom: var(--spacing-3);
  display: block;
}

.required-mark {
  color: var(--text-red);
  margin-right: var(--spacing);
}

.form-item {
  display: flex;
  align-items: center;
  background-color: var(--bg-input);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: var(--spacing-2);
}

.item-label {
  width: 120rpx;
  flex-shrink: 0;
}

.form-input {
  flex: 1;
  border-radius: 16rpx;
  background-color: transparent;
  min-width: 0; /* 防止输入框撑开容器 */
}

.input-placeholder {
  color: var(--text-info);
}

.form-textarea {
  width: 100%;
  min-height: 160rpx;
  background-color: var(--bg-input);
  border-radius: 16rpx;
  padding: 24rpx var(--spacing);

  line-height: 1.5;
  box-sizing: border-box;
}

// 经验选项样式
.experience-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.experience-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-radius: 16rpx;
  background-color: var(--bg-input);
  transition: all 0.3s ease;
  box-sizing: border-box;

  &.selected {
    background-color: var(--bg-primary-light);
    border: 1rpx solid var(--primary);
  }
}

.experience-icon {
  font-size: 32rpx;
  color: var(--primary);
}

// 时间选项样式
.time-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-2);
}

.time-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3);
  background-color: var(--bg-input);
  border-radius: 16rpx;
  transition: all 0.3s ease;

  &.selected {
    background-color: var(--primary);
    color: var(--text-inverse);
  }
}

.time-text {
  font-size: 26rpx;
  color: inherit;
}

.modal-footer {
  padding: var(--spacing);
  border-top: 1rpx solid var(--border-color);
  background-color: var(--bg-card);
}

.agreement-section {
  margin: var(--spacing-2) 0;
}

.agreement-checkbox {
  display: flex;
  align-items: center;
  gap: var(--spacing);
}

.checkbox-icon {
  font-size: 32rpx;
  color: var(--primary);
}

.agreement-text {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

.agreement-link {
  color: var(--primary);
  text-decoration: underline;
}

.action-buttons {
  display: flex;
  gap: var(--spacing-3);
}

.cancel-btn,
.submit-btn {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-2);
  transition: all 0.3s ease;
}

.cancel-btn {
  flex: 1;
}

.submit-btn {
  flex: 2;
}

.cancel-btn {
  background-color: var(--bg-search);
  color: var(--text-secondary);
}

.submit-btn {
  background: linear-gradient(135deg, var(--primary-400), var(--primary));
  color: var(--text-inverse);

  &.disabled {
    opacity: 0.6;
    background: var(--bg-tag);
    color: var(--text-disable);
    box-shadow: none;
  }
}

.btn-text {
  font-weight: 500;
  font-size: 32rpx;
  color: inherit;
}
</style>
