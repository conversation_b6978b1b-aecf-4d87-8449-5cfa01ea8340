<template>
  <view class="gig-actions-container">
    <!-- 发布者状态 -->
    <template v-if="actionState === 'publisher'">
      <view class="action-group">
        <view class="primary-btn manage-btn" @tap="handleManageApplications">
          <text class="btn-text">管理申请</text>
          <text v-if="applicationCount > 0" class="count-badge">{{
            applicationCount
          }}</text>
        </view>
        <view class="secondary-btn more-btn" @tap="showMoreActions">
          <text class="i-solar:menu-dots-linear"></text>
        </view>
      </view>
    </template>

    <!-- 已申请状态 -->
    <template v-else-if="actionState === 'applied'">
      <view class="action-group applied-group">
        <view class="status-indicator">
          <text class="status-text">已申请</text>
          <text class="status-desc">等待雇主确认</text>
        </view>
        <view class="secondary-btn contact-btn" @tap="handleContact">
          <text class="btn-text">联系雇主</text>
        </view>
        <view class="tertiary-btn withdraw-btn" @tap="handleWithdraw">
          <text class="btn-text">撤销</text>
        </view>
      </view>
    </template>

    <!-- 不可申请状态 -->
    <template v-else-if="actionState === 'unavailable'">
      <view class="primary-btn disabled">
        <text class="btn-text">{{ unavailableReason }}</text>
      </view>
    </template>

    <!-- 可申请状态 -->
    <template v-else-if="actionState === 'available'">
      <view class="primary-btn apply-btn interactive-scale" @tap="handleApply">
        <text class="btn-text">立即申请</text>
      </view>
    </template>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useUserStore } from "@/stores/user";
import { useGlobalStore } from "@/stores/global";
import { GigStatus } from "@/constants/gig";
import type { Gig } from "@/types/gig";
import { showActionConfirm } from "@/utils/ui/feedback";

// 组件属性定义
interface Props {
  gig?: Gig | null;
  actionState:
    | "loading"
    | "publisher"
    | "applied"
    | "unavailable"
    | "available";
  applicationCount?: number;
}

const props = withDefaults(defineProps<Props>(), {
  gig: null,
  applicationCount: 0,
});

// 事件定义
const emit = defineEmits<{
  apply: [];
  contact: [];
  withdraw: [];
  manageApplications: [];
  pauseResume: [];
  close: [];
}>();

// Store 实例
const userStore = useUserStore();
const globalStore = useGlobalStore();

// 计算属性
const gigStatus = computed(() => props.gig?.status);

// 不可申请的原因
const unavailableReason = computed(() => {
  if (!props.gig) return "零工不存在";

  // 检查名额限制
  if (
    props.gig.people_count &&
    props.gig.current_people_count >= props.gig.people_count
  ) {
    return "名额已满";
  }

  // 检查零工状态
  if (gigStatus.value === GigStatus.Paused) {
    return "暂停招聘";
  }

  if (gigStatus.value === GigStatus.Closed) {
    return "招聘已结束";
  }

  if (gigStatus.value === GigStatus.InProgress) {
    return "工作进行中";
  }

  if (gigStatus.value === GigStatus.Completed) {
    return "工作已完成";
  }

  return "暂不可申请";
});

const handleApply = () => {
  emit("apply");
};

const handleContact = () => {
  emit("contact");
};

const handleWithdraw = async () => {
  await showActionConfirm("撤销申请", "确定要撤销此申请吗？", () => {
    emit("withdraw");
  });
};

const handleManageApplications = () => {
  emit("manageApplications");
};

const showMoreActions = () => {
  const actions: string[] = [];

  // 根据零工状态添加操作选项
  if (gigStatus.value === GigStatus.Recruiting) {
    actions.push("暂停招聘");
  } else if (gigStatus.value === GigStatus.Paused) {
    actions.push("恢复招聘");
  }

  if (
    [GigStatus.Recruiting, GigStatus.Paused, GigStatus.InProgress].includes(
      gigStatus.value
    )
  ) {
    actions.push("结束招聘");
  }

  if (actions.length === 0) return;

  uni.showActionSheet({
    itemList: actions,
    success: (res) => {
      const selectedAction = actions[res.tapIndex];

      if (selectedAction === "暂停招聘" || selectedAction === "恢复招聘") {
        handlePauseResume();
      } else if (selectedAction === "结束招聘") {
        handleClose();
      }
    },
  });
};

const handlePauseResume = async () => {
  const action = gigStatus.value === GigStatus.Paused ? "恢复" : "暂停";
  await showActionConfirm(
    `${action}招聘`,
    `确定要${action}此零工的招聘吗？`,
    () => {
      emit("pauseResume");
    }
  );
};

const handleClose = async () => {
  await showActionConfirm(
    "结束招聘",
    "结束后将无法继续招聘，确定要结束吗？",
    () => {
      emit("close");
    }
  );
};
</script>

<style lang="scss" scoped>
.gig-actions-container {
  padding: var(--spacing-4);
  background: var(--bg-card);
  border-top: 1px solid var(--border-color);
}

.action-group {
  display: flex;
  gap: var(--spacing-3);
  align-items: center;
}

.applied-group {
  .status-indicator {
    flex: 1;

    .status-text {
      display: block;
      font-size: 30rpx;
      font-weight: 500;
      color: var(--text-green);
      margin-bottom: var(--spacing);
    }

    .status-desc {
      display: block;
      font-size: 26rpx;
      color: var(--text-secondary);
    }
  }
}

// 按钮基础样式
.primary-btn,
.secondary-btn,
.tertiary-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--radius-2);
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;

  .btn-text {
    font-size: 30rpx;
  }


}

// 主要按钮
.primary-btn {
  background: var(--primary);
  color: white;
  flex: 1;

  &.disabled {
    background: var(--bg-secondary);
    color: var(--text-disabled);


  }

  .count-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--text-red);
    color: white;
    font-size: 22rpx;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
  }
}

// 次要按钮
.secondary-btn {
  background: var(--bg-secondary);
  color: var(--text-base);
  border: 1px solid var(--border-color);

  &.more-btn {
    width: 48px;
    height: 48px;
    padding: 0;

    .btn-text {
      font-size: 32rpx;
    }
  }
}

// 第三级按钮
.tertiary-btn {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);

  &.withdraw-btn {
    color: var(--text-red);
    border-color: var(--text-red);
  }
}

// 特定按钮样式
.login-btn {
  background: var(--primary);
}

.apply-btn {
  background: linear-gradient(135deg, var(--primary), var(--primary-700));
}

.manage-btn {
  background: var(--text-green);
}

.contact-btn {
  background: var(--text-blue);
  color: white;
  border: none;
}
</style>
