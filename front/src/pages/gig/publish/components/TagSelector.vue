<template>
  <view class="tag-selector">
    <!-- 按分类显示标签 -->
    <view
      v-for="(categoryTags, category) in tagsByCategory"
      :key="category"
      class="tag-category"
    >
      <text class="category-title">{{ getCategoryName(category) }}</text>
      <view class="tag-list">
        <view
          v-for="tag in categoryTags"
          :key="tag.value"
          class="tag-item"
          :class="{ 
            active: selectedTags.includes(tag.value),
            disabled: !selectedTags.includes(tag.value) && selectedTags.length >= maxTags
          }"
          @click="toggleTag(tag.value)"
        >
          <text class="tag-text">{{ tag.label }}</text>
        </view>
      </view>
    </view>

    <!-- 选中标签数量提示 -->
    <view class="tag-counter">
      <text class="counter-text">已选择 {{ selectedTags.length }}/{{ maxTags }} 个标签</text>
    </view>

    <!-- 自定义标签输入 -->
    <view class="custom-tag-section">
      <text class="section-title">自定义标签</text>
      <view class="custom-tag-input">
        <input
          v-model="customTagInput"
          placeholder="输入自定义标签，按回车添加"
          class="tag-input"
          maxlength="10"
          @confirm="addCustomTag"
        />
        <view 
          class="add-btn"
          :class="{ disabled: !customTagInput.trim() || selectedTags.length >= maxTags }"
          @click="addCustomTag"
        >
          <text class="add-text">添加</text>
        </view>
      </view>
      
      <!-- 自定义标签列表 -->
      <view v-if="customTags.length > 0" class="custom-tag-list">
        <view
          v-for="tag in customTags"
          :key="tag"
          class="custom-tag-item"
          :class="{ active: selectedTags.includes(tag) }"
          @click="toggleTag(tag)"
        >
          <text class="tag-text">{{ tag }}</text>
          <text class="remove-btn" @click.stop="removeCustomTag(tag)">×</text>
        </view>
      </view>
    </view>

    <!-- 标签使用提示 -->
    <view class="tag-tips">
      <text class="tip-text">• 选择合适的标签能帮助求职者更好地了解工作要求</text>
      <text class="tip-text">• 建议选择3-5个最相关的标签</text>
      <text class="tip-text">• 自定义标签可以更精确地描述特殊要求</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { GIG_REQUIREMENT_TAGS, GIG_TAGS_BY_CATEGORY } from '@/constants/gig'
import { showToast } from '@/utils/ui/feedback'

interface Props {
  selectedTags: string[]
}

interface Emits {
  (e: 'update:tags', tags: string[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 本地选中的标签
const selectedTags = ref<string[]>([...props.selectedTags])

// 自定义标签
const customTags = ref<string[]>([])
const customTagInput = ref('')

// 最大标签数量
const maxTags = 8

// 按分类组织的标签
const tagsByCategory = computed(() => {
  return GIG_TAGS_BY_CATEGORY
})

// 分类名称映射
const categoryNames: Record<string, string> = {
  experience: '经验要求',
  benefit: '福利待遇',
  rule: '工作要求',
  position: '工作方式',
  time: '时间安排',
  attitude: '工作态度',
  skill: '技能要求'
}

// 获取分类名称
const getCategoryName = (category: string): string => {
  return categoryNames[category] || category
}

// 切换标签选中状态
const toggleTag = (tagValue: string) => {
  const index = selectedTags.value.indexOf(tagValue)
  
  if (index > -1) {
    // 取消选中
    selectedTags.value.splice(index, 1)
  } else {
    // 选中
    if (selectedTags.value.length >= maxTags) {
      showToast(`最多只能选择${maxTags}个标签`)
      return
    }
    selectedTags.value.push(tagValue)
  }
  
  emit('update:tags', selectedTags.value)
}

// 添加自定义标签
const addCustomTag = () => {
  const tagText = customTagInput.value.trim()
  
  if (!tagText) {
    showToast('请输入标签内容')
    return
  }
  
  if (tagText.length > 10) {
    showToast('标签长度不能超过10个字符')
    return
  }
  
  if (selectedTags.value.length >= maxTags) {
    showToast(`最多只能选择${maxTags}个标签`)
    return
  }
  
  // 检查是否已存在
  const allExistingTags = [
    ...GIG_REQUIREMENT_TAGS.map(tag => tag.label),
    ...customTags.value
  ]
  
  if (allExistingTags.includes(tagText)) {
    showToast('该标签已存在')
    return
  }
  
  // 添加到自定义标签列表
  customTags.value.push(tagText)
  
  // 自动选中新添加的标签
  selectedTags.value.push(tagText)
  emit('update:tags', selectedTags.value)
  
  // 清空输入框
  customTagInput.value = ''
  
  showToast('标签添加成功')
}

// 删除自定义标签
const removeCustomTag = (tag: string) => {
  // 从自定义标签列表中删除
  const customIndex = customTags.value.indexOf(tag)
  if (customIndex > -1) {
    customTags.value.splice(customIndex, 1)
  }
  
  // 从选中标签中删除
  const selectedIndex = selectedTags.value.indexOf(tag)
  if (selectedIndex > -1) {
    selectedTags.value.splice(selectedIndex, 1)
    emit('update:tags', selectedTags.value)
  }
}
</script>

<style lang="scss" scoped>
.tag-selector {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.tag-category {
  margin-bottom: var(--spacing-4);
  
  &:last-child {
    margin-bottom: 0;
  }
}

.category-title {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-2);
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.tag-item {
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--bg-search);
  border: 2rpx solid transparent;
  border-radius: var(--radius);
  transition: all 0.3s ease;
  
  &.active {
    background-color: var(--primary);
    border-color: var(--primary);
    
    .tag-text {
      color: var(--text-inverse);
    }
  }
  
  &.disabled {
    opacity: 0.5;
    
    .tag-text {
      color: var(--text-disable);
    }
  }
  
  &:not(.disabled):active {
    transform: scale(0.95);
  }
  
  .tag-text {
    font-size: 24rpx;
    color: var(--text-secondary);
    transition: color 0.3s ease;
  }
}

.tag-counter {
  padding: var(--spacing-3);
  background-color: var(--bg-tag);
  border-radius: var(--radius);
  margin: var(--spacing-4) 0;
  text-align: center;
}

.counter-text {
  font-size: 24rpx;
  color: var(--text-info);
}

.custom-tag-section {
  border-top: 1rpx solid var(--border-color);
  padding-top: var(--spacing-4);
  margin-top: var(--spacing-4);
}

.section-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-base);
  margin-bottom: var(--spacing-3);
}

.custom-tag-input {
  display: flex;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-3);
}

.tag-input {
  flex: 1;
  height: 80rpx;
  padding: 0 16rpx;
  background-color: var(--bg-input);
  border: 2rpx solid transparent;
  border-radius: var(--radius);
  font-size: 28rpx;
  color: var(--text-base);
  transition: all 0.3s ease;
  
  &:focus {
    border-color: var(--primary);
    background-color: var(--bg-card);
  }
  
  &::placeholder {
    color: var(--text-info);
  }
}

.add-btn {
  width: 120rpx;
  height: 80rpx;
  background-color: var(--primary);
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  
  &.disabled {
    background-color: var(--bg-tag);
    
    .add-text {
      color: var(--text-disable);
    }
  }
  
  &:not(.disabled):active {
    transform: scale(0.95);
  }
  
  .add-text {
    font-size: 28rpx;
    color: var(--text-inverse);
    font-weight: 500;
  }
}

.custom-tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-3);
}

.custom-tag-item {
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--bg-warning-light);
  border: 2rpx solid var(--text-yellow);
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  transition: all 0.3s ease;
  
  &.active {
    background-color: var(--primary);
    border-color: var(--primary);
    
    .tag-text {
      color: var(--text-inverse);
    }
    
    .remove-btn {
      color: var(--text-inverse);
    }
  }
  
  .tag-text {
    font-size: 24rpx;
    color: var(--text-yellow);
  }
  
  .remove-btn {
    font-size: 32rpx;
    color: var(--text-yellow);
    font-weight: bold;
    padding: 0 8rpx;
    margin: -8rpx -4rpx -8rpx 4rpx;
    
    &:active {
      transform: scale(1.2);
    }
  }
}

.tag-tips {
  padding: var(--spacing-3);
  background-color: var(--bg-tag);
  border-radius: var(--radius);
  margin-top: var(--spacing-4);
}

.tip-text {
  display: block;
  font-size: 22rpx;
  color: var(--text-info);
  line-height: 1.4;
  margin-bottom: 4rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}
</style>