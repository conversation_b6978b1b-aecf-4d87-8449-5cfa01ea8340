<template>
  <view class="datetime-selector">
    <!-- 日期选择 -->
    <view class="date-section">
      <text class="section-label">选择日期</text>
      <view class="date-options">
        <view
          v-for="dateOption in dateOptions"
          :key="dateOption.date"
          class="date-option"
          :class="{ active: selectedDate === dateOption.date }"
          @click="selectDate(dateOption.date)"
        >
          <text class="date-label">{{ dateOption.label }}</text>
          <text class="date-value">{{ dateOption.dateText }}</text>
        </view>
      </view>
    </view>

    <!-- 时间范围选择 -->
    <view class="time-section">
      <text class="section-label">工作时间</text>
      
      <!-- 开始时间 -->
      <view class="time-row">
        <text class="time-label">开始时间</text>
        <picker
          mode="time"
          :value="startTime"
          @change="onStartTimeChange"
        >
          <view class="time-picker">
            <text class="time-value">{{ startTime || '请选择' }}</text>
            <text class="picker-arrow">›</text>
          </view>
        </picker>
      </view>

      <!-- 结束时间 -->
      <view class="time-row">
        <text class="time-label">结束时间</text>
        <picker
          mode="time"
          :value="endTime"
          @change="onEndTimeChange"
        >
          <view class="time-picker">
            <text class="time-value">{{ endTime || '请选择' }}</text>
            <text class="picker-arrow">›</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 工作时长显示 -->
    <view v-if="workDuration" class="duration-display">
      <text class="duration-text">预计工作时长: {{ workDuration }}</text>
    </view>

    <!-- 快捷时间选项 -->
    <view class="quick-time-section">
      <text class="section-label">常用时间</text>
      <view class="quick-time-options">
        <view
          v-for="option in quickTimeOptions"
          :key="option.label"
          class="quick-time-option"
          @click="selectQuickTime(option)"
        >
          <text>{{ option.label }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { formatDate, DateFormat } from '@/utils/core/date'

interface Props {
  startTime: string
  endTime: string
}

interface Emits {
  (e: 'update:startTime', time: string): void
  (e: 'update:endTime', time: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 选中的日期
const selectedDate = ref('')
const startTime = ref(props.startTime.split(' ')[1] || '')
const endTime = ref(props.endTime.split(' ')[1] || '')

// 生成接下来7天的日期选项
const dateOptions = computed(() => {
  const options = []
  const today = new Date()
  
  for (let i = 0; i < 7; i++) {
    const date = new Date(today)
    date.setDate(today.getDate() + i)
    
    const dateStr = formatDate(date, DateFormat.DATE_ONLY)
    let label = ''
    
    if (i === 0) {
      label = '今天'
    } else if (i === 1) {
      label = '明天'
    } else {
      const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      label = weekDays[date.getDay()]
    }
    
    options.push({
      date: dateStr,
      label,
      dateText: formatDate(date, DateFormat.MONTH_DAY)
    })
  }
  
  return options
})

// 快捷时间选项
const quickTimeOptions = [
  { label: '上午班 9:00-12:00', start: '09:00', end: '12:00' },
  { label: '下午班 14:00-18:00', start: '14:00', end: '18:00' },
  { label: '全天班 9:00-18:00', start: '09:00', end: '18:00' },
  { label: '晚班 18:00-22:00', start: '18:00', end: '22:00' },
  { label: '夜班 22:00-06:00', start: '22:00', end: '06:00' }
]

// 计算工作时长
const workDuration = computed(() => {
  if (!startTime.value || !endTime.value) return ''
  
  const [startHour, startMin] = startTime.value.split(':').map(Number)
  const [endHour, endMin] = endTime.value.split(':').map(Number)
  
  let startMinutes = startHour * 60 + startMin
  let endMinutes = endHour * 60 + endMin
  
  // 处理跨日情况（如夜班）
  if (endMinutes <= startMinutes) {
    endMinutes += 24 * 60
  }
  
  const durationMinutes = endMinutes - startMinutes
  const hours = Math.floor(durationMinutes / 60)
  const minutes = durationMinutes % 60
  
  if (hours > 0 && minutes > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (hours > 0) {
    return `${hours}小时`
  } else {
    return `${minutes}分钟`
  }
})

// 初始化选中日期
onMounted(() => {
  if (props.startTime) {
    selectedDate.value = props.startTime.split(' ')[0]
  } else {
    // 默认选择今天
    selectedDate.value = formatDate(new Date(), DateFormat.DATE_ONLY)
  }
})

// 监听props变化
watch(
  () => props.startTime,
  (newVal) => {
    if (newVal) {
      const [date, time] = newVal.split(' ')
      selectedDate.value = date
      startTime.value = time
    }
  }
)

watch(
  () => props.endTime,
  (newVal) => {
    if (newVal) {
      endTime.value = newVal.split(' ')[1] || ''
    }
  }
)

// 选择日期
const selectDate = (date: string) => {
  selectedDate.value = date
  emitDateTime()
}

// 开始时间变化
const onStartTimeChange = (e: any) => {
  startTime.value = e.detail.value
  emitDateTime()
}

// 结束时间变化
const onEndTimeChange = (e: any) => {
  endTime.value = e.detail.value
  emitDateTime()
}

// 选择快捷时间
const selectQuickTime = (option: { start: string; end: string }) => {
  startTime.value = option.start
  endTime.value = option.end
  emitDateTime()
}

// 发送完整的日期时间到父组件
const emitDateTime = () => {
  if (selectedDate.value && startTime.value) {
    emit('update:startTime', `${selectedDate.value} ${startTime.value}:00`)
  }
  if (selectedDate.value && endTime.value) {
    emit('update:endTime', `${selectedDate.value} ${endTime.value}:00`)
  }
}
</script>

<style lang="scss" scoped>
.datetime-selector {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.section-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-base);
  margin-bottom: var(--spacing-3);
}

.date-section {
  padding: var(--spacing-4);
  border-bottom: 1rpx solid var(--border-color);
}

.date-options {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

.date-option {
  flex: 1;
  min-width: 0;
  padding: var(--spacing-3);
  background-color: var(--bg-search);
  border-radius: var(--radius);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
  transition: all 0.3s ease;
  
  &.active {
    background-color: var(--primary);
    color: var(--text-inverse);
    
    .date-label,
    .date-value {
      color: var(--text-inverse);
    }
  }
  
  .date-label {
    font-size: 24rpx;
    color: var(--text-info);
    font-weight: 500;
  }
  
  .date-value {
    font-size: 26rpx;
    color: var(--text-base);
  }
}

.time-section {
  padding: var(--spacing-4);
  border-bottom: 1rpx solid var(--border-color);
}

.time-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-3);
  
  &:last-child {
    margin-bottom: 0;
  }
}

.time-label {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.time-picker {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--bg-search);
  border-radius: var(--radius);
  min-width: 200rpx;
  justify-content: space-between;
}

.time-value {
  font-size: 28rpx;
  color: var(--text-base);
}

.picker-arrow {
  font-size: 32rpx;
  color: var(--text-info);
  transform: rotate(90deg);
}

.duration-display {
  padding: var(--spacing-3) var(--spacing-4);
  background-color: var(--bg-primary-light);
  border-left: 4rpx solid var(--primary);
}

.duration-text {
  font-size: 26rpx;
  color: var(--primary);
  font-weight: 500;
}

.quick-time-section {
  padding: var(--spacing-4);
}

.quick-time-options {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.quick-time-option {
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--bg-search);
  border-radius: var(--radius);
  font-size: 24rpx;
  color: var(--text-secondary);
  transition: all 0.3s ease;
  
  &:active {
    background-color: var(--primary);
    color: var(--text-inverse);
  }
}
</style>