<template>
  <view class="card">
    <view class="section-title">
      <text>联系方式</text>
    </view>

    <form-input
      prop="contact_name"
      label="联系人"
      :asterisk="true"
      v-model="localFormData.contact_name"
      placeholder="请输入联系人姓名"
    />

    <form-input
      prop="contact_phone"
      label="联系电话"
      :asterisk="true"
      v-model="localFormData.contact_phone"
      placeholder="请输入联系电话"
    />
  </view>
</template>

<script setup lang="ts">
import { toRef } from 'vue';
import FormInput from "@/components/common/FormInput.vue";
import type { CreateGigRequest } from "@/types/gig";

const props = defineProps<{ formData: CreateGigRequest }>();
const localFormData = toRef(props, 'formData');
</script>

<style lang="scss" scoped>
.card {
  margin-bottom: 24rpx;
}
.section-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: 600;
  padding: 16rpx;
}
</style>
