<template>
  <view class="card">
    <view class="section-title">招聘要求</view>
    <form-input
      prop="gender_age"
      label="性别年龄"
      :arrow="true"
      :asterisk="true"
      :bottomBorder="false"
    >
      <view class="gender-age-container" @click="showGenderAgeModal">
        <text
          class="gender-age-text"
          :class="{ placeholder: !genderAgeDisplayText }"
        >
          {{ genderAgeDisplayText || "请选择性别年龄要求" }}
        </text>
      </view>
    </form-input>

    <form-input prop="education" label="学历要求" :arrow="true">
      <picker
        :value="educationPicker.selectedIndex.value"
        :range="educationPicker.pickerRange.value"
        @change="educationPicker.onPickerChange"
      >
        <view
          class="picker-input"
          :class="{ placeholder: !localFormData.education }"
          >{{ educationPicker.selectedText.value || "请选择学历要求" }}</view
        >
      </picker>
    </form-input>
    <form-input
      prop="experience"
      label="工作经验"
      :arrow="true"
      class="interactive-scale"
    >
      <picker
        :value="experiencePicker.selectedIndex.value"
        :range="experiencePicker.pickerRange.value"
        @change="experiencePicker.onPickerChange"
      >
        <view
          class="picker-input"
          :class="{ placeholder: !localFormData.experience }"
        >
          {{ experiencePicker.selectedText.value || "请选择工作经验" }}
        </view>
      </picker>
    </form-input>

    <form-input
      prop="approval_mode"
      label="审核方式"
      :asterisk="true"
      :arrow="true"
    >
      <picker
        :value="approvalModePicker.selectedIndex.value"
        :range="approvalModePicker.pickerRange.value"
        @change="approvalModePicker.onPickerChange"
      >
        <view
          class="picker-input"
          :class="{ placeholder: !approvalModePicker.selectedText.value }"
        >
          {{ approvalModePicker.selectedText.value || "请选择审核方式" }}
        </view>
      </picker>
    </form-input>

    <view v-if="localFormData.approval_mode" class="option-description">
      <text class="description-text">{{ approvalModeDescription }}</text>
    </view>

    <form-input
      prop="check_in_method"
      label="打卡方式"
      :asterisk="true"
      :arrow="true"
      class="interactive-scale"
    >
      <picker
        :value="checkInMethodPicker.selectedIndex.value"
        :range="checkInMethodPicker.pickerRange.value"
        @change="checkInMethodPicker.onPickerChange"
      >
        <view
          class="picker-input"
          :class="{ placeholder: !checkInMethodPicker.selectedText.value }"
        >
          {{ checkInMethodPicker.selectedText.value || "请选择打卡方式" }}
        </view>
      </picker>
    </form-input>

    <view
      v-if="localFormData.check_in_method"
      class="option-description"
      :bottomBorder="false"
    >
      <text class="description-text">{{ checkInMethodDescription }}</text>
    </view>

    <GenderAgeSelector
      :show="showGenderAgeSelector"
      :gender="localFormData.gender"
      :min-age="localFormData.age_min"
      :max-age="localFormData.age_max"
      @close="closeGenderAgeModal"
      @confirm="onGenderAgeConfirm"
    />
  </view>
</template>

<script setup lang="ts">
import FormInput from "@/components/common/FormInput.vue";
import GenderAgeSelector from "@/components/gig/GenderAgeSelector.vue";
import { usePicker } from "@/hooks/usePicker";
import { GIG_APPROVAL_MODE_OPTIONS, GIG_CHECKIN_METHOD_OPTIONS } from "@/constants/gig";
import { genderOptions, educationOptions, experienceOptions } from "@/constants/standards";
import type { CreateGigRequest } from "@/types/gig";

const props = defineProps<{ formData: CreateGigRequest }>();
const localFormData = toRef(props, 'formData');

const showGenderAgeSelector = ref(false);

const educationPicker = usePicker(educationOptions, localFormData, 'education');
const experiencePicker = usePicker(experienceOptions, localFormData, 'experience');
const approvalModePicker = usePicker(GIG_APPROVAL_MODE_OPTIONS, localFormData, 'approval_mode');
const checkInMethodPicker = usePicker(GIG_CHECKIN_METHOD_OPTIONS, localFormData, 'check_in_method');

const genderAgeDisplayText = computed(() => {
  const genderText =
    genderOptions?.find((g) => g.code === localFormData.value.gender)?.label || "不限";
  const ageText =
    localFormData.value.age_min === 16 && localFormData.value.age_max === 65
      ? "不限年龄"
      : `${localFormData.value.age_min}-${localFormData.value.age_max}岁`;
  return `${genderText}，${ageText}`;
});

const approvalModeDescription = computed(() => {
  switch (localFormData.value.approval_mode) {
    case "manual":
      return "💭 手动审核：您需要逐一审核求职者的申请，可以查看简历和申请信息后决定是否录用";
    case "auto":
      return "⚡ 自动录用：系统将自动通过所有申请，求职者申请后即可参与工作，适合紧急招聘";
    default:
      return "";
  }
});

const checkInMethodDescription = computed(() => {
  switch (localFormData.value.check_in_method) {
    case "none":
      return "🚫 无需打卡：不进行考勤管理，适合灵活性较高的工作";
    case "gps":
      return "📍 GPS打卡：求职者需要在工作地点附近签到，系统会验证位置信息";
    case "qrcode":
      return "📱 二维码打卡：您提供二维码，求职者扫码签到，适合固定工作场所";
    default:
      return "";
  }
});

const showGenderAgeModal = () => {
  showGenderAgeSelector.value = true;
};

const closeGenderAgeModal = () => {
  showGenderAgeSelector.value = false;
};

const onGenderAgeConfirm = (gender: number, minAge: number, maxAge: number) => {
  localFormData.value.gender = gender;
  localFormData.value.age_min = minAge;
  localFormData.value.age_max = maxAge;
};
</script>

<style lang="scss" scoped>
.card {
  margin-bottom: 24rpx;
}
.section-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: 600;
  padding: 16rpx;
}
.picker-input {
  height: 100%;
  display: flex;
  align-items: center;
  text-align: right;
  width: 100%;
  padding-right: 8rpx;
  &.placeholder {
    color: var(--text-grey);
  }
}
.gender-age-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 8rpx 0;
  .gender-age-text {
    &.placeholder {
      color: var(--text-grey);
    }
  }
  .arrow-icon {
    color: var(--text-info);
    font-size: 24rpx;
  }
}
.option-description {
  margin: 16rpx 32rpx;
  padding: 24rpx;
  background-color: var(--bg-input);
  border-radius: var(--radius-2);
  border-left: 6rpx solid var(--primary);
  .description-text {
    font-size: 26rpx;
    color: var(--text-info);
    line-height: var(--line-height-large);
  }
}
</style>
