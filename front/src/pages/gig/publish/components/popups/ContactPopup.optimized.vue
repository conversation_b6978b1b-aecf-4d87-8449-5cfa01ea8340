<!--
  优化后的联系人弹窗组件
  使用PopupBase基础组件，大幅减少重复代码
-->
<template>
  <PopupBase 
    :show="true" 
    title="联系电话" 
    :height="700"
    @close="$emit('close')"
    @confirm="confirmSelection"
  >
    <!-- 报名接单联系人 -->
    <view class="contact-section">
      <view class="section-header">
        <text class="section-title">报名接单联系人</text>
        <text class="section-subtitle">零工接单时按打的电话，方便沟通面试</text>
      </view>
      
      <ContactItem 
        :contact="applicationContact"
        @add="showAddApplicationContact"
      />
    </view>

    <!-- 现场开工联系人 -->
    <view class="contact-section">
      <view class="section-header">
        <text class="section-title">现场开工联系人</text>
        <text class="section-subtitle">零工到现场后问路，工作安排</text>
      </view>
      
      <ContactItem 
        :contact="workContact"
        @add="showAddWorkContact"
      />
    </view>
  </PopupBase>
</template>

<script setup lang="ts">
import PopupBase from '@/components/common/PopupBase.vue'
import ContactItem from './ContactItem.vue' // 提取的联系人项组件

interface Contact {
  name: string
  phone: string
  tag: string
  active: boolean
}

interface Props {
  contactName?: string
  contactPhone?: string
}

interface Emits {
  (e: 'close'): void
  (e: 'confirm', data: { contactName: string, contactPhone: string }): void
}

const props = withDefaults(defineProps<Props>(), {
  contactName: '',
  contactPhone: ''
})

const emit = defineEmits<Emits>()

// 联系人数据
const applicationContact = reactive<Contact>({
  name: '我',
  phone: props.contactPhone || '13126606919',
  tag: '优先联系',
  active: true
})

const workContact = reactive<Contact>({
  name: '我',
  phone: props.contactPhone || '13126606919', 
  tag: '优先联系',
  active: true
})

// 初始化
onMounted(() => {
  if (props.contactPhone) {
    applicationContact.phone = props.contactPhone
    workContact.phone = props.contactPhone
  }
  if (props.contactName) {
    applicationContact.name = props.contactName
    workContact.name = props.contactName
  }
})

// 添加联系人方法（简化版）
const showAddApplicationContact = () => {
  uni.showToast({
    title: '此功能暂未开放',
    icon: 'none'
  })
}

const showAddWorkContact = () => {
  uni.showToast({
    title: '此功能暂未开放',
    icon: 'none'
  })
}

// 确认选择
const confirmSelection = () => {
  emit('confirm', {
    contactName: applicationContact.name,
    contactPhone: applicationContact.phone
  })
}
</script>

<style lang="scss" scoped>
.contact-section {
  margin-bottom: var(--spacing-6);
}

.section-header {
  padding: var(--spacing-4) var(--spacing-4) var(--spacing-3);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-base);
  display: block;
  margin-bottom: var(--spacing-1);
}

.section-subtitle {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}
</style>