<template>
  <view class="popup-mask" @click="$emit('close')">
    <view class="popup-container show" @click.stop>
      <view class="popup-header">
        <text class="popup-title">工作时间</text>
        <view class="popup-close" @click="$emit('close')">×</view>
      </view>
      
      <view class="popup-content">
        <DateTimeSelector
          :start-time="selectedStartTime"
          :end-time="selectedEndTime"
          @update:start-time="onStartTimeChange"
          @update:end-time="onEndTimeChange"
        />
      </view>
      
      <view class="popup-footer">
        <view class="popup-btn" @click="confirmSelection">
          确定
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import DateTimeSelector from '../DateTimeSelector.vue'

interface Props {
  startTime?: string
  endTime?: string
}

interface Emits {
  (e: 'close'): void
  (e: 'confirm', data: { startTime: string, endTime: string }): void
}

const props = withDefaults(defineProps<Props>(), {
  startTime: '',
  endTime: ''
})

const emit = defineEmits<Emits>()

// 选中的时间
const selectedStartTime = ref(props.startTime)
const selectedEndTime = ref(props.endTime)

// 时间变化处理
const onStartTimeChange = (time: string) => {
  selectedStartTime.value = time
}

const onEndTimeChange = (time: string) => {
  selectedEndTime.value = time
}

// 确认选择
const confirmSelection = () => {
  if (!selectedStartTime.value || !selectedEndTime.value) {
    uni.showToast({
      title: '请选择完整的工作时间',
      icon: 'none'
    })
    return
  }
  
  emit('confirm', {
    startTime: selectedStartTime.value,
    endTime: selectedEndTime.value
  })
}
</script>

<style lang="scss" scoped>
// 样式继承自popup基础样式
</style>