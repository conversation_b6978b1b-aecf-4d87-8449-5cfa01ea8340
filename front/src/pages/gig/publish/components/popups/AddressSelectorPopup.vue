<template>
  <tui-bottom-popup :show="true" @close="$emit('close')" :height="900">
    <view class="popup-container">
      <view class="popup-header">
        <text class="popup-title">选择工作地点</text>
        <view class="popup-close" @click="$emit('close')">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="popup-content">
        <!-- 当前位置 -->
        <view class="current-location-section">
          <view 
            class="location-option primary-option"
            @click="useCurrentLocation"
          >
            <view class="option-icon">
              <text class="icon-text">📍</text>
            </view>
            <view class="location-info">
              <text class="location-title">使用当前位置</text>
              <text class="location-desc">自动获取您当前的位置信息</text>
            </view>
            <text class="option-arrow">›</text>
          </view>
        </view>

        <!-- 地址输入 -->
        <view class="address-input-section">
          <text class="section-label">或手动输入地址</text>
          
          <!-- 地址名称 -->
          <view class="input-group">
            <text class="input-label">地点名称</text>
            <input
              v-model="localAddressName"
              placeholder="如：金田影视传媒产业园-12号楼"
              class="form-input"
              :maxlength="50"
            />
          </view>

          <!-- 详细地址 -->
          <view class="input-group">
            <text class="input-label">详细地址</text>
            <textarea
              v-model="localDetailAddress"
              placeholder="请输入详细地址，如：北京市朝阳区金田影视传媒产业园1单元1门"
              class="form-textarea"
              :maxlength="200"
            />
          </view>

          <!-- 地图选点 -->
          <view class="map-selector-section">
            <view 
              class="map-btn"
              @click="openMapSelector"
            >
              <view class="map-btn-info">
                <view class="map-icon">
                  <text class="icon-text">🗺️</text>
                </view>
                <view class="map-text-info">
                  <text class="map-btn-title">在地图上选择</text>
                  <text class="map-btn-desc">精准定位工作地点</text>
                </view>
              </view>
              <text class="map-arrow">›</text>
            </view>
            
            <!-- 已选位置显示 -->
            <view v-if="selectedLocation" class="selected-location">
              <view class="selected-info">
                <view class="selected-icon">
                  <text class="icon-text">✅</text>
                </view>
                <view class="selected-content">
                  <text class="location-text">{{ selectedLocation.address }}</text>
                  <text class="coordinates">
                    经度: {{ selectedLocation.longitude.toFixed(6) }}, 纬度: {{ selectedLocation.latitude.toFixed(6) }}
                  </text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 历史地址 -->
        <view v-if="recentAddresses.length > 0" class="recent-addresses-section">
          <text class="section-label">最近使用的地址</text>
          <view class="recent-list">
            <view
              v-for="(address, index) in recentAddresses"
              :key="index"
              class="recent-item"
              @click="selectRecentAddress(address)"
            >
              <view class="recent-icon">
                <text class="icon-text">📍</text>
              </view>
              <view class="recent-content">
                <text class="address-name">{{ address.name }}</text>
                <text class="address-detail">{{ address.detail }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <view class="popup-footer">
        <tui-button
          type="primary"
          width="100%"
          height="88rpx"
          :disabled="!canConfirm"
          @click="confirmSelection"
        >
          确定
        </tui-button>
      </view>
    </view>
  </tui-bottom-popup>
</template>

<script setup lang="ts">
import TuiBottomPopup from '@/components/thorui/tui-bottom-popup/tui-bottom-popup.vue'

interface Props {
  address?: string
  detailAddress?: string
  addressName?: string
  latitude?: number
  longitude?: number
}

interface Emits {
  (e: 'close'): void
  (e: 'confirm', data: { 
    address: string
    detailAddress?: string
    addressName?: string
    latitude: number
    longitude: number
  }): void
}

const props = withDefaults(defineProps<Props>(), {
  address: '',
  detailAddress: '',
  addressName: '',
  latitude: 0,
  longitude: 0
})

const emit = defineEmits<Emits>()

// 本地状态
const localAddressName = ref(props.addressName || '')
const localDetailAddress = ref(props.detailAddress || '')
const selectedLocation = ref<{ 
  address: string
  latitude: number
  longitude: number
} | null>(null)

// 模拟历史地址数据
const recentAddresses = ref([
  {
    name: '金田影视传媒产业园-12号楼',
    detail: '北京市朝阳区金田影视传媒产业园1单元1门',
    latitude: 39.9042,
    longitude: 116.4074
  },
  {
    name: '中关村软件园',
    detail: '北京市海淀区中关村软件园二期',
    latitude: 40.0275,
    longitude: 116.2671
  }
])

// 初始化选中位置
onMounted(() => {
  if (props.latitude && props.longitude) {
    selectedLocation.value = {
      address: props.address,
      latitude: props.latitude,
      longitude: props.longitude
    }
  }
})

// 是否可以确认
const canConfirm = computed(() => {
  return (localAddressName.value && localDetailAddress.value) || selectedLocation.value
})

// 使用当前位置
const useCurrentLocation = async () => {
  try {
    uni.showLoading({ title: '获取位置中...' })
    
    // 获取当前位置
    const location = await new Promise<any>((resolve, reject) => {
      uni.getLocation({
        type: 'gcj02',
        success: resolve,
        fail: reject
      })
    })
    
    // 逆地理编码获取地址信息
    // 这里应该调用地图API获取地址，暂时使用模拟数据
    selectedLocation.value = {
      address: '北京市朝阳区金田影视传媒产业园1单元1门',
      latitude: location.latitude,
      longitude: location.longitude
    }
    
    localAddressName.value = '金田影视传媒产业园-12号楼'
    localDetailAddress.value = '北京市朝阳区金田影视传媒产业园1单元1门'
    
    uni.hideLoading()
    uni.showToast({
      title: '获取位置成功',
      icon: 'success'
    })
    
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: '获取位置失败',
      icon: 'none'
    })
  }
}

// 打开地图选择器
const openMapSelector = () => {
  // 这里应该打开地图选择页面
  uni.showToast({
    title: '地图选择功能开发中',
    icon: 'none'
  })
}

// 选择历史地址
const selectRecentAddress = (address: any) => {
  localAddressName.value = address.name
  localDetailAddress.value = address.detail
  selectedLocation.value = {
    address: address.detail,
    latitude: address.latitude,
    longitude: address.longitude
  }
}

// 确认选择
const confirmSelection = () => {
  if (!canConfirm.value) {
    uni.showToast({
      title: '请选择或输入地址信息',
      icon: 'none'
    })
    return
  }
  
  const result = {
    address: selectedLocation.value?.address || localDetailAddress.value,
    detailAddress: localDetailAddress.value,
    addressName: localAddressName.value,
    latitude: selectedLocation.value?.latitude || 39.9042,
    longitude: selectedLocation.value?.longitude || 116.4074
  }
  
  emit('confirm', result)
}
</script>

<style lang="scss" scoped>
.popup-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--bg-card);
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4);
  border-bottom: 2rpx solid var(--border-light);
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-base);
}

.popup-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--bg-search);
}

.close-icon {
  font-size: 36rpx;
  color: var(--text-secondary);
  font-weight: 300;
}

.popup-content {
  flex: 1;
  overflow-y: auto;
  background: var(--bg-page);
}

.current-location-section {
  padding: var(--spacing-4);
  padding-bottom: var(--spacing-6);
}

.location-option {
  display: flex;
  align-items: center;
  padding: var(--spacing-4);
  background: var(--bg-card);
  border-radius: var(--radius-3);
  border: 2rpx solid var(--border-light);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  
  &.primary-option {
    background: var(--bg-primary-light);
    border-color: var(--primary);
  }
  
  &:active {
    transform: scale(0.98);
    background: var(--bg-search);
  }
}

.option-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-search);
  border-radius: var(--radius-2);
  margin-right: var(--spacing-3);
}

.icon-text {
  font-size: 32rpx;
}

.location-info {
  flex: 1;
}

.location-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--primary);
  display: block;
  margin-bottom: 6rpx;
}

.location-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

.option-arrow {
  font-size: 28rpx;
  color: var(--primary);
  margin-left: var(--spacing-2);
}

.address-input-section {
  padding: 0 var(--spacing-4) var(--spacing-6);
}

.section-label {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-base);
  display: block;
  margin-bottom: var(--spacing-4);
}

.input-group {
  margin-bottom: var(--spacing-4);
}

.input-label {
  font-size: 28rpx;
  color: var(--text-secondary);
  display: block;
  margin-bottom: var(--spacing-2);
}

.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 var(--spacing-3);
  background: var(--bg-search);
  border: 2rpx solid var(--border-light);
  border-radius: var(--radius-2);
  font-size: 28rpx;
  color: var(--text-base);
  transition: all 0.3s ease;

  &:focus {
    border-color: var(--primary);
    background: var(--bg-card);
  }

  &::placeholder {
    color: var(--text-grey);
  }
}

.form-textarea {
  width: 100%;
  min-height: 160rpx;
  padding: var(--spacing-3);
  background: var(--bg-search);
  border: 2rpx solid var(--border-light);
  border-radius: var(--radius-2);
  font-size: 28rpx;
  color: var(--text-base);
  line-height: 1.5;
  resize: none;

  &:focus {
    border-color: var(--primary);
    background: var(--bg-card);
  }

  &::placeholder {
    color: var(--text-grey);
  }
}

.map-selector-section {
  margin-top: var(--spacing-4);
}

.map-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4);
  background: var(--bg-card);
  border: 2rpx solid var(--border-light);
  border-radius: var(--radius-3);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
    background: var(--bg-search);
  }
}

.map-btn-info {
  display: flex;
  align-items: center;
}

.map-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-search);
  border-radius: var(--radius-2);
  margin-right: var(--spacing-3);
}

.map-text-info {
  display: flex;
  flex-direction: column;
}

.map-btn-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-base);
  margin-bottom: 6rpx;
}

.map-btn-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
}

.map-arrow {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.selected-location {
  margin-top: var(--spacing-3);
  padding: var(--spacing-3);
  background: var(--bg-success-light);
  border: 2rpx solid var(--border-success);
  border-radius: var(--radius-2);
}

.selected-info {
  display: flex;
  align-items: flex-start;
}

.selected-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-2);
  margin-top: 4rpx;
}

.selected-content {
  flex: 1;
}

.location-text {
  font-size: 28rpx;
  color: var(--text-success);
  font-weight: 500;
  display: block;
  margin-bottom: 6rpx;
}

.coordinates {
  font-size: 24rpx;
  color: var(--text-success);
  opacity: 0.8;
}

.recent-addresses-section {
  padding: 0 var(--spacing-4) var(--spacing-6);
}

.recent-list {
  margin-top: var(--spacing-3);
}

.recent-item {
  display: flex;
  align-items: flex-start;
  padding: var(--spacing-3);
  margin-bottom: var(--spacing-3);
  background: var(--bg-card);
  border: 2rpx solid var(--border-light);
  border-radius: var(--radius-3);
  transition: all 0.3s ease;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  &:active {
    transform: scale(0.98);
    background: var(--bg-search);
  }
}

.recent-icon {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-search);
  border-radius: var(--radius-2);
  margin-right: var(--spacing-3);
  margin-top: 4rpx;
}

.recent-content {
  flex: 1;
}

.address-name {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-base);
  display: block;
  margin-bottom: 6rpx;
}

.address-detail {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

.popup-footer {
  padding: var(--spacing-4);
  border-top: 2rpx solid var(--border-light);
  background: var(--bg-card);
}
</style>