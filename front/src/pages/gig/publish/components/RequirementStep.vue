<template>
  <view class="requirement-step">
    <!-- 招聘要求 -->
    <view class="form-section">
      <view class="section-header">
        <text class="section-title">招聘要求</text>
      </view>

      <!-- 性别要求 -->
      <view class="requirement-row">
        <text class="requirement-label">性别要求</text>
        <picker
          mode="selector"
          :range="genderOptions"
          range-key="label"
          :value="genderIndex"
          @change="onGenderChange"
        >
          <view class="requirement-value">
            <text>{{ currentGender.label }}</text>
            <text class="picker-arrow">›</text>
          </view>
        </picker>
      </view>

      <!-- 年龄范围 -->
      <view class="requirement-row">
        <text class="requirement-label">年龄范围</text>
        <view class="age-range-selector">
          <input
            v-model.number="localFormData.age_min"
            type="number"
            class="age-input"
            placeholder="18"
            @input="updateFormData"
          />
          <text class="age-separator">-</text>
          <input
            v-model.number="localFormData.age_max"
            type="number"
            class="age-input"
            placeholder="60"
            @input="updateFormData"
          />
          <text class="age-unit">岁</text>
        </view>
      </view>

      <!-- 经验要求 -->
      <view class="requirement-row">
        <text class="requirement-label">经验要求</text>
        <picker
          mode="selector"
          :range="experienceOptions"
          range-key="label"
          :value="experienceIndex"
          @change="onExperienceChange"
        >
          <view class="requirement-value">
            <text>{{ currentExperience.label }}</text>
            <text class="picker-arrow">›</text>
          </view>
        </picker>
      </view>

      <!-- 学历要求 -->
      <view class="requirement-row">
        <text class="requirement-label">学历要求</text>
        <picker
          mode="selector"
          :range="educationOptions"
          range-key="label"
          :value="educationIndex"
          @change="onEducationChange"
        >
          <view class="requirement-value">
            <text>{{ currentEducation.label }}</text>
            <text class="picker-arrow">›</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 技能要求 -->
    <view class="form-section">
      <view class="section-header">
        <text class="section-title">技能要求</text>
        <text class="section-subtitle">描述具体的技能或能力要求</text>
      </view>
      <textarea
        v-model="localFormData.skills"
        placeholder="如：熟练操作电脑、有驾驶证、会使用叉车等"
        class="form-textarea"
        maxlength="200"
        @input="updateFormData"
      />
      <text class="char-count">{{ (localFormData.skills || '').length }}/200</text>
    </view>

    <!-- 工作标签 -->
    <view class="form-section">
      <view class="section-header">
        <text class="section-title">工作标签</text>
        <text class="section-subtitle">选择合适的标签帮助求职者了解工作特点</text>
      </view>
      <TagSelector
        :selected-tags="localFormData.tags || []"
        @update:tags="updateTags"
      />
    </view>

    <!-- 联系方式 -->
    <view class="form-section">
      <view class="section-header">
        <text class="section-title">联系方式</text>
        <text class="required">*</text>
      </view>

      <!-- 联系人 -->
      <view class="input-row">
        <text class="input-label">联系人</text>
        <input
          v-model="localFormData.contact_name"
          placeholder="请输入联系人姓名"
          class="form-input"
          maxlength="20"
          @input="updateFormData"
        />
      </view>

      <!-- 联系电话 -->
      <view class="input-row">
        <text class="input-label">联系电话</text>
        <input
          v-model="localFormData.contact_phone"
          placeholder="请输入联系电话"
          class="form-input"
          type="number"
          maxlength="11"
          @input="updateFormData"
        />
      </view>
    </view>

    <!-- 审核设置 -->
    <view class="form-section">
      <view class="section-header">
        <text class="section-title">审核设置</text>
      </view>

      <!-- 审核方式 -->
      <view class="requirement-row">
        <text class="requirement-label">审核方式</text>
        <picker
          mode="selector"
          :range="approvalModeOptions"
          range-key="label"
          :value="approvalModeIndex"
          @change="onApprovalModeChange"
        >
          <view class="requirement-value">
            <text>{{ currentApprovalMode.label }}</text>
            <text class="picker-arrow">›</text>
          </view>
        </picker>
      </view>

      <!-- 打卡方式 -->
      <view class="requirement-row">
        <text class="requirement-label">打卡方式</text>
        <picker
          mode="selector"
          :range="checkInMethodOptions"
          range-key="label"
          :value="checkInMethodIndex"
          @change="onCheckInMethodChange"
        >
          <view class="requirement-value">
            <text>{{ currentCheckInMethod.label }}</text>
            <text class="picker-arrow">›</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 高级选项 -->
    <view class="form-section">
      <view class="section-header">
        <text class="section-title">高级选项</text>
      </view>

      <!-- 保底工价 -->
      <view class="input-row">
        <text class="input-label">保底工价</text>
        <view class="input-wrapper">
          <text class="currency-symbol">¥</text>
          <input
            v-model.number="localFormData.guaranteed_wage"
            type="digit"
            placeholder="0"
            class="form-input-inline"
            @input="updateFormData"
          />
          <text class="input-unit">元（选填）</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { reactive, computed, watch } from 'vue'
import type { CreateGigRequest } from '@/types/gig'
import { 
  genderOptions, 
  experienceOptions, 
  educationOptions
} from '@/constants/standards'
import {
  GIG_APPROVAL_MODE_OPTIONS,
  GIG_CHECKIN_METHOD_OPTIONS 
} from '@/constants/gig'
import TagSelector from './components/TagSelector.vue'

interface Props {
  formData: CreateGigRequest
}

interface Emits {
  (e: 'update:formData', data: Partial<CreateGigRequest>): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 本地表单数据
const localFormData = reactive({ ...props.formData })

// 选项数据
const approvalModeOptions = GIG_APPROVAL_MODE_OPTIONS
const checkInMethodOptions = GIG_CHECKIN_METHOD_OPTIONS

// 监听父组件传入的数据变化
watch(
  () => props.formData,
  (newData) => {
    Object.assign(localFormData, newData)
  },
  { deep: true }
)

// 当前选中的索引
const genderIndex = computed(() => {
  return genderOptions.findIndex(option => option.value === localFormData.gender)
})

const experienceIndex = computed(() => {
  return experienceOptions.findIndex(option => option.value === localFormData.experience)
})

const educationIndex = computed(() => {
  return educationOptions.findIndex(option => option.value === localFormData.education)
})

const approvalModeIndex = computed(() => {
  return approvalModeOptions.findIndex(option => option.value === localFormData.approval_mode)
})

const checkInMethodIndex = computed(() => {
  return checkInMethodOptions.findIndex(option => option.value === localFormData.check_in_method)
})

// 当前选中的选项
const currentGender = computed(() => {
  return genderOptions.find(option => option.value === localFormData.gender) || genderOptions[0]
})

const currentExperience = computed(() => {
  return experienceOptions.find(option => option.value === localFormData.experience) || experienceOptions[0]
})

const currentEducation = computed(() => {
  return educationOptions.find(option => option.value === localFormData.education) || educationOptions[0]
})

const currentApprovalMode = computed(() => {
  return approvalModeOptions.find(option => option.value === localFormData.approval_mode) || approvalModeOptions[0]
})

const currentCheckInMethod = computed(() => {
  return checkInMethodOptions.find(option => option.value === localFormData.check_in_method) || checkInMethodOptions[0]
})

// 更新表单数据到父组件
const updateFormData = () => {
  emit('update:formData', { ...localFormData })
}

// 性别变化
const onGenderChange = (e: any) => {
  const index = e.detail.value
  const selectedOption = genderOptions[index]
  localFormData.gender = selectedOption.value
  updateFormData()
}

// 经验变化
const onExperienceChange = (e: any) => {
  const index = e.detail.value
  const selectedOption = experienceOptions[index]
  localFormData.experience = selectedOption.value
  updateFormData()
}

// 学历变化
const onEducationChange = (e: any) => {
  const index = e.detail.value
  const selectedOption = educationOptions[index]
  localFormData.education = selectedOption.value
  updateFormData()
}

// 审核方式变化
const onApprovalModeChange = (e: any) => {
  const index = e.detail.value
  const selectedOption = approvalModeOptions[index]
  localFormData.approval_mode = selectedOption.value
  updateFormData()
}

// 打卡方式变化
const onCheckInMethodChange = (e: any) => {
  const index = e.detail.value
  const selectedOption = checkInMethodOptions[index]
  localFormData.check_in_method = selectedOption.value
  updateFormData()
}

// 更新标签
const updateTags = (tags: string[]) => {
  localFormData.tags = tags
  updateFormData()
}
</script>

<style lang="scss" scoped>
.requirement-step {
  padding: var(--spacing-4);
}

.form-section {
  margin-bottom: var(--spacing-6);
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  display: flex;
  align-items: baseline;
  margin-bottom: var(--spacing-3);
  
  .section-title {
    font-size: 32rpx;
    font-weight: 500;
    color: var(--text-base);
  }
  
  .section-subtitle {
    font-size: 24rpx;
    color: var(--text-info);
    margin-left: var(--spacing-2);
  }
  
  .required {
    color: var(--text-red);
    margin-left: 4rpx;
  }
}

.requirement-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3) 0;
  border-bottom: 1rpx solid var(--border-color);
  
  &:last-child {
    border-bottom: none;
  }
}

.requirement-label {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.requirement-value {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--bg-search);
  border-radius: var(--radius);
  min-width: 200rpx;
  justify-content: space-between;
  
  text {
    font-size: 28rpx;
    color: var(--text-base);
  }
}

.picker-arrow {
  font-size: 32rpx;
  color: var(--text-info);
  transform: rotate(90deg);
}

.age-range-selector {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.age-input {
  width: 120rpx;
  height: 64rpx;
  padding: 0 12rpx;
  background-color: var(--bg-search);
  border-radius: var(--radius);
  font-size: 28rpx;
  color: var(--text-base);
  text-align: center;
  
  &::placeholder {
    color: var(--text-info);
  }
}

.age-separator {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.age-unit {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx;
  background-color: var(--bg-input);
  border: 2rpx solid transparent;
  border-radius: var(--radius);
  font-size: 28rpx;
  color: var(--text-base);
  line-height: 1.5;
  transition: all 0.3s ease;
  
  &:focus {
    border-color: var(--primary);
    background-color: var(--bg-card);
  }
  
  &::placeholder {
    color: var(--text-info);
  }
}

.char-count {
  display: block;
  text-align: right;
  font-size: 24rpx;
  color: var(--text-info);
  margin-top: var(--spacing-1);
}

.input-row {
  margin-bottom: var(--spacing-3);
  
  &:last-child {
    margin-bottom: 0;
  }
}

.input-label {
  display: block;
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-2);
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 16rpx;
  background-color: var(--bg-input);
  border: 2rpx solid transparent;
  border-radius: var(--radius);
  font-size: 28rpx;
  color: var(--text-base);
  transition: all 0.3s ease;
  
  &:focus {
    border-color: var(--primary);
    background-color: var(--bg-card);
  }
  
  &::placeholder {
    color: var(--text-info);
  }
}

.input-wrapper {
  display: flex;
  align-items: center;
  background-color: var(--bg-input);
  border-radius: var(--radius);
  padding: 0 16rpx;
  height: 80rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  
  &:focus-within {
    border-color: var(--primary);
    background-color: var(--bg-card);
  }
}

.currency-symbol {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-right: var(--spacing-2);
}

.form-input-inline {
  flex: 1;
  font-size: 28rpx;
  color: var(--text-base);
  height: 100%;
  
  &::placeholder {
    color: var(--text-info);
  }
}

.input-unit {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-left: var(--spacing-2);
}
</style>