<template>
  <view class="step-navigator">
    <view class="step-container">
      <!-- 第1步 -->
      <view class="step-item" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
        <view class="step-dot">
          <text v-if="currentStep > 1" class="check-icon">✓</text>
          <text v-else>1</text>
        </view>
        <text class="step-label">基础信息</text>
      </view>

      <!-- 连接线 -->
      <view class="step-line" :class="{ active: currentStep > 1 }"></view>

      <!-- 第2步 -->
      <view class="step-item" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
        <view class="step-dot">
          <text v-if="currentStep > 2" class="check-icon">✓</text>
          <text v-else>2</text>
        </view>
        <text class="step-label">招工需求</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  currentStep: number
}

defineProps<Props>()
</script>

<style lang="scss" scoped>
.step-navigator {
  background-color: var(--bg-card);
  padding: var(--spacing-4) var(--spacing-2);
  border-bottom: 1rpx solid var(--border-color);
}

.step-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;

  .step-dot {
    width: 64rpx;
    height: 64rpx;
    border-radius: 50%;
    background-color: var(--bg-tag);
    color: var(--text-grey);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    font-weight: 500;
    margin-bottom: var(--spacing);
    transition: all 0.3s ease;
  }

  .step-label {
    font-size: 26rpx;
    color: var(--text-grey);
    transition: all 0.3s ease;
  }

  &.active {
    .step-dot {
      background-color: var(--primary);
      color: var(--text-inverse);
    }
    
    .step-label {
      color: var(--primary);
      font-weight: 500;
    }
  }

  &.completed {
    .step-dot {
      background-color: var(--text-green);
      color: var(--text-inverse);
    }

    .check-icon {
      font-size: 32rpx;
    }
  }
}

.step-line {
  width: 120rpx;
  height: 4rpx;
  background-color: var(--border-color);
  margin: 0 var(--spacing-3);
  margin-bottom: var(--spacing-6);
  transition: all 0.3s ease;

  &.active {
    background-color: var(--text-green);
  }
}
</style>