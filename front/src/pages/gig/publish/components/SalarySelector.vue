<template>
  <view class="salary-selector">
    <!-- 薪酬金额 -->
    <view class="salary-amount">
      <text class="amount-label">薪酬金额</text>
      <view class="amount-input-wrapper">
        <text class="currency-symbol">¥</text>
        <input
          v-model.number="localSalary"
          type="digit"
          placeholder="0"
          class="salary-input"
          @input="onSalaryChange"
        />
        <text class="unit-text">/ {{ salaryUnitText }}</text>
      </view>
    </view>

    <!-- 薪酬单位选择 -->
    <view class="selector-row">
      <text class="selector-label">计薪方式</text>
      <picker
        mode="selector"
        :range="salaryUnitOptions"
        range-key="label"
        :value="salaryUnitIndex"
        @change="onSalaryUnitChange"
      >
        <view class="selector-display">
          <text class="selector-value">{{ currentSalaryUnit.label }}</text>
          <text class="selector-arrow">›</text>
        </view>
      </picker>
    </view>

    <!-- 结算方式选择 -->
    <view class="selector-row">
      <text class="selector-label">结算方式</text>
      <picker
        mode="selector"
        :range="settlementOptions"
        range-key="label"
        :value="settlementIndex"
        @change="onSettlementChange"
      >
        <view class="selector-display">
          <text class="selector-value">{{ currentSettlement.label }}</text>
          <text class="selector-arrow">›</text>
        </view>
      </picker>
    </view>

    <!-- 薪酬预览 -->
    <view v-if="salaryPreview" class="salary-preview">
      <text class="preview-text">{{ salaryPreview }}</text>
    </view>

    <!-- 薪酬参考 -->
    <view class="salary-reference">
      <text class="reference-title">薪酬参考</text>
      <view class="reference-items">
        <view
          v-for="ref in salaryReferences"
          :key="ref.label"
          class="reference-item"
          @click="applySalaryReference(ref)"
        >
          <text class="reference-label">{{ ref.label }}</text>
          <text class="reference-range">{{ ref.range }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { GIG_SALARY_UNIT_OPTIONS, SETTLEMENT_METHOD_OPTIONS, SALARY_UNIT } from '@/constants/gig'

interface Props {
  salary: number
  salaryUnit: number
  settlement: number
}

interface Emits {
  (e: 'update:salary', salary: number): void
  (e: 'update:salaryUnit', unit: number): void
  (e: 'update:settlement', settlement: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 本地数据
const localSalary = ref(props.salary)
const localSalaryUnit = ref(props.salaryUnit)
const localSettlement = ref(props.settlement)

// 选项数据
const salaryUnitOptions = GIG_SALARY_UNIT_OPTIONS
const settlementOptions = SETTLEMENT_METHOD_OPTIONS

// 当前选中的索引
const salaryUnitIndex = computed(() => {
  return salaryUnitOptions.findIndex(option => option.value === localSalaryUnit.value)
})

const settlementIndex = computed(() => {
  return settlementOptions.findIndex(option => option.value === localSettlement.value)
})

// 当前选中的选项
const currentSalaryUnit = computed(() => {
  return salaryUnitOptions.find(option => option.value === localSalaryUnit.value) || salaryUnitOptions[0]
})

const currentSettlement = computed(() => {
  return settlementOptions.find(option => option.value === localSettlement.value) || settlementOptions[0]
})

// 薪酬单位文本
const salaryUnitText = computed(() => {
  return currentSalaryUnit.value.label
})

// 薪酬预览
const salaryPreview = computed(() => {
  if (!localSalary.value || localSalary.value <= 0) return ''
  
  const amount = localSalary.value
  const unit = currentSalaryUnit.value.label
  const settlement = currentSettlement.value.label
  
  return `¥${amount}/${unit} · ${settlement}`
})

// 薪酬参考数据
const salaryReferences = [
  { label: '普工', range: '20-30/小时', salary: 25, unit: SALARY_UNIT.HOUR },
  { label: '技工', range: '30-50/小时', salary: 40, unit: SALARY_UNIT.HOUR },
  { label: '包装工', range: '150-200/天', salary: 180, unit: SALARY_UNIT.DAY },
  { label: '搬运工', range: '200-300/天', salary: 250, unit: SALARY_UNIT.DAY },
  { label: '临时工', range: '100-150/天', salary: 120, unit: SALARY_UNIT.DAY },
  { label: '计件工', range: '0.5-2/件', salary: 1, unit: SALARY_UNIT.PIECE }
]

// 监听props变化
watch(() => props.salary, (newVal) => {
  localSalary.value = newVal
})

watch(() => props.salaryUnit, (newVal) => {
  localSalaryUnit.value = newVal
})

watch(() => props.settlement, (newVal) => {
  localSettlement.value = newVal
})

// 薪酬金额变化
const onSalaryChange = () => {
  emit('update:salary', localSalary.value)
}

// 薪酬单位变化
const onSalaryUnitChange = (e: any) => {
  const index = e.detail.value
  const selectedOption = salaryUnitOptions[index]
  localSalaryUnit.value = selectedOption.value
  emit('update:salaryUnit', selectedOption.value)
}

// 结算方式变化
const onSettlementChange = (e: any) => {
  const index = e.detail.value
  const selectedOption = settlementOptions[index]
  localSettlement.value = selectedOption.value
  emit('update:settlement', selectedOption.value)
}

// 应用薪酬参考
const applySalaryReference = (ref: { salary: number; unit: number }) => {
  localSalary.value = ref.salary
  localSalaryUnit.value = ref.unit
  
  emit('update:salary', ref.salary)
  emit('update:salaryUnit', ref.unit)
}
</script>

<style lang="scss" scoped>
.salary-selector {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.salary-amount {
  padding: var(--spacing-4);
  border-bottom: 1rpx solid var(--border-color);
}

.amount-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-base);
  margin-bottom: var(--spacing-3);
}

.amount-input-wrapper {
  display: flex;
  align-items: center;
  background-color: var(--bg-input);
  border-radius: var(--radius);
  padding: 0 16rpx;
  height: 88rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  
  &:focus-within {
    border-color: var(--primary);
    background-color: var(--bg-card);
  }
}

.currency-symbol {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-base);
  margin-right: var(--spacing-2);
}

.salary-input {
  flex: 1;
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-base);
  height: 100%;
  
  &::placeholder {
    color: var(--text-info);
  }
}

.unit-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-left: var(--spacing-2);
}

.selector-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4);
  border-bottom: 1rpx solid var(--border-color);
  
  &:last-of-type {
    border-bottom: none;
  }
}

.selector-label {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.selector-display {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--bg-search);
  border-radius: var(--radius);
  min-width: 200rpx;
  justify-content: space-between;
}

.selector-value {
  font-size: 28rpx;
  color: var(--text-base);
}

.selector-arrow {
  font-size: 32rpx;
  color: var(--text-info);
  transform: rotate(90deg);
}

.salary-preview {
  padding: var(--spacing-3) var(--spacing-4);
  background-color: var(--bg-primary-light);
  border-left: 4rpx solid var(--primary);
  border-bottom: 1rpx solid var(--border-color);
}

.preview-text {
  font-size: 28rpx;
  color: var(--primary);
  font-weight: 500;
}

.salary-reference {
  padding: var(--spacing-4);
}

.reference-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-base);
  margin-bottom: var(--spacing-3);
}

.reference-items {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.reference-item {
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--bg-search);
  border-radius: var(--radius);
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  
  &:active {
    border-color: var(--primary);
    background-color: var(--bg-primary-light);
  }
}

.reference-label {
  display: block;
  font-size: 26rpx;
  color: var(--text-base);
  font-weight: 500;
  margin-bottom: 2rpx;
}

.reference-range {
  display: block;
  font-size: 22rpx;
  color: var(--text-info);
}
</style>