<template>
  <view class="card">
    <view class="section-title">
      <text>基本信息</text>
    </view>

    <form-input
      prop="title"
      label="工作标题"
      :asterisk="true"
      v-model="localFormData.title"
      placeholder="请输入工作标题"
    />

    <form-input
      prop="start_time"
      label="开始时间"
      :asterisk="true"
      :arrow="true"
      @click="showStartTimePicker"
    >
      <view
        class="picker-input"
        :class="{ placeholder: !localFormData.start_time }"
      >
        {{ localFormData.start_time || "请选择开始时间" }}
      </view>
    </form-input>

    <form-input
      prop="end_time"
      label="结束时间"
      :asterisk="true"
      :arrow="true"
      class="interactive-scale"
      @click="showEndTimePicker"
    >
      <view
        class="picker-input"
        :class="{ placeholder: !localFormData.end_time }"
      >
        {{ localFormData.end_time || "请选择结束时间" }}
      </view>
    </form-input>
    <form-input
      prop="address"
      label="工作地点"
      :asterisk="true"
      v-model="localFormData.address"
      placeholder="点击选择地点"
      :arrow="true"
      @tap="selectLocation"
      :disabled="true"
    />
    <form-input
      prop="detail_address"
      label="详细地址"
      :asterisk="true"
      v-model="localFormData.detail_address"
      placeholder="请输入楼号、楼层、门牌号等"
    />
    <form-input
      prop="people_count"
      label="招聘人数"
      :asterisk="true"
      v-model.number="localFormData.people_count"
      placeholder="请输入招聘人数"
      type="number"
      unit="人"
    />
    <form-input prop="salary" label="工作薪酬" :asterisk="true">
      <view class="salary-input-group">
        <input
          v-model.number="localFormData.salary"
          class="salary-input"
          type="number"
          placeholder="请输入金额"
        />
        <text class="salary-unit">元/</text>
        <picker
          class="salary-picker"
          :value="salaryUnitPicker.selectedIndex.value"
          :range="salaryUnitPicker.pickerRange.value"
          @change="salaryUnitPicker.onPickerChange"
        >
          <view class="picker-input">
            {{ salaryUnitPicker.selectedText.value || "单位" }}
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
    </form-input>

    <form-input
      prop="settlement"
      label="结算方式"
      :asterisk="true"
      :arrow="true"
      class="interactive-scale"
      :bottomBorder="false"
    >
      <picker
        :value="settlementPicker.selectedIndex.value"
        :range="settlementPicker.pickerRange.value"
        @change="settlementPicker.onPickerChange"
      >
        <view
          class="picker-input"
          :class="{ placeholder: !settlementPicker.selectedText.value }"
          >{{ settlementPicker.selectedText.value || "请选择结算方式" }}</view
        >
      </picker>
    </form-input>

    <!-- 时间选择器 -->
    <tui-datetime
      ref="startTimePickerRef"
      :type="1"
      title="选择开始时间"
      :set-date-time="localFormData.start_time"
      :minutes-data="minutesData"
      @confirm="onStartTimeConfirm"
    />

    <tui-datetime
      ref="endTimePickerRef"
      :type="1"
      title="选择结束时间"
      :minutes-data="minutesData"
      :set-date-time="localFormData.end_time"
      @confirm="onEndTimeConfirm"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, toRef } from "vue";
import FormInput from "@/components/common/FormInput.vue";
import { usePicker } from "@/hooks/usePicker";
import {
  GIG_SALARY_UNIT_OPTIONS,
  SETTLEMENT_METHOD_OPTIONS,
} from "@/constants/gig";
import type { CreateGigRequest } from "@/types/gig";

const props = defineProps<{ formData: CreateGigRequest }>();
const localFormData = toRef(props, "formData");

const startTimePickerRef = ref();
const endTimePickerRef = ref();
const minutesData = ["0", "15", "30", "45"];

const salaryUnitPicker = usePicker(
  GIG_SALARY_UNIT_OPTIONS,
  localFormData,
  "salary_unit"
);
const settlementPicker = usePicker(
  SETTLEMENT_METHOD_OPTIONS,
  localFormData,
  "settlement"
);

const showStartTimePicker = () => startTimePickerRef.value?.show();
const showEndTimePicker = () => endTimePickerRef.value?.show();

const onStartTimeConfirm = (e: { result: string }) => {
  const dateTime = e.result;
  if (
    localFormData.value.end_time &&
    new Date(dateTime) >= new Date(localFormData.value.end_time)
  ) {
    uni.showToast({ title: "开始时间不能晚于结束时间", icon: "none" });
  } else {
    localFormData.value.start_time = dateTime;
  }
};

const onEndTimeConfirm = (e: { result: string }) => {
  const dateTime = e.result;
  if (
    localFormData.value.start_time &&
    new Date(dateTime) <= new Date(localFormData.value.start_time)
  ) {
    uni.showToast({ title: "结束时间不能早于开始时间", icon: "none" });
  } else {
    localFormData.value.end_time = dateTime;
  }
};

const selectLocation = () => {
  uni.chooseLocation({
    success: (res) => {
      localFormData.value.address_name = res.name || "";
      localFormData.value.address = res.address || "";
      localFormData.value.latitude = res.latitude;
      localFormData.value.longitude = res.longitude;
    },
  });
};
</script>

<style lang="scss" scoped>
.card {
  margin-bottom: 24rpx;
}
.section-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: 600;
  padding: 16rpx;
}
.picker-input {
  height: 100%;
  display: flex;
  align-items: center;
  text-align: right;
  width: 100%;
  padding-right: 8rpx;
  &.placeholder {
    color: var(--text-grey);
  }
}
.salary-input-group {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 8rpx;
}
.salary-input {
  flex: 1;
  text-align: right;
}
.salary-unit {
  flex-shrink: 0;
}
.salary-picker {
  flex-shrink: 0;
  .picker-input {
    padding-right: 0;
    border-left: 1rpx solid var(--border-color);
    padding-left: 16rpx;
    min-width: 120rpx;
    justify-content: space-between;
    gap: 8rpx;
    .picker-arrow {
      font-size: 20rpx;
      color: var(--text-info);
    }
  }
}
</style>
