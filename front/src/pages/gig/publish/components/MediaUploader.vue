<template>
  <view class="media-uploader">
    <!-- 媒体类型选择 -->
    <view class="media-type-selector">
      <view 
        class="type-option"
        :class="{ active: currentType === 'image' }"
        @click="switchType('image')"
      >
        <text class="type-icon">📷</text>
        <text class="type-text">照片</text>
      </view>
      <view 
        class="type-option"
        :class="{ active: currentType === 'video' }"
        @click="switchType('video')"
      >
        <text class="type-icon">🎥</text>
        <text class="type-text">视频</text>
      </view>
    </view>

    <!-- 图片上传区域 -->
    <view v-if="currentType === 'image'" class="image-upload-area">
      <view class="media-grid">
        <!-- 已上传的图片 -->
        <view 
          v-for="(image, index) in localImages"
          :key="index"
          class="media-item image-item"
        >
          <image :src="image" class="media-preview" mode="aspectFill" />
          <view class="media-actions">
            <view class="action-btn preview-btn" @click="previewImage(index)">
              <text class="action-icon">👁</text>
            </view>
            <view class="action-btn delete-btn" @click="removeImage(index)">
              <text class="action-icon">🗑</text>
            </view>
          </view>
        </view>

        <!-- 添加图片按钮 -->
        <view 
          v-if="localImages.length < maxImages"
          class="media-item add-item"
          @click="chooseImages"
        >
          <text class="add-icon">+</text>
          <text class="add-text">添加照片</text>
          <text class="add-limit">{{ localImages.length }}/{{ maxImages }}</text>
        </view>
      </view>
      
      <view class="upload-tips">
        <text class="tip-text">• 最多上传{{ maxImages }}张照片</text>
        <text class="tip-text">• 支持JPG、PNG格式，单张不超过5MB</text>
        <text class="tip-text">• 建议上传工作环境、设备等相关照片</text>
      </view>
    </view>

    <!-- 视频上传区域 -->
    <view v-if="currentType === 'video'" class="video-upload-area">
      <view v-if="localVideos.length > 0" class="video-list">
        <view 
          v-for="(video, index) in localVideos"
          :key="index"
          class="video-item"
        >
          <video 
            :src="video"
            class="video-preview"
            controls
            :show-center-play-btn="true"
            :show-play-btn="true"
          />
          <view class="video-actions">
            <view class="action-btn delete-btn" @click="removeVideo(index)">
              <text class="action-icon">🗑</text>
            </view>
          </view>
        </view>
      </view>

      <view 
        v-if="localVideos.length < maxVideos"
        class="video-upload-btn"
        @click="chooseVideo"
      >
        <text class="upload-icon">🎥</text>
        <text class="upload-text">选择视频</text>
        <text class="upload-limit">{{ localVideos.length }}/{{ maxVideos }}</text>
      </view>

      <view class="upload-tips">
        <text class="tip-text">• 最多上传{{ maxVideos }}个视频</text>
        <text class="tip-text">• 支持MP4格式，单个不超过100MB</text>
        <text class="tip-text">• 建议时长30秒内，清晰展示工作内容</text>
      </view>
    </view>

    <!-- 上传进度显示 -->
    <view v-if="uploadProgress.show" class="upload-progress">
      <view class="progress-bar">
        <view 
          class="progress-fill"
          :style="{ width: uploadProgress.percent + '%' }"
        ></view>
      </view>
      <text class="progress-text">上传中... {{ uploadProgress.percent }}%</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { showToast } from '@/utils/ui/feedback'

interface Props {
  images: string[]
  videos: string[]
}

interface Emits {
  (e: 'update:images', images: string[]): void
  (e: 'update:videos', videos: string[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 当前媒体类型
const currentType = ref<'image' | 'video'>('image')

// 本地媒体数据
const localImages = ref<string[]>([...props.images])
const localVideos = ref<string[]>([...props.videos])

// 限制配置
const maxImages = 9
const maxVideos = 1

// 上传进度
const uploadProgress = reactive({
  show: false,
  percent: 0
})

// 监听props变化
watch(() => props.images, (newVal) => {
  localImages.value = [...newVal]
  if (newVal.length > 0) {
    currentType.value = 'image'
  }
})

watch(() => props.videos, (newVal) => {
  localVideos.value = [...newVal]
  if (newVal.length > 0) {
    currentType.value = 'video'
  }
})

// 切换媒体类型
const switchType = (type: 'image' | 'video') => {
  currentType.value = type
}

// 选择图片
const chooseImages = () => {
  const remainingSlots = maxImages - localImages.value.length
  
  uni.chooseImage({
    count: remainingSlots,
    sizeType: ['compressed'],
    sourceType: ['camera', 'album'],
    success: (res) => {
      const filePaths = Array.isArray(res.tempFilePaths) ? res.tempFilePaths : [res.tempFilePaths]
      uploadFiles(filePaths, 'image')
    },
    fail: (error) => {
      console.error('选择图片失败:', error)
      showToast('选择图片失败')
    }
  })
}

// 选择视频
const chooseVideo = () => {
  uni.chooseVideo({
    sourceType: ['camera', 'album'],
    maxDuration: 60,
    compressed: true,
    success: (res) => {
      // 检查文件大小
      if (res.size > 100 * 1024 * 1024) { // 100MB
        showToast('视频文件过大，请选择小于100MB的视频')
        return
      }
      
      uploadFiles([res.tempFilePath], 'video')
    },
    fail: (error) => {
      console.error('选择视频失败:', error)
      showToast('选择视频失败')
    }
  })
}

// 上传文件
const uploadFiles = async (filePaths: string[], type: 'image' | 'video') => {
  try {
    uploadProgress.show = true
    uploadProgress.percent = 0
    
    const uploadedUrls: string[] = []
    
    for (let i = 0; i < filePaths.length; i++) {
      const filePath = filePaths[i]
      
      // 模拟上传进度
      const progressStep = 100 / filePaths.length
      uploadProgress.percent = Math.round((i + 0.5) * progressStep)
      
      try {
        // TODO: 调用实际的上传API
        // const url = await uploadFile(filePath)
        
        // 模拟上传延时和结果
        await new Promise(resolve => setTimeout(resolve, 1000))
        const mockUrl = `https://example.com/${type}/${Date.now()}_${i}.${type === 'image' ? 'jpg' : 'mp4'}`
        uploadedUrls.push(mockUrl)
        
        uploadProgress.percent = Math.round((i + 1) * progressStep)
      } catch (uploadError) {
        console.error('上传文件失败:', uploadError)
        showToast(`上传第${i + 1}个文件失败`)
      }
    }
    
    // 更新本地数据并发送到父组件
    if (type === 'image' && uploadedUrls.length > 0) {
      localImages.value.push(...uploadedUrls)
      emit('update:images', localImages.value)
      // 清空视频（互斥）
      if (localVideos.value.length > 0) {
        localVideos.value = []
        emit('update:videos', [])
      }
    } else if (type === 'video' && uploadedUrls.length > 0) {
      localVideos.value = uploadedUrls
      emit('update:videos', localVideos.value)
      // 清空图片（互斥）
      if (localImages.value.length > 0) {
        localImages.value = []
        emit('update:images', [])
      }
    }
    
    showToast('上传成功')
  } catch (error) {
    console.error('上传失败:', error)
    showToast('上传失败，请稍后再试')
  } finally {
    uploadProgress.show = false
    uploadProgress.percent = 0
  }
}

// 预览图片
const previewImage = (index: number) => {
  uni.previewImage({
    current: index,
    urls: localImages.value
  })
}

// 删除图片
const removeImage = (index: number) => {
  localImages.value.splice(index, 1)
  emit('update:images', localImages.value)
}

// 删除视频
const removeVideo = (index: number) => {
  localVideos.value.splice(index, 1)
  emit('update:videos', localVideos.value)
}
</script>

<style lang="scss" scoped>
.media-uploader {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.media-type-selector {
  display: flex;
  background-color: var(--bg-search);
  border-radius: var(--radius);
  overflow: hidden;
  margin-bottom: var(--spacing-4);
}

.type-option {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  transition: all 0.3s ease;
  
  &.active {
    background-color: var(--primary);
    color: var(--text-inverse);
    
    .type-text {
      color: var(--text-inverse);
    }
  }
  
  .type-icon {
    font-size: 32rpx;
  }
  
  .type-text {
    font-size: 28rpx;
    color: var(--text-secondary);
    font-weight: 500;
  }
}

.image-upload-area, .video-upload-area {
  // Empty styles, content-specific styles below
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
}

.media-item {
  aspect-ratio: 1;
  border-radius: var(--radius);
  overflow: hidden;
  position: relative;
  
  &.add-item {
    background-color: var(--bg-search);
    border: 2rpx dashed var(--border-color);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
    transition: all 0.3s ease;
    
    &:active {
      border-color: var(--primary);
      background-color: var(--bg-primary-light);
    }
    
    .add-icon {
      font-size: 48rpx;
      color: var(--text-info);
    }
    
    .add-text {
      font-size: 24rpx;
      color: var(--text-secondary);
    }
    
    .add-limit {
      font-size: 20rpx;
      color: var(--text-info);
    }
  }
}

.image-item {
  .media-preview {
    width: 100%;
    height: 100%;
  }
  
  .media-actions {
    position: absolute;
    top: 8rpx;
    right: 8rpx;
    display: flex;
    gap: 8rpx;
  }
  
  .action-btn {
    width: 56rpx;
    height: 56rpx;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    
    &.preview-btn:active {
      background-color: var(--primary);
    }
    
    &.delete-btn:active {
      background-color: var(--text-red);
    }
    
    .action-icon {
      font-size: 28rpx;
      color: white;
    }
  }
}

.video-list {
  margin-bottom: var(--spacing-4);
}

.video-item {
  position: relative;
  border-radius: var(--radius);
  overflow: hidden;
  margin-bottom: var(--spacing-3);
  
  .video-preview {
    width: 100%;
    height: 400rpx;
  }
  
  .video-actions {
    position: absolute;
    top: 16rpx;
    right: 16rpx;
    
    .action-btn {
      width: 64rpx;
      height: 64rpx;
      background-color: rgba(0, 0, 0, 0.6);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &.delete-btn:active {
        background-color: var(--text-red);
      }
      
      .action-icon {
        font-size: 32rpx;
        color: white;
      }
    }
  }
}

.video-upload-btn {
  height: 200rpx;
  background-color: var(--bg-search);
  border: 2rpx dashed var(--border-color);
  border-radius: var(--radius);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  transition: all 0.3s ease;
  margin-bottom: var(--spacing-4);
  
  &:active {
    border-color: var(--primary);
    background-color: var(--bg-primary-light);
  }
  
  .upload-icon {
    font-size: 64rpx;
    color: var(--text-info);
  }
  
  .upload-text {
    font-size: 28rpx;
    color: var(--text-secondary);
    font-weight: 500;
  }
  
  .upload-limit {
    font-size: 24rpx;
    color: var(--text-info);
  }
}

.upload-tips {
  padding: var(--spacing-3);
  background-color: var(--bg-tag);
  border-radius: var(--radius);
  
  .tip-text {
    display: block;
    font-size: 24rpx;
    color: var(--text-info);
    line-height: 1.5;
    margin-bottom: 4rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.upload-progress {
  padding: var(--spacing-4);
  background-color: var(--bg-primary-light);
  border-top: 1rpx solid var(--border-color);
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: var(--bg-search);
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: var(--spacing-2);
}

.progress-fill {
  height: 100%;
  background-color: var(--primary);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: var(--primary);
  text-align: center;
  display: block;
}
</style>