<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left">
          <view class="back-button" @click="goBack">
            <text class="back-icon">←</text>
          </view>
          <text class="navbar-title">发布零工</text>
        </view>
        <view class="navbar-right">
          <text class="step-indicator">{{ currentStep }}/2</text>
        </view>
      </view>

      <!-- 步骤导航器 -->
      <view class="step-navigator-container">
        <StepNavigator :current-step="currentStep" />
        <view class="step-progress">
          <view class="progress-bar">
            <view
              class="progress-fill"
              :style="{ width: (currentStep / 2) * 100 + '%' }"
            ></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 页面内容 -->
    <view class="page-content">
      <!-- 表单内容区域 -->
      <tui-form ref="formRef" :model="formData" class="form-container">
      <!-- 第1步：基础信息 -->
      <view v-if="currentStep === 1" class="step-content">
        <!-- 任务详情 -->
        <view class="form-section" :class="{ 'form-section--empty': !formData.description }">
          <tui-form-item
            prop="description"
            label="任务详情"
            :arrow="true"
            @click="openTaskDetailEditor"
          >
            <view class="form-value" :class="{ 'form-value--empty': !formData.description }">
              <text v-if="!formData.description" class="empty-icon">📝</text>
              <text class="value-text">{{ getTaskDetailDesc() }}</text>
            </view>
          </tui-form-item>
        </view>

        <!-- 性别年龄 -->
        <view class="form-section" :class="{ 'form-section--empty': isGenderAgeEmpty }">
          <tui-form-item
            prop="gender"
            label="性别年龄"
            :arrow="true"
            @click="openGenderAgeSelector"
          >
            <view class="form-value" :class="{ 'form-value--empty': isGenderAgeEmpty }">
              <text v-if="isGenderAgeEmpty" class="empty-icon">👤</text>
              <text class="value-text">{{ getGenderAgeText() }}</text>
            </view>
          </tui-form-item>
        </view>

        <!-- 工作日期 - 使用新的横向滚动设计 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">工作日期<text class="required">*</text></text>
          </view>
          <HorizontalDateSelector
            :selected-date="selectedDate"
            @update:date="onDateSelect"
          />
        </view>

        <!-- 工作时间 - 直接显示在页面上 -->
        <view class="form-section" :class="{ 'form-section--empty': !formData.start_time || !formData.end_time }">
          <tui-form-item
            prop="start_time"
            label="工作时间"
            :asterisk="true"
            :arrow="true"
            @click="openTimeEditor"
          >
            <view class="work-time-display" :class="{ 'work-time-display--empty': !formData.start_time || !formData.end_time }">
              <view v-if="formData.start_time && formData.end_time" class="time-range">
                <text class="start-time">{{ getStartTimeDisplay() }}</text>
                <text class="time-separator">-</text>
                <text class="end-time">{{ getEndTimeDisplay() }}</text>
                <text v-if="isNextDay" class="next-day-label">次日</text>
              </view>
              <view v-else class="time-empty">
                <text class="empty-icon">⏰</text>
                <text class="empty-text">请选择工作时间</text>
              </view>
              <view v-if="workHours && formData.start_time && formData.end_time" class="work-duration">
                <text class="duration-text">{{ workHours }}工时</text>
                <text class="daily-hours">每日工作时长</text>
              </view>
            </view>
          </tui-form-item>
        </view>

        <!-- 干活地点 - 参考图片设计 -->
        <view class="form-section" :class="{ 'form-section--empty': !formData.address_name }">
          <tui-form-item
            prop="address"
            label="干活地点"
            :asterisk="true"
            :arrow="true"
            @click="openAddressSelector"
          >
            <view class="location-display" :class="{ 'location-display--empty': !formData.address_name }">
              <view v-if="formData.address_name" class="location-content">
                <text class="location-name">{{ formData.address_name }}</text>
                <text class="location-address">{{ formData.detail_address }}</text>
              </view>
              <view v-else class="location-empty">
                <text class="empty-icon">📍</text>
                <text class="empty-text">设置干活地点</text>
              </view>
            </view>
          </tui-form-item>
        </view>

      </view>

      <!-- 第2步：招工需求 -->
      <view v-if="currentStep === 2" class="step-content">
        <!-- 需求人数 -->
        <view class="form-section">
          <tui-form-item
            prop="people_count"
            label="需求人数"
            :asterisk="true"
          >
            <CountSelector
              v-model="formData.people_count"
              :min="1"
              :max="99"
              unit="人"
            />
          </tui-form-item>
        </view>

        <!-- 工价设置 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">工价设置<text class="required">*</text></text>
            <text class="section-desc">设置合理的工价吸引更多工人</text>
          </view>

          <view class="salary-card">
            <view class="salary-input-container">
              <view class="salary-input-group">
                <tui-input
                  v-model="formData.salary"
                  type="number"
                  placeholder="0"
                  :borderBottom="false"
                  padding="0"
                  class="salary-input"
                />
                <view class="salary-unit-selector">
                  <text class="salary-unit">元/小时</text>
                  <text class="unit-arrow">▼</text>
                </view>
              </view>

              <view class="salary-suggestions">
                <text class="suggestion-label">参考工价：</text>
                <view class="suggestion-tags">
                  <view class="suggestion-tag" @click="setSalary(25)">
                    <text class="tag-text">25元/时</text>
                  </view>
                  <view class="suggestion-tag" @click="setSalary(30)">
                    <text class="tag-text">30元/时</text>
                  </view>
                  <view class="suggestion-tag" @click="setSalary(35)">
                    <text class="tag-text">35元/时</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="salary-preview">
              <view class="preview-item">
                <text class="preview-label">预计日薪</text>
                <text class="preview-value">{{ getDailySalary() }}元</text>
              </view>
              <view class="preview-item">
                <text class="preview-label">总工价</text>
                <text class="preview-value">{{ getTotalSalary() }}元</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 联系方式 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">联系方式<text class="required">*</text></text>
          </view>

          <view class="contact-card">
            <tui-form-item
              prop="contact_phone"
              label="联系电话"
              :asterisk="true"
              :arrow="true"
              @click="openContactEditor"
            >
              <view class="contact-display">
                <view class="contact-info">
                  <text class="contact-phone">{{ formData.contact_phone || '13126606919' }}</text>
                  <text class="contact-label">招工联系电话</text>
                </view>
                <view class="contact-status verified">
                  <text class="status-text">已验证</text>
                </view>
              </view>
            </tui-form-item>
          </view>
        </view>

        <!-- 结算方式 -->
        <view class="form-section">
          <tui-form-item
            prop="settlement"
            label="结算方式"
            :asterisk="true"
          >
            <tui-radio-group v-model="settlementValue" @change="onSettlementChange">
              <view class="settlement-options">
                <tui-radio
                  v-for="option in settlementOptions"
                  :key="option.value"
                  :value="option.value"
                  :color="'var(--primary)'"
                  class="settlement-radio"
                >
                  <view class="settlement-option-content">
                    <text class="option-title">{{ option.label }}</text>
                    <text class="option-desc">{{ option.desc }}</text>
                  </view>
                </tui-radio>
              </view>
            </tui-radio-group>
          </tui-form-item>
        </view>

        <!-- 接单设置 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">接单设置</text>
            <text class="section-desc">设置工人接单和打卡方式</text>
          </view>

          <view class="order-settings-card">
            <view class="setting-item">
              <view class="setting-info">
                <text class="setting-title">自动接单</text>
                <text class="setting-desc">工人申请后自动通过</text>
              </view>
              <switch
                :checked="formData.approval_mode === 'auto'"
                @change="toggleAutoApproval"
                color="#FF6D00"
              />
            </view>

            <view class="setting-item">
              <view class="setting-info">
                <text class="setting-title">人脸打卡</text>
                <text class="setting-desc">要求工人人脸识别打卡</text>
              </view>
              <switch
                :checked="formData.check_in_method === 'face'"
                @change="toggleFaceCheckIn"
                color="#FF6D00"
              />
            </view>

            <view class="setting-item">
              <view class="setting-info">
                <text class="setting-title">紧急招工</text>
                <text class="setting-desc">标记为紧急，优先展示</text>
              </view>
              <switch
                :checked="formData.is_urgent"
                @change="toggleUrgent"
                color="#FF6D00"
              />
            </view>
          </view>
        </view>
      </view>
    </tui-form>

    <!-- 底部操作区 - 现代化设计 -->
    <view class="action-bar">
      <view class="action-bar-content">
        <!-- 上一步按钮 -->
        <view
          v-if="currentStep > 1"
          class="action-btn action-btn--secondary"
          @click="prevStep"
        >
          <view class="btn-content">
            <text class="btn-icon">←</text>
            <text class="btn-text">上一步</text>
          </view>
        </view>

        <!-- 下一步/发布按钮 -->
        <view
          class="action-btn action-btn--primary"
          :class="{
            'action-btn--full': currentStep === 1,
            'action-btn--disabled': !canProceed
          }"
          @click="nextStep"
        >
          <view class="btn-content">
            <text class="btn-text">
              {{ currentStep === 2 ? '发布零工' : '下一步' }}
            </text>
            <text v-if="currentStep === 2" class="btn-price">¥3</text>
            <text v-else class="btn-icon">→</text>
          </view>
          <view class="btn-shine"></view>
        </view>
      </view>
    </view>

    <!-- 全屏加载 -->
    <view v-if="isSubmitting" class="loading-overlay">
      <view class="loading-backdrop" @click.stop></view>
      <view class="loading-content">
        <view class="loading-animation">
          <view class="loading-spinner"></view>
          <view class="loading-dots">
            <view class="dot dot-1"></view>
            <view class="dot dot-2"></view>
            <view class="dot dot-3"></view>
          </view>
        </view>
        <text class="loading-text">{{ loadingText }}</text>
        <text class="loading-subtitle">请稍候，正在为您发布零工...</text>
      </view>
    </view>

    <!-- 弹窗组件（使用简化的状态管理） -->
    <TaskDetailPopup 
      v-if="popupStates.taskDetail" 
      :form-data="formData"
      @close="closePopup('taskDetail')"
      @confirm="onTaskDetailConfirm"
    />
    
    <GenderAgePopup 
      v-if="popupStates.genderAge" 
      :gender="formData.gender"
      :age-min="formData.age_min"
      :age-max="formData.age_max"
      @close="closePopup('genderAge')"
      @confirm="onGenderAgeConfirm"
    />
    
    <TimeEditorPopup 
      v-if="popupStates.timeEditor" 
      :start-time="formData.start_time"
      :end-time="formData.end_time"
      @close="closePopup('timeEditor')"
      @confirm="onTimeConfirm"
    />
    
    <ContactPopup 
      v-if="popupStates.contact" 
      :contact-name="formData.contact_name"
      :contact-phone="formData.contact_phone"
      @close="closePopup('contact')"
      @confirm="onContactConfirm"
    />
    
    <OrderSettingsPopup 
      v-if="popupStates.orderSettings" 
      :form-data="formData"
      @close="closePopup('orderSettings')"
      @confirm="onOrderSettingsConfirm"
    />

    <AddressSelectorPopup
      v-if="popupStates.addressSelector"
      :address="formData.address"
      :detail-address="formData.detail_address"
      :address-name="formData.address_name"
      :latitude="formData.latitude"
      :longitude="formData.longitude"
      @close="closePopup('addressSelector')"
      @confirm="onAddressConfirm"
    />
    </view>
  </view>
</template>

<script setup lang="ts">
import StepNavigator from './components/StepNavigator.vue'
import TaskDetailPopup from './components/popups/TaskDetailPopup.vue'
import GenderAgePopup from './components/popups/GenderAgePopup.vue'
import TimeEditorPopup from './components/popups/TimeEditorPopup.vue'
import ContactPopup from './components/popups/ContactPopup.vue'
import OrderSettingsPopup from './components/popups/OrderSettingsPopup.vue'
import AddressSelectorPopup from './components/popups/AddressSelectorPopup.vue'
import HorizontalDateSelector from './components/HorizontalDateSelector.vue'
import CountSelector from '@/components/common/CountSelector.vue'
import tuiRadioGroup from '@/components/thorui/tui-radio-group/tui-radio-group.vue'
import tuiRadio from '@/components/thorui/tui-radio/tui-radio.vue'

import type { CreateGigRequest } from '@/types/gig'
import { showToast } from '@/utils/ui/feedback'
import { formatDate, DateFormat } from '@/utils/core/date'
import { genderOptions } from '@/constants/standards'
import { callApi } from '@/utils/network/helpers'
import { validateGigForm } from '@/utils/business/gig-validation'
import GigApi from '@/api/gig'

// 当前步骤
const currentStep = ref(1)

// 加载状态
const isSubmitting = ref(false)
const loadingText = ref('发布中...')

// 弹窗状态管理（简化版）
const popupStates = reactive({
  taskDetail: false,
  genderAge: false,
  timeEditor: false,
  contact: false,
  orderSettings: false,
  addressSelector: false
})

// 兼容性别名 - 移除未使用的computed属性

// 选中的日期
const selectedDate = ref('')

// 空状态检查
const isGenderAgeEmpty = computed(() => {
  return formData.gender === 0 && formData.age_min === 18 && formData.age_max === 60
})

// 表单数据
const formData = reactive<CreateGigRequest>({
  title: '零工招聘', // 设置默认标题
  description: '',
  salary: 0,
  salary_unit: 1, // 默认按小时
  settlement: 1,  // 默认日结
  people_count: 1,
  start_time: '',
  end_time: '',
  address_name: '',
  address: '',
  detail_address: '',
  latitude: 0,
  longitude: 0,
  gender: 0,
  age_min: 18,
  age_max: 60,
  experience: 0,
  education: 0,
  skills: '',
  contact_name: '',
  contact_phone: '',
  is_urgent: false,
  company_name: '',
  tags: [],
  images: [],
  videos: [],
  guaranteed_wage: 0,
  approval_mode: 'manual',
  check_in_method: 'none'
})

// 额外的UI状态
const workDays = ref(1)

// 页面加载
onLoad((options) => {
  // 初始化选中日期为今天
  selectedDate.value = formatDate(new Date(), DateFormat.DATE_ONLY)
  
  // 如果有ID参数，则是编辑模式
  if (options?.id) {
    // TODO: 加载现有数据
  }
  
  // 初始化默认的工作时间
  if (!formData.start_time) {
    const today = formatDate(new Date(), DateFormat.DATE_ONLY)
    formData.start_time = `${today} 18:50:00`
    formData.end_time = `${today} 02:50:00`
  }
})



// 简化的验证函数，使用工具函数
const validateStep1 = (): boolean => validateGigForm(formData, 'step1')
const validateStep2 = (): boolean => validateGigForm(formData, 'step2')

// 是否可以继续下一步
const canProceed = computed((): boolean => {
  if (currentStep.value === 1) {
    return !!(
      formData.start_time && formData.end_time &&
      formData.address && formData.address.trim().length > 0 &&
      formData.detail_address && formData.detail_address.trim().length >= 5 &&
      formData.people_count > 0 && formData.people_count <= 99 &&
      formData.salary > 0
    )
  }

  if (currentStep.value === 2) {
    const phoneRegex = /^1[3-9]\d{9}$/
    return !!(
      formData.contact_name && formData.contact_name.trim().length >= 2 &&
      formData.contact_phone && phoneRegex.test(formData.contact_phone.trim()) &&
      formData.age_min < formData.age_max && formData.age_min >= 16 && formData.age_max <= 65
    )
  }

  return false
})

// 上一步
const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

// 下一步/提交
const nextStep = async () => {
  if (!canProceed.value) return

  if (currentStep.value === 1) {
    if (validateStep1()) {
      currentStep.value = 2
    }
  } else if (currentStep.value === 2) {
    if (validateStep2()) {
      await submitForm()
    }
  }
}

// 人数控制函数已移至CountSelector组件中

// 弹窗控制函数（简化版）
const openPopup = (popupName: keyof typeof popupStates) => {
  popupStates[popupName] = true
}

const closePopup = (popupName: keyof typeof popupStates) => {
  popupStates[popupName] = false
}

// 兼容性函数
const openTaskDetailEditor = () => openPopup('taskDetail')
const openGenderAgeSelector = () => openPopup('genderAge')
const openTimeEditor = () => openPopup('timeEditor')
const openContactEditor = () => openPopup('contact')
const openAddressSelector = () => openPopup('addressSelector')

// 导航函数
const goBack = () => {
  uni.navigateBack()
}

// 已在上面定义



// 工作天数控制函数已移至CountSelector组件中

// 工价计算相关函数（简化版）
const setSalary = (amount: number) => {
  formData.salary = amount
}

// 使用computed优化计算
const dailySalary = computed(() => {
  if (!formData.salary || !workHours.value) return 0
  const hours = parseFloat(workHours.value) || 0
  return Math.round(formData.salary * hours)
})

const totalSalary = computed(() => {
  return Math.round(dailySalary.value * workDays.value)
})

// 兼容性函数（保持模板中的调用）
const getDailySalary = () => dailySalary.value
const getTotalSalary = () => totalSalary.value

// 结算方式设置
const settlementOptions = [
  { value: '1', label: '日结', desc: '当日完工当日结算' },
  { value: '2', label: '周结', desc: '每周结算一次' },
  { value: '3', label: '月结', desc: '每月结算一次' }
]

const settlementValue = computed({
  get: () => String(formData.settlement),
  set: (value: string) => {
    formData.settlement = parseInt(value)
  }
})

const onSettlementChange = (e: any) => {
  formData.settlement = parseInt(e.detail.value)
}

// 接单设置切换函数
const toggleAutoApproval = (e: any) => {
  formData.approval_mode = e.detail.value ? 'auto' : 'manual'
}

const toggleFaceCheckIn = (e: any) => {
  formData.check_in_method = e.detail.value ? 'face' : 'none'
}

const toggleUrgent = (e: any) => {
  formData.is_urgent = e.detail.value
}

// 弹窗确认回调


// 弹窗确认回调（简化版）
const onTaskDetailConfirm = (data: any) => {
  Object.assign(formData, data)
  closePopup('taskDetail')
}

const onGenderAgeConfirm = (data: { gender: number, ageMin: number, ageMax: number }) => {
  Object.assign(formData, {
    gender: data.gender,
    age_min: data.ageMin,
    age_max: data.ageMax
  })
  closePopup('genderAge')
}

const onTimeConfirm = (data: { startTime: string, endTime: string }) => {
  Object.assign(formData, {
    start_time: data.startTime,
    end_time: data.endTime
  })
  closePopup('timeEditor')
}

const onAddressConfirm = (data: { 
  address: string
  detailAddress?: string
  addressName?: string
  latitude: number
  longitude: number
}) => {
  Object.assign(formData, {
    address: data.address,
    detail_address: data.detailAddress || '',
    address_name: data.addressName || '',
    latitude: data.latitude,
    longitude: data.longitude
  })
  closePopup('addressSelector')
}

// 日期选择相关
const onDateSelect = (date: string) => {
  selectedDate.value = date
  // 更新formData中的日期部分
  if (formData.start_time) {
    const time = formData.start_time.split(' ')[1] || '09:00:00'
    formData.start_time = `${date} ${time}`
  }
  if (formData.end_time) {
    const time = formData.end_time.split(' ')[1] || '18:00:00'
    formData.end_time = `${date} ${time}`
  }
}

const onContactConfirm = (data: { contactName: string, contactPhone: string }) => {
  Object.assign(formData, {
    contact_name: data.contactName,
    contact_phone: data.contactPhone
  })
  closePopup('contact')
}

const onOrderSettingsConfirm = (data: any) => {
  Object.assign(formData, data)
  closePopup('orderSettings')
}

// 显示文本计算函数（简化版）
const getTaskDetailDesc = () => {
  const desc = formData.description || '未填写详情'
  return desc.length > 20 ? `${desc.substring(0, 20)}...` : desc
}

const getGenderAgeText = () => {
  const genderText = genderOptions.find(opt => opt.value === formData.gender)?.label || '不限'
  const ageText = formData.age_min === 16 && formData.age_max === 60 
    ? '16岁~不限' 
    : `${formData.age_min}岁~${formData.age_max}岁`
  return `${genderText}, ${ageText}`
}



// 时间显示函数
const getTimeDisplay = (timeString: string, defaultTime: string = '00:00') => {
  if (!timeString) return defaultTime
  const time = timeString.split(' ')[1] || `${defaultTime}:00`
  return time.substring(0, 5)
}

const getStartTimeDisplay = () => getTimeDisplay(formData.start_time, '18:50')
const getEndTimeDisplay = () => getTimeDisplay(formData.end_time, '02:50')

// 判断是否为次日
const isNextDay = computed(() => {
  if (!formData.start_time || !formData.end_time) return false
  
  const startTime = formData.start_time.split(' ')[1] || '18:50:00'
  const endTime = formData.end_time.split(' ')[1] || '02:50:00'
  
  const [startHour] = startTime.split(':').map(Number)
  const [endHour] = endTime.split(':').map(Number)
  
  return endHour < startHour
})

// 计算工作小时数（简化版）
const workHours = computed(() => {
  if (!formData.start_time || !formData.end_time) return '8'
  
  const getMinutes = (timeStr: string, defaultTime: string) => {
    const time = timeStr.split(' ')[1] || defaultTime
    const [hour, min] = time.split(':').map(Number)
    return hour * 60 + min
  }
  
  let startMinutes = getMinutes(formData.start_time, '18:50:00')
  let endMinutes = getMinutes(formData.end_time, '02:50:00')
  
  // 处理跨日情况
  if (endMinutes <= startMinutes) {
    endMinutes += 24 * 60
  }
  
  const hours = Math.round((endMinutes - startMinutes) / 60)
  return hours.toString()
})

// 提交表单
const submitForm = async () => {
  try {
    isSubmitting.value = true
    loadingText.value = '发布中...'

    // 使用 callApi 进行表单提交
    const result = await callApi(() => GigApi.create(formData), {
      showLoading: true,
      loadingText: '发布中...',
      showSuccessToast: true,
      successText: '零工发布成功！',
      onSuccess: (data) => {
        console.log('零工发布成功, ID:', data.gigId)
        // 延迟跳转，让用户看到成功提示
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      },
      onError: (error) => {
        console.error('零工发布失败:', error)
        // callApi 会自动显示错误提示，这里可以添加额外的错误处理逻辑
        if (error?.code === 'VALIDATION_ERROR') {
          // 如果是验证错误，可能需要回到第一步修改数据
          currentStep.value = 1
        }
      }
    })

    // 如果成功，result.success 会是 true
    if (!result.success) {
      // callApi 已经处理了错误显示，这里只处理业务逻辑
      return
    }
    
  } catch (error: any) {
    console.error('零工发布异常:', error)
    showToast('发布过程中出现异常，请重试')
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style lang="scss" scoped>
/* 页面容器样式 */
.page-container {
  min-height: 100vh;
  background-color: var(--bg-page);
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: var(--bg-card);
  border-bottom: 2rpx solid var(--border-light);
  padding-top: env(safe-area-inset-top);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3) var(--spacing-4);
  height: 88rpx;
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.back-button {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-2);
  transition: all 0.3s ease;

  &:active {
    background: var(--bg-search);
    transform: scale(0.95);
  }
}

.back-icon {
  font-size: 36rpx;
  color: var(--text-base);
  font-weight: 600;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-base);
}

.navbar-right {
  display: flex;
  align-items: center;
}

.step-indicator {
  font-size: 28rpx;
  color: var(--text-secondary);
  font-weight: 500;
}

.page-content {
  flex: 1;
  margin-top: calc(88rpx + env(safe-area-inset-top) + 120rpx); /* 导航栏高度 + 步骤导航器高度 */
  padding-bottom: 200rpx; /* 为底部操作栏留出空间 */
}

.form-container {
  background: transparent;

  /* 移动优先：默认为小屏幕优化 */
  padding: var(--spacing-3);

  /* 更大屏幕的渐进增强 */
  @media screen and (min-width: 600rpx) {
    padding: var(--spacing-4);
  }

  @media screen and (min-width: 750rpx) {
    padding: var(--spacing-5);
  }
}

.step-content {
  display: flex;
  flex-direction: column;

  /* 移动优先：适中的间距 */
  gap: var(--spacing-3);

  /* 更大屏幕增加间距 */
  @media screen and (min-width: 600rpx) {
    gap: var(--spacing-4);
  }

  @media screen and (min-width: 750rpx) {
    gap: var(--spacing-5);
  }
}

.form-section {
  background: var(--bg-card);
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;

  /* 移动优先：较小的圆角 */
  border-radius: var(--radius-2);

  /* 更大屏幕增加圆角 */
  @media screen and (min-width: 600rpx) {
    border-radius: var(--radius-3);
  }

  /* 小屏幕减少阴影 */
  @media screen and (max-width: 480rpx) {
    box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.06);
  }

  /* 空状态样式 */
  &--empty {
    border: 2rpx dashed var(--border-light);
    background: var(--bg-search);

    /* 移动设备上的触摸反馈 */
    &:active {
      border-color: var(--primary);
      background: var(--bg-primary-light);
      transform: scale(0.995);
    }

    /* 桌面设备的悬停效果 */
    @media (hover: hover) {
      &:hover {
        border-color: var(--primary);
        background: var(--bg-primary-light);
      }
    }
  }
}

.section-header {
  padding: var(--spacing-4) var(--spacing-4) var(--spacing-2);
}

.section-title {
  color: var(--text-base);
  font-weight: 600;

  /* 移动优先：适中的字体大小 */
  font-size: 30rpx;
  line-height: 1.4;

  /* 更大屏幕增加字体大小 */
  @media screen and (min-width: 600rpx) {
    font-size: 32rpx;
  }

  @media screen and (min-width: 750rpx) {
    font-size: 34rpx;
  }
}

.required {
  color: var(--text-red);
  margin-left: 4rpx;
  font-weight: 600;
}

.form-value {
  color: var(--text-base);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-2);

  /* 移动优先：适中的字体大小和行高 */
  font-size: 26rpx;
  line-height: 1.5;

  /* 更大屏幕增加字体大小 */
  @media screen and (min-width: 600rpx) {
    font-size: 28rpx;
  }

  @media screen and (min-width: 750rpx) {
    font-size: 30rpx;
  }

  &.placeholder {
    color: var(--text-grey);
  }

  /* 空状态样式 */
  &--empty {
    color: var(--text-placeholder);

    .value-text {
      font-style: italic;
    }
  }
}

/* 空状态图标和文本 - 移动优化 */
.empty-icon {
  opacity: 0.6;

  /* 移动优先：适中的图标大小 */
  font-size: 28rpx;

  /* 更大屏幕增加图标大小 */
  @media screen and (min-width: 600rpx) {
    font-size: 32rpx;
  }
}

.empty-text {
  color: var(--text-placeholder);
  font-style: italic;

  /* 移动优先：适中的字体大小 */
  font-size: 26rpx;
  line-height: 1.4;

  /* 更大屏幕增加字体大小 */
  @media screen and (min-width: 600rpx) {
    font-size: 28rpx;
  }
}

.value-text {
  flex: 1;
  text-align: right;
  word-break: break-word; /* 防止长文本溢出 */
}

/* 工作时间空状态 */
.work-time-display {
  &--empty {
    .time-empty {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: var(--spacing-2);
    }
  }
}

/* 地点空状态 */
.location-display {
  &--empty {
    .location-empty {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: var(--spacing-2);
    }
  }
}

.form-desc {
  color: var(--text-secondary);
  font-size: 26rpx;
}

// 工作时间显示
.work-time-display {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.time-range {
  display: flex;
  align-items: baseline;
  gap: var(--spacing);
}

.start-time, .end-time {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-base);
}

.time-separator {
  color: var(--text-secondary);
  font-size: 28rpx;
}

.next-day-label {
  background: var(--bg-warning-light);
  color: var(--text-yellow);
  padding: 4rpx 8rpx;
  border-radius: var(--radius);
  font-size: 22rpx;
  font-weight: 500;
}

.work-duration {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding-top: var(--spacing-2);
  border-top: 1rpx solid var(--border-light);
}

.duration-text {
  color: var(--primary);
  font-size: 26rpx;
  font-weight: 500;
}

.daily-hours {
  color: var(--text-secondary);
  font-size: 24rpx;
}

// 地点显示
.location-display {
  display: flex;
  flex-direction: column;
  gap: var(--spacing);
}

.location-content {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.location-name {
  color: var(--text-base);
  font-size: 28rpx;
  font-weight: 500;
}

.location-address {
  color: var(--text-secondary);
  font-size: 26rpx;
  line-height: 1.4;
}

.location-placeholder {
  color: var(--text-grey);
  font-size: 28rpx;
}

// 人数选择器样式已移至CountSelector组件

// 薪酬选择器
.salary-selector {
  width: 100%;
}

.salary-input-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.salary-unit {
  color: var(--text-secondary);
  font-size: 28rpx;
  white-space: nowrap;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 现代化底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.95) 100%);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
  padding: var(--spacing-4) var(--spacing-4) calc(var(--spacing-4) + env(safe-area-inset-bottom));
  z-index: 100;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.12);

  /* 移动端优化 */
  @media screen and (max-width: 750rpx) {
    padding: var(--spacing-3) var(--spacing-3) calc(var(--spacing-3) + env(safe-area-inset-bottom));
  }
}

.action-bar-content {
  display: flex;
  gap: var(--spacing-3);
  align-items: center;
  max-width: 750rpx;
  margin: 0 auto;
}

/* 现代化按钮样式 */
.action-btn {
  position: relative;
  flex: 1;
  height: 100rpx;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  user-select: none;

  /* 确保触摸目标足够大 */
  min-height: 100rpx;

  &:active {
    transform: scale(0.96);
  }

  /* 全宽按钮 */
  &--full {
    flex: 1;
  }

  /* 主要按钮样式 */
  &--primary {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-400) 100%);
    box-shadow: 0 8rpx 24rpx rgba(255, 109, 0, 0.4);

    .btn-text {
      color: var(--text-inverse);
      font-weight: 600;
    }

    .btn-price {
      color: var(--text-inverse);
      font-weight: 700;
    }

    .btn-icon {
      color: var(--text-inverse);
    }

    &:active {
      box-shadow: 0 4rpx 16rpx rgba(255, 109, 0, 0.6);
    }
  }

  /* 次要按钮样式 */
  &--secondary {
    background: var(--bg-card);
    border: 2rpx solid var(--border-light);
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);

    .btn-text {
      color: var(--text-base);
      font-weight: 500;
    }

    .btn-icon {
      color: var(--text-secondary);
    }

    &:active {
      background: var(--bg-search);
      border-color: var(--primary);
    }
  }

  /* 禁用状态 */
  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;

    &:active {
      transform: none !important;
    }
  }
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  position: relative;
  z-index: 2;
}

.btn-text {
  font-size: 32rpx;
  line-height: 1;
}

.btn-price {
  font-size: 28rpx;
  background: rgba(255, 255, 255, 0.2);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  line-height: 1;
}

.btn-icon {
  font-size: 28rpx;
  font-weight: 600;
  line-height: 1;
}

/* 按钮光泽效果 */
.btn-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.action-btn--primary:active .btn-shine {
  left: 100%;
}

/* 工作天数选择器样式已移至CountSelector组件 */

/* 工价设置样式 */
.salary-card {
  background: var(--bg-card);
  border-radius: var(--radius-3);
  padding: var(--spacing-4);
  border: 2rpx solid var(--border-light);
}

.salary-input-container {
  margin-bottom: var(--spacing-4);
}

.salary-input-group {
  display: flex;
  align-items: center;
  background: var(--bg-search);
  border-radius: var(--radius-2);
  padding: var(--spacing-3);
  border: 2rpx solid var(--border-light);
  transition: border-color 0.3s ease;

  &:focus-within {
    border-color: var(--primary);
    box-shadow: 0 0 0 4rpx rgba(255, 109, 0, 0.1);
  }
}

.salary-input {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-base);
}

.salary-unit-selector {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-2) var(--spacing-3);
  background: var(--bg-card);
  border-radius: var(--radius-1);
  border: 1rpx solid var(--border-light);
}

.salary-unit {
  font-size: 28rpx;
  color: var(--text-base);
  font-weight: 500;
}

.unit-arrow {
  font-size: 20rpx;
  color: var(--text-secondary);
}

.salary-suggestions {
  margin-top: var(--spacing-3);
}

.suggestion-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-2);
  display: block;
}

.suggestion-tags {
  display: flex;
  gap: var(--spacing-2);
}

.suggestion-tag {
  padding: var(--spacing-2) var(--spacing-3);
  background: var(--bg-search);
  border-radius: var(--radius-2);
  border: 2rpx solid var(--border-light);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    background: var(--primary);
    border-color: var(--primary);

    .tag-text {
      color: var(--text-inverse);
    }
  }
}

.tag-text {
  font-size: 24rpx;
  color: var(--text-base);
  font-weight: 500;
}

.salary-preview {
  display: flex;
  justify-content: space-between;
  padding-top: var(--spacing-4);
  border-top: 2rpx solid var(--border-light);
}

.preview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-1);
}

.preview-label {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.preview-value {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--primary);
}

/* 联系方式样式 */
.contact-card {
  background: var(--bg-card);
  border-radius: var(--radius-3);
  border: 2rpx solid var(--border-light);
}

.contact-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.contact-phone {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-base);
}

.contact-label {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.contact-status {
  padding: 8rpx 16rpx;
  border-radius: var(--radius-1);

  &.verified {
    background: rgba(52, 199, 89, 0.1);

    .status-text {
      color: #34c759;
      font-size: 22rpx;
      font-weight: 500;
    }
  }
}

/* 结算方式样式 - 使用ThorUI Radio */
.settlement-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.settlement-radio {
  padding: var(--spacing-3) 0;
  border-bottom: 1rpx solid var(--border-light);

  &:last-child {
    border-bottom: none;
  }
}

.settlement-option-content {
  flex: 1;
  margin-left: var(--spacing-3);
}

.option-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-base);
  display: block;
  margin-bottom: 8rpx;
}

.option-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}



/* 接单设置样式 */
.order-settings-card {
  background: var(--bg-card);
  border-radius: var(--radius-3);
  border: 2rpx solid var(--border-light);
  overflow: hidden;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4);
  border-bottom: 2rpx solid var(--border-light);

  &:last-child {
    border-bottom: none;
  }
}

.setting-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.setting-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-base);
}

.setting-desc {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 步骤导航器容器样式 */
.step-navigator-container {
  padding: var(--spacing-3) var(--spacing-4);
  border-bottom: 2rpx solid var(--border-light);
}

.step-progress {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: var(--spacing-3);
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background: var(--bg-search);
  border-radius: 4rpx;
  overflow: hidden;
  margin-right: var(--spacing-3);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), #ff8c00);
  border-radius: 4rpx;
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-text {
  font-size: 24rpx;
  color: var(--text-secondary);
  font-weight: 500;
  min-width: 80rpx;
  text-align: right;
}

/* 现代加载动画样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8rpx);
  animation: fadeIn 0.3s ease-out;
}

.loading-content {
  position: relative;
  background: var(--bg-card);
  border-radius: var(--radius-4);
  padding: var(--spacing-6);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-4);
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 300rpx;
  max-width: 90vw;

  /* 移动端优化 */
  @media screen and (max-width: 750rpx) {
    padding: var(--spacing-5);
    min-width: 280rpx;
    border-radius: var(--radius-3);
  }

  /* 小屏幕设备优化 */
  @media screen and (max-width: 600rpx) {
    padding: var(--spacing-4);
    min-width: 260rpx;
    gap: var(--spacing-3);
  }
}

.loading-animation {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid var(--bg-search);
  border-top: 6rpx solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-dots {
  position: absolute;
  display: flex;
  gap: 8rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  background: var(--primary);
  border-radius: 50%;
  animation: bounce 1.4s ease-in-out infinite both;
}

.dot-1 {
  animation-delay: -0.32s;
}

.dot-2 {
  animation-delay: -0.16s;
}

.loading-text {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-base);
  text-align: center;
}

.loading-subtitle {
  font-size: 24rpx;
  color: var(--text-secondary);
  text-align: center;
  line-height: 1.4;
}

/* 动画关键帧 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(60rpx) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
</style>