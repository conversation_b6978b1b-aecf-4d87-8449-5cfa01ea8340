<template>
  <view class="container">
    <view class="content">
      <tui-form ref="formRef" :model="formData" :rules="formRules">
        <!-- 基本信息 -->
        <GigBasicInfoForm :form-data="formData" />

        <!-- 招聘要求 -->
        <GigRequirementsForm :form-data="formData" />

        <!-- 工作描述 -->
        <GigDescriptionForm :form-data="formData" />

        <!-- 联系方式 -->
        <GigContactForm :form-data="formData" />
      </tui-form>

      <!-- 协议确认 -->
      <view class="card">
        <view class="agreement-item interactive-scale" @click="toggleAgreement">
          <view class="checkbox" :class="{ checked: agreedToTerms }">
            <text v-if="agreedToTerms">✓</text>
          </view>
          <view class="agreement-text">
            我已阅读并同意《用户服务协议》和《隐私政策》
          </view>
        </view>
      </view>

      <!-- 底部操作栏 -->
      <view class="submit-section">
        <tui-button
          @click="handleSubmit"
          :disabled="!canSubmit || isSubmitting"
          width="100%"
          height="96rpx"
          size="large"
          type="primary"
          shape="circle"
        >
          {{
            isSubmitting
              ? "提交中..."
              : isEditMode
              ? "保存修改"
              : "付费发布 3 元"
          }}
        </tui-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import tuiForm from "@/components/thorui/tui-form/tui-form.vue";
import GigBasicInfoForm from "./components/GigBasicInfoForm.vue";
import GigRequirementsForm from "./components/GigRequirementsForm.vue";
import GigDescriptionForm from "./components/GigDescriptionForm.vue";
import GigContactForm from "./components/GigContactForm.vue";
import { useGigForm } from "@/services/gig";
import type { CreateGigRequest } from "@/types/gig";
import { showToast } from "@/utils/ui/feedback";
import { navigateBack } from "@/utils/ui/navigation";

const gigId = ref<number | null>(null);
onLoad((options) => {
  if (options?.id) {
    gigId.value = parseInt(options.id, 10);
  }
});

const { formData, isEditMode, isSubmitting, submit } = useGigForm(gigId);

const formRef = ref();
const agreedToTerms = ref(false);

const formRules = [
  { name: "title", rule: ["required"], msg: "请输入工作标题" },
  { name: "start_time", rule: ["required"], msg: "请选择开始时间" },
  { name: "end_time", rule: ["required"], msg: "请选择结束时间" },
  { name: "address", rule: ["required"], msg: "请选择工作地点" },
  { name: "detail_address", rule: ["required"], msg: "请输入详细地址" },
  { name: "people_count", rule: ["required"], msg: "请输入招聘人数" },
  { name: "salary", rule: ["required"], msg: "请输入薪酬金额" },
  { name: "settlement", rule: ["required"], msg: "请选择结算方式" },
  { name: "gender", rule: ["required"], msg: "请选择性别要求" },
  { name: "age_min", rule: ["required"], msg: "请输入最小年龄" },
  { name: "age_max", rule: ["required"], msg: "请输入最大年龄" },
  { name: "approval_mode", rule: ["required"], msg: "请选择审核方式" },
  { name: "check_in_method", rule: ["required"], msg: "请选择打卡方式" },
  { name: "contact_name", rule: ["required"], msg: "请输入联系人" },
  {
    name: "contact_phone",
    rule: ["required", "isMobile"],
    msg: ["请输入联系电话", "请输入正确的联系电话格式"],
  },
];

const resetForm = () => {
  agreedToTerms.value = false;
};

const toggleAgreement = () => {
  agreedToTerms.value = !agreedToTerms.value;
};

const canSubmit = computed(() => {
  const requiredFields: (keyof CreateGigRequest)[] = [
    "title",
    "start_time",
    "end_time",
    "address",
    "detail_address",
    "people_count",
    "salary",
    "settlement",
    "age_min",
    "age_max",
    "approval_mode",
    "check_in_method",
    "contact_name",
    "contact_phone",
  ];
  return (
    requiredFields.every((field) => !!formData[field]) &&
    formData.age_min >= 16 &&
    formData.age_max <= 65 &&
    formData.age_min < formData.age_max &&
    agreedToTerms.value
  );
});

const handleSubmit = async () => {
  if (!canSubmit.value) {
    showToast("请完整填写必要信息并同意协议");
    return;
  }

  try {
    const validationResult = await formRef.value.validate(
      formData,
      formRules,
      true
    );
    if (!validationResult.isPass) {
      showToast(
        validationResult.errorMsg[0]?.msg ||
          validationResult.errorMsg ||
          "请检查表单信息"
      );
      return;
    }

    if (!formData.gender && formData.gender !== 0) {
      showToast("请选择性别要求");
      return;
    }
    if (!formData.age_min || !formData.age_max) {
      showToast("请选择年龄范围");
      return;
    }
    if (new Date(formData.start_time) >= new Date(formData.end_time)) {
      showToast("开始时间必须早于结束时间");
      return;
    }
    if (formData.age_min >= formData.age_max) {
      showToast("最小年龄必须小于最大年龄");
      return;
    }
    if (formData.age_min < 16 || formData.age_max > 65) {
      showToast("年龄范围必须在16-65岁之间");
      return;
    }

    if (formData.approval_mode === "auto" && formData.people_count > 10) {
      const result = await new Promise((resolve) => {
        uni.showModal({
          title: "提示",
          content:
            "招聘人数较多，建议使用手动审核方式以便更好筛选。是否继续使用自动录用？",
          success: (res) => resolve(res.confirm),
        });
      });
      if (!result) return;
    }

    await submit();

    showToast(isEditMode.value ? "更新成功！" : "发布成功！");
    resetForm();
    setTimeout(() => navigateBack(), 1500);
  } catch (error: any) {
    showToast(error.message || "提交失败，请稍后再试");
  }
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: var(--bg-page);
}
.content {
  margin: 20rpx;
  padding-bottom: 160rpx;
}
.card {
  margin-bottom: 24rpx;
}
.agreement-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-2);
  padding: var(--spacing) 0;
}
.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-card);
  flex-shrink: 0;
  transition: all 0.3s ease;
  &.checked {
    background-color: var(--primary);
    border-color: var(--primary);
    color: var(--text-inverse);
    font-size: 24rpx;
    font-weight: 600;
  }
}
.agreement-text {
  line-height: var(--line-height-large);
  flex: 1;
}
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx var(--spacing);
  background-color: var(--bg-card);
  border-top: 1rpx solid var(--border-color);
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.08);
  z-index: 100;
}
</style>
