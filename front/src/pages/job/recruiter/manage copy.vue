<template>
  <view class="container">
    <!-- 顶部发布按钮 -->
    <view class="publish-btn-wrapper px-20rpx my-30rpx">
      <view class="publish-btn" @tap="goToPublish">
        <text class="i-carbon-add font-size-44rpx font-500 mr-10rpx"></text>
        <text>发布新职位</text>
      </view>
    </view>

    <!-- 会员状态卡片 -->
    <view
      class="membership-card mx-20rpx mb-24rpx"
      v-if="!userMembership.isEnterpriseMember"
    >
      <view class="membership-promotion">
        <view class="promotion-content">
          <view class="promotion-header">
            <text class="i-carbon-star-filled promotion-icon"></text>
            <text class="promotion-title">升级企业会员</text>
            <view class="promotion-badge">限时优惠</view>
          </view>
          <text class="promotion-subtitle">解锁更多招聘特权，提升招聘效率</text>
          <view class="promotion-benefits">
            <view class="benefit-item">
              <text class="i-carbon-checkmark-filled benefit-icon"></text>
              <text class="benefit-text">无限职位发布</text>
            </view>
            <view class="benefit-item">
              <text class="i-carbon-checkmark-filled benefit-icon"></text>
              <text class="benefit-text">急聘置顶推广</text>
            </view>
            <view class="benefit-item">
              <text class="i-carbon-checkmark-filled benefit-icon"></text>
              <text class="benefit-text">简历筛选工具</text>
            </view>
          </view>
        </view>
        <view class="promotion-action">
          <view class="upgrade-btn" @tap="goToUpgrade">
            <text class="i-carbon-arrow-right upgrade-arrow"></text>
            <text>立即升级</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 企业会员仪表板 -->
    <view
      class="enterprise-dashboard mx-20rpx mb-24rpx"
      v-if="userMembership.isEnterpriseMember"
    >
      <view class="dashboard-header">
        <view class="company-info">
          <image
            class="company-avatar"
            :src="userMembership.companyAvatar"
            mode="aspectFill"
          ></image>
          <view class="company-details">
            <view class="company-name-row">
              <text class="company-name">{{ userMembership.companyName }}</text>
              <view class="membership-tier">
                <text class="i-carbon-star-filled tier-icon"></text>
                <text class="tier-text">{{
                  userMembership.membershipTier
                }}</text>
              </view>
            </view>
            <text class="membership-status"
              >企业会员 · 有效期至 {{ userMembership.expiryDate }}</text
            >
          </view>
        </view>
      </view>

      <view class="benefits-overview">
        <view class="benefit-card">
          <view class="benefit-header">
            <text class="i-carbon-document benefit-card-icon"></text>
            <text class="benefit-title">职位发布</text>
          </view>
          <view class="benefit-stats">
            <text class="benefit-value">{{ userMembership.jobPostQuota }}</text>
            <text class="benefit-label">剩余额度</text>
          </view>
        </view>

        <view class="benefit-card">
          <view class="benefit-header">
            <text class="i-carbon-rocket benefit-card-icon"></text>
            <text class="benefit-title">急聘推广</text>
          </view>
          <view class="benefit-stats">
            <text class="benefit-value">{{ userMembership.urgentBoosts }}</text>
            <text class="benefit-label">可用次数</text>
          </view>
        </view>

        <view class="benefit-card">
          <view class="benefit-header">
            <text class="i-carbon-renew benefit-card-icon"></text>
            <text class="benefit-title">刷新次数</text>
          </view>
          <view class="benefit-stats">
            <text class="benefit-value">{{
              userMembership.refreshCredits
            }}</text>
            <text class="benefit-label">今日剩余</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 数据概览 -->
    <view class="card mx-20rpx">
      <view class="card-title mb-30rpx">
        <text class="i-carbon-chart-line mr-10rpx"></text>
        <text>数据概览</text>
      </view>
      <view class="grid grid-cols-3 gap-20rpx">
        <view class="stat-item">
          <text class="stat-value">{{ statistics.totalJobs }}</text>
          <text class="stat-label">在招职位</text>
        </view>
        <view class="stat-item">
          <text class="stat-value stat-views">{{ statistics.totalViews }}</text>
          <text class="stat-label">今日浏览</text>
        </view>
        <view class="stat-item">
          <text class="stat-value stat-resumes">{{
            statistics.totalResumes
          }}</text>
          <text class="stat-label">待处理简历</text>
        </view>
      </view>
    </view>

    <!-- 搜索和筛选区域 -->
    <view class="search-filter-section mx-20rpx my-24rpx">
      <view class="search-container">
        <view class="search-input-wrapper">
          <text class="i-carbon-search search-icon"></text>
          <input
            class="search-input"
            placeholder="搜索职位名称或关键词"
            v-model="searchKeyword"
            @input="handleSearch"
          />
          <text
            v-if="searchKeyword"
            class="i-carbon-close clear-icon"
            @tap="clearSearch"
          ></text>
        </view>
      </view>
    </view>
    <!-- 职位筛选 -->
    <view
      class="mx-20rpx sticky top-0 py-10rpx"
      :class="{ 'bg-white': isSticky }"
    >
      <uv-tabs
        :list="tabs"
        lineColor="#ff860a"
        :activeStyle="{
          fontWeight: 'bold',
        }"
        :current="currentTab"
        @change="handleTabClick"
      ></uv-tabs>
    </view>
    <!-- 职位列表 -->
    <view class="mx-20rpx mb-30rpx">
      <view v-if="filteredJobList.length > 0" class="job-list">
        <view v-for="job in filteredJobList" :key="job.id" class="job-card">
          <view class="job-card-header">
            <view class="job-title-section">
              <view class="job-title-row">
                <text class="job-title">{{ job.title }}</text>
                <view class="job-tags">
                  <view v-if="job.isUrgent" class="job-tag urgent-tag">
                    <text class="i-carbon-flash-filled tag-icon"></text>
                    <text>急聘</text>
                  </view>
                  <view v-if="job.isTop" class="job-tag top-tag">
                    <text class="i-carbon-arrow-up tag-icon"></text>
                    <text>置顶</text>
                  </view>
                </view>
              </view>
              <view class="job-salary-row">
                <text class="job-salary">{{ job.salary }}</text>
                <view class="job-status" :class="getStatusClass(job.status)">
                  {{ job.statusText }}
                </view>
              </view>
            </view>
          </view>

          <!-- 职位统计信息 -->
          <view class="job-metrics">
            <view class="metric-item">
              <text class="i-carbon-view metric-icon"></text>
              <text class="metric-value">{{ job.views }}</text>
              <text class="metric-label">浏览</text>
            </view>
            <view class="metric-item">
              <text class="i-carbon-user-profile metric-icon"></text>
              <text class="metric-value">{{ job.applications }}</text>
              <text class="metric-label">投递</text>
            </view>
            <view class="metric-item">
              <text class="i-carbon-time metric-icon"></text>
              <text class="metric-value">{{ job.remainingDays }}</text>
              <text class="metric-label">天后到期</text>
            </view>
          </view>

          <!-- 最后刷新时间 -->
          <view class="job-refresh-info">
            <text class="i-carbon-renew refresh-icon"></text>
            <text class="refresh-text"
              >最后刷新：{{ job.lastRefreshTime }}</text
            >
          </view>

          <!-- 操作按钮 -->
          <view class="job-actions">
            <view class="action-btn secondary-btn" @tap="editJob(job.id)">
              <text class="i-carbon-edit action-icon"></text>
              <text>编辑</text>
            </view>

            <view class="action-btn secondary-btn" @tap="refreshJob(job.id)">
              <text class="i-carbon-renew action-icon"></text>
              <text>刷新</text>
            </view>

            <view
              class="action-btn warning-btn"
              @tap="closeJob(job.id)"
              v-if="job.status === 'active'"
            >
              <text class="i-carbon-pause action-icon"></text>
              <text>下线</text>
            </view>
            <view
              class="action-btn success-btn"
              @tap="reopenJob(job.id)"
              v-else-if="job.status === 'closed'"
            >
              <text class="i-carbon-play action-icon"></text>
              <text>上线</text>
            </view>

            <view
              class="action-btn primary-btn"
              @tap="viewResumes(job.id)"
              v-if="job.applications > 0"
            >
              <text class="i-carbon-user-multiple action-icon"></text>
              <text>查看简历</text>
            </view>
            <view class="action-btn primary-btn" @tap="boostJob(job.id)" v-else>
              <text class="i-carbon-rocket action-icon"></text>
              <text>推广</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else class="empty-state">
        <text class="i-carbon-document-blank empty-icon"></text>
        <view class="empty-text">暂无发布的职位</view>
        <view class="empty-btn" @tap="goToPublish">立即发布</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";

const isSticky = ref(false);

onPageScroll((e) => {
  isSticky.value = e.scrollTop > 170;
});

// 搜索关键词
const searchKeyword = ref("");

// 用户会员信息
const userMembership = ref({
  isEnterpriseMember: true, // 设置为true以显示企业会员仪表板，false显示升级提示
  companyName: "科技有限公司",
  companyAvatar: "/static/images/company-avatar.png",
  membershipTier: "企业会员 Pro",
  expiryDate: "2024-12-31",
  jobPostQuota: 15,
  urgentBoosts: 8,
  refreshCredits: 12,
});

// 跳转到发布职位页面
const goToPublish = () => {
  uni.navigateTo({
    url: "/pages/job/publish",
  });
};

// 跳转到会员升级页面
const goToUpgrade = () => {
  uni.navigateTo({
    url: "/pages/job/enterprise-cooperation",
  });
};

// 搜索处理
const handleSearch = () => {
  // 实时搜索逻辑
  console.log("搜索关键词:", searchKeyword.value);
};

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = "";
};

// 统计数据
const statistics = ref({
  totalJobs: 3,
  totalViews: 68,
  totalResumes: 12,
});

// 职位列表数据
const jobList = ref([
  {
    id: "001",
    title: "前端开发工程师",
    salary: "15K-25K",
    status: "active",
    statusText: "招聘中",
    isUrgent: true,
    isTop: true,
    views: 253,
    applications: 8,
    remainingDays: 12,
    lastRefreshTime: "2024-01-15 14:30",
  },
  {
    id: "002",
    title: "销售经理",
    salary: "8K-15K",
    status: "active",
    statusText: "招聘中",
    isUrgent: false,
    isTop: false,
    views: 156,
    applications: 4,
    remainingDays: 5,
    lastRefreshTime: "2024-01-14 16:20",
  },
  {
    id: "003",
    title: "人事专员",
    salary: "6K-8K",
    status: "closed",
    statusText: "已暂停",
    isUrgent: false,
    isTop: false,
    views: 86,
    applications: 0,
    remainingDays: 20,
    lastRefreshTime: "2024-01-10 09:15",
  },
  {
    id: "004",
    title: "产品经理",
    salary: "20K-30K",
    status: "active",
    statusText: "招聘中",
    isUrgent: true,
    isTop: true,
    views: 189,
    applications: 5,
    remainingDays: 7,
    lastRefreshTime: "2024-01-14 11:45",
  },
  {
    id: "005",
    title: "UI设计师",
    salary: "12K-18K",
    status: "active",
    statusText: "招聘中",
    isUrgent: false,
    isTop: false,
    views: 97,
    applications: 3,
    remainingDays: 15,
    lastRefreshTime: "2024-01-13 13:20",
  },
  {
    id: "006",
    title: "Java开发工程师",
    salary: "18K-25K",
    status: "closed",
    statusText: "已暂停",
    isUrgent: false,
    isTop: true,
    views: 210,
    applications: 7,
    remainingDays: 0,
    lastRefreshTime: "2024-01-12 10:30",
  },
  {
    id: "007",
    title: "运营专员",
    salary: "8K-12K",
    status: "active",
    statusText: "招聘中",
    isUrgent: false,
    isTop: false,
    views: 76,
    applications: 2,
    remainingDays: 3,
    lastRefreshTime: "2024-01-15 08:45",
  },
]);

// 筛选功能
const tabs = ref([
  {
    name: "全部",
    value: "all",
  },
  {
    name: "招聘中",
    value: "active",
  },
  {
    name: "已暂停",
    value: "closed",
  },
]);
const currentTab = ref(0);

const handleTabClick = (e: any) => {
  if (e.index !== undefined) {
    currentTab.value = e.index;
  }
};

const filteredJobList = computed(() => {
  let filtered = jobList.value;

  // 按状态筛选
  const tab = tabs.value[currentTab.value];
  if (tab.value === "active") {
    filtered = filtered.filter((job) => job.status === "active");
  } else if (tab.value === "closed") {
    filtered = filtered.filter((job) => job.status === "closed");
  }

  // 按搜索关键词筛选
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.toLowerCase().trim();
    filtered = filtered.filter(
      (job) =>
        job.title.toLowerCase().includes(keyword) ||
        job.salary.toLowerCase().includes(keyword)
    );
  }

  return filtered;
});

// 获取状态样式类
const getStatusClass = (status: string) => {
  switch (status) {
    case "active":
      return "status-active";
    case "closed":
      return "status-closed";
    case "expired":
      return "status-expired";
    default:
      return "";
  }
};

// 编辑职位
const editJob = (jobId: string) => {
  uni.navigateTo({
    url: `/pages/job/publish?id=${jobId}&edit=true`,
  });
};

// 下线职位
const closeJob = (jobId: string) => {
  uni.showModal({
    title: "提示",
    content: "确定要暂停此职位的招聘吗？",
    success: function (res) {
      if (res.confirm) {
        const index = jobList.value.findIndex((job) => job.id === jobId);
        if (index !== -1) {
          jobList.value[index].status = "closed";
          jobList.value[index].statusText = "已暂停";

          uni.showToast({
            title: "已暂停招聘",
            icon: "success",
          });
        }
      }
    },
  });
};

// 重新上线职位
const reopenJob = (jobId: string) => {
  const index = jobList.value.findIndex((job) => job.id === jobId);
  if (index !== -1) {
    jobList.value[index].status = "active";
    jobList.value[index].statusText = "招聘中";

    uni.showToast({
      title: "已恢复招聘",
      icon: "success",
    });
  }
};

// 查看简历
const viewResumes = (jobId: string) => {
  uni.navigateTo({
    url: `/pages/job/resumes?jobId=${jobId}`,
  });
};

// 推广职位
const boostJob = (jobId: string) => {
  uni.navigateTo({
    url: `/pages/job/boost?jobId=${jobId}`,
  });
};

// 刷新职位
const refreshJob = (jobId: string) => {
  const index = jobList.value.findIndex((job) => job.id === jobId);
  if (index !== -1) {
    // 更新刷新时间
    const now = new Date();
    const timeString = `${now.getFullYear()}-${String(
      now.getMonth() + 1
    ).padStart(2, "0")}-${String(now.getDate()).padStart(2, "0")} ${String(
      now.getHours()
    ).padStart(2, "0")}:${String(now.getMinutes()).padStart(2, "0")}`;
    jobList.value[index].lastRefreshTime = timeString;

    // 模拟增加浏览量
    jobList.value[index].views += Math.floor(Math.random() * 10) + 1;

    uni.showToast({
      title: "刷新成功",
      icon: "success",
    });
  }
};
</script>

<style lang="scss" scoped>
.container {
  // 导航栏样式
  .nav-bar-wrapper {
    background: linear-gradient(
      to right,
      rgba(var(--primary), 0.05),
      rgba(var(--primary), 0.1)
    );
    margin-bottom: 10rpx;

    ::v-deep .uni-nav-bar-text {
      font-weight: 600 !important;
    }
  }

  // 发布按钮样式
  .publish-btn-wrapper {
    padding-top: 10rpx;
  }

  .publish-btn {
    background: linear-gradient(to right, var(--primary-400), var(--primary));
    color: #fff;
    height: 88rpx;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30rpx;
    font-weight: bold;
    box-shadow: 0 4rpx 12rpx rgba(var(--primary), 0.3);
    transition: all 0.2s;

  }

  // 会员状态卡片样式
  .membership-card {
    background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
    border-radius: var(--radius-3);
    padding: var(--spacing-3);
    border: 2rpx solid rgba(var(--primary), 0.1);
    box-shadow: 0 4rpx 16rpx rgba(var(--primary), 0.08);
  }

  .membership-promotion {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .promotion-content {
    flex: 1;
  }

  .promotion-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-2);
  }

  .promotion-icon {
    font-size: 32rpx;
    color: var(--primary);
    margin-right: var(--spacing-2);
  }

  .promotion-title {
    font-size: 32rpx;
    font-weight: 600;

    margin-right: 20rpx;
  }

  .promotion-badge {
    background: linear-gradient(135deg, var(--primary), var(--primary-500));
    color: #fff;
    font-size: 24rpx;
    padding: 4rpx 12rpx;
    border-radius: 12rpx;
    font-weight: 500;
  }

  .promotion-subtitle {
    font-size: 26rpx;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-3);
  }

  .promotion-benefits {
    display: flex;
    flex-direction: column;
    gap: 12rpx;
  }

  .benefit-item {
    display: flex;
    align-items: center;
  }

  .benefit-icon {
    font-size: 24rpx;
    color: var(--primary);
    margin-right: 12rpx;
  }

  .benefit-text {
    font-size: 26rpx;
    color: var(--text-secondary);
  }

  .promotion-action {
    margin-left: var(--spacing);
  }

  .upgrade-btn {
    background: linear-gradient(135deg, var(--primary), var(--primary-500));
    color: #fff;
    padding: 24rpx var(--spacing);
    border-radius: var(--radius-3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 26rpx;
    font-weight: 600;
    box-shadow: 0 4rpx 12rpx rgba(var(--primary), 0.3);
    transition: all 0.2s;

  }

  .upgrade-arrow {
    font-size: 24rpx;
    margin-left: 12rpx;
  }

  // 企业会员仪表板样式
  .enterprise-dashboard {
    background: var(--bg-card);
    border-radius: var(--radius-3);
    padding: var(--spacing);
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
    border: 1rpx solid var(--border-color);
  }

  .dashboard-header {
    margin-bottom: var(--spacing);
  }

  .company-info {
    display: flex;
    align-items: center;
  }

  .company-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: var(--radius-2);
    margin-right: var(--spacing-3);
    border: 2rpx solid var(--border-color);
  }

  .company-details {
    flex: 1;
  }

  .company-name-row {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;
  }

  .company-name {
    font-size: 32rpx;
    font-weight: 600;

    margin-right: 20rpx;
  }

  .membership-tier {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
    padding: 4rpx 12rpx;
    border-radius: 12rpx;
    border: 1rpx solid rgba(var(--primary), 0.2);
  }

  .tier-icon {
    font-size: 20rpx;
    color: var(--primary);
    margin-right: 4rpx;
  }

  .tier-text {
    font-size: 24rpx;
    color: var(--primary);
    font-weight: 500;
  }

  .membership-status {
    font-size: 26rpx;
    color: var(--text-info);
  }

  .benefits-overview {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-3);
  }

  .benefit-card {
    background: var(--bg-tag);
    border-radius: var(--radius-2);
    padding: var(--spacing-3);
    text-align: center;
    border: 1rpx solid var(--border-color);
    transition: all 0.2s;

    &:hover {
      transform: translateY(-2rpx);
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
    }
  }

  .benefit-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-2);
  }

  .benefit-card-icon {
    font-size: 28rpx;
    color: var(--primary);
    margin-right: 12rpx;
  }

  .benefit-title {
    font-size: 26rpx;
    color: var(--text-secondary);
    font-weight: 500;
  }

  .benefit-stats {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .benefit-value {
    font-size: 36rpx;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 4rpx;
  }

  .benefit-label {
    font-size: 24rpx;
    color: var(--text-info);
  }

  // 搜索和筛选区域样式
  .search-filter-section {
    background: var(--bg-card);
    border-radius: var(--radius-3);
    padding: var(--spacing-3);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  }

  .search-container {
    margin-bottom: var(--spacing-2);
  }

  .search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--bg-search);
    border-radius: var(--radius-3);
    padding: 0 var(--spacing-3);
    height: 72rpx;
  }

  .search-icon {
    font-size: 32rpx;
    color: $text-info;
    margin-right: 20rpx;
  }

  .search-input {
    flex: 1;

    background: transparent;
    border: none;
    outline: none;

    &::placeholder {
      color: var(--text-grey);
    }
  }

  .clear-icon {
    font-size: 28rpx;
    color: var(--text-info);
    padding: 12rpx;
    margin-left: 12rpx;

  }

  // 卡片标题样式
  .card-title {
    font-size: 32rpx;
    font-weight: 600;

    display: flex;
    align-items: center;
  }

  .job-count {
    font-size: 26rpx;
    color: var(--primary);
    font-weight: 500;
    flex-shrink: 0;
    margin-left: 20rpx;
  }

  .tabs-container {
    width: 100%;
  }

  // 统计卡片样式
  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20rpx;
    border-radius: 12rpx;
    background-color: #f9f9f9;
    transition: all 0.2s;

    &:hover {
      transform: translateY(-2rpx);
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    }
  }

  .stat-value {
    font-size: 40rpx;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 10rpx;
  }

  .stat-views {
    color: #3b82f6; // 蓝色
  }

  .stat-resumes {
    color: #10b981; // 绿色
  }

  .stat-label {
    font-size: 26rpx;
    color: var(--text-info);
  }

  // 职位列表样式
  .job-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing);
  }

  .job-card {
    background: var(--bg-card);
    border-radius: var(--radius-3);
    padding: var(--spacing);
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
    border: 1rpx solid var(--border-color);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2rpx);
      box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
    }

  }

  .job-card-header {
    margin-bottom: 28rpx;
  }

  .job-title-section {
    width: 100%;
  }

  .job-title-row {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: var(--spacing-2);
  }

  .job-title {
    font-size: 36rpx;
    font-weight: 600;

    margin-right: var(--spacing-3);
  }

  .job-tags {
    display: flex;
    align-items: center;
    gap: 12rpx;
    flex-shrink: 0;
  }

  .job-tag {
    display: flex;
    align-items: center;
    padding: 4rpx 10rpx;
    border-radius: var(--radius);
    font-size: 24rpx;
    font-weight: 500;
    line-height: 1.2;
  }

  .tag-icon {
    font-size: 20rpx;
    margin-right: 4rpx;
  }

  .urgent-tag {
    background: rgba(var(--text-red), 0.1);
    color: var(--text-red);
    border: 1rpx solid rgba(var(--text-red), 0.2);
  }

  .top-tag {
    background: rgba(var(--text-yellow), 0.1);
    color: var(--text-yellow);
    border: 1rpx solid rgba(var(--text-yellow), 0.2);
  }

  .job-salary-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .job-salary {
    font-size: 32rpx;
    color: var(--primary);
    font-weight: 600;
  }

  // 职位状态样式
  .job-status {
    font-size: $font-size-sm;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    font-weight: 500;
    flex-shrink: 0;
  }

  .status-active {
    background-color: rgba($text-green, 0.1);
    color: $text-green;
    border: 1rpx solid rgba($text-green, 0.2);
  }

  .status-closed {
    background-color: rgba($text-grey, 0.1);
    color: var(--text-grey);
    border: 1rpx solid rgba($text-grey, 0.2);
  }

  .status-expired {
    background-color: rgba($text-red, 0.1);
    color: $text-red;
    border: 1rpx solid rgba($text-red, 0.2);
  }

  // 职位统计信息样式
  .job-metrics {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-3);
    padding: 20rpx 0;
    border-top: 1rpx solid var(--border-color);
    border-bottom: 1rpx solid var(--border-color);
  }

  .metric-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
  }

  .metric-icon {
    font-size: 28rpx;
    color: var(--text-info);
    margin-bottom: 4rpx;
  }

  .metric-value {
    font-size: 32rpx;
    font-weight: 600;

    margin-bottom: 2rpx;
  }

  .metric-label {
    font-size: 24rpx;
    color: var(--text-info);
  }

  // 刷新信息样式
  .job-refresh-info {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-3);
    padding: var(--spacing-2) 20rpx;
    background: var(--bg-tag);
    border-radius: var(--radius);
  }

  .refresh-icon {
    font-size: 24rpx;
    color: var(--text-info);
    margin-right: 12rpx;
  }

  .refresh-text {
    font-size: 24rpx;
    color: var(--text-info);
  }

  // 操作按钮样式
  .job-actions {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-2);
    margin-top: var(--spacing-3);
  }

  .action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--radius);
    font-size: 26rpx;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 1rpx solid transparent;
    min-height: 60rpx;
    flex: 1;


  }

  .action-icon {
    font-size: 24rpx;
    margin-right: var(--spacing);
  }

  .secondary-btn {
    background-color: var(--bg-tag);
    color: var(--text-secondary);
    border-color: var(--border-color);

  }

  .primary-btn {
    background: linear-gradient(
      135deg,
      rgba(var(--primary), 0.1),
      rgba(var(--primary), 0.15)
    );
    color: var(--primary);
    border-color: rgba(var(--primary), 0.2);
    font-weight: 600;

  }

  .success-btn {
    background: linear-gradient(
      135deg,
      rgba(var(--text-green), 0.1),
      rgba(var(--text-green), 0.15)
    );
    color: var(--text-green);
    border-color: rgba(var(--text-green), 0.2);


  }

  .warning-btn {
    background: linear-gradient(
      135deg,
      rgba(var(--text-yellow), 0.1),
      rgba(var(--text-yellow), 0.15)
    );
    color: var(--text-yellow);
    border-color: rgba(var(--text-yellow), 0.2);

  }

  // 空状态样式
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60rpx;
    min-height: 300rpx;
  }

  .empty-icon {
    font-size: 80rpx;
    color: var(--text-grey);
    margin-bottom: 20rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: var(--text-grey);
    margin-bottom: 30rpx;
  }

  .empty-btn {
    display: inline-block;
    padding: 16rpx 40rpx;
    border-radius: 30rpx;
    font-size: 28rpx;
    color: #fff;
    background: linear-gradient(to right, var(--primary), var(--primary-500));
    box-shadow: 0 4rpx 12rpx rgba(var(--primary), 0.3);
    transition: all 0.2s;

  }
}
</style>
