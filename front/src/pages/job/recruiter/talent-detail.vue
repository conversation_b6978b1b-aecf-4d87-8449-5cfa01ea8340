<template>
  <view class="container">
    <view class="header-bg"></view>
    <uni-nav-bar
      fixed
      statusBar="true"
      :border="false"
      left-icon="back"
      @clickLeft="goBack"
      backgroundColor="transparent"
    />

    <!-- 可滚动内容区域 -->
    <scroll-view scroll-y enable-flex class="scroll-content">
      <!-- 头部固定区域 -->
      <view class="card mx-20rpx">
        <view class="info-header">
          <image :src="talentInfo.avatar" class="avatar" />
          <view class="info-name-section">
            <text class="name">{{ talentInfo.name }}</text>
            <view class="meta-info">
              <text>{{ talentInfo.age }}岁</text>
              <text class="divider">|</text>
              <text>{{ talentInfo.experience }}</text>
              <text class="divider">|</text>
              <text>{{ talentInfo.education }}</text>
            </view>
          </view>
        </view>
        <view class="info-footer">
          <view class="job-intention">
            <text class="i-carbon-bullhorn icon"></text>
            <text>求职意向: {{ talentInfo.intention.position }}</text>
          </view>
          <view class="expected-salary">
            <text>期望薪资: {{ talentInfo.intention.salary }}</text>
          </view>
        </view>
      </view>
      <view class="content-wrapper">
        <Card title="技能标签">
          <view class="skills-container">
            <view
              v-for="(skill, index) in talentInfo.skills"
              :key="index"
              class="skill-tag"
            >
              {{ skill }}
            </view>
          </view>
        </Card>

        <Card title="工作经历">
          <view
            v-for="(exp, index) in talentInfo.workExperience"
            :key="index"
            class="experience-item"
          >
            <view class="item-header">
              <text class="position">{{ exp.position }}</text>
              <text class="duration">{{ exp.duration }}</text>
            </view>
            <view class="company-name">{{ exp.company }}</view>
            <text class="description">{{ exp.description }}</text>
          </view>
        </Card>

        <Card title="教育背景">
          <view
            v-for="(edu, index) in talentInfo.educationBackground"
            :key="index"
            class="education-item"
          >
            <view class="item-header">
              <text class="school">{{ edu.school }}</text>
              <text class="duration">{{ edu.duration }}</text>
            </view>
            <view class="major-degree">{{ edu.major }} · {{ edu.degree }}</view>
          </view>
        </Card>
      </view>
      <view class="h-500rpx border-red-500 w-full"> </view>
    </scroll-view>

    <!-- 底部固定操作栏 -->
    <view class="bottom-actions">
      <view class="action-btn secondary">
        <text class="i-carbon-download icon"></text>
        <text>简历</text>
      </view>
      <view class="action-btn primary interactive-scale">
        <text class="i-carbon-chat icon"></text>
        <text>立即沟通</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import Card from "@/components/common/Card.vue";

const talentInfo = ref({
  id: "talent1",
  name: "张三",
  avatar: "https://picsum.photos/seed/talent1/120/120",
  age: 28,
  experience: "3年经验",
  education: "本科",
  intention: {
    position: "前端开发工程师",
    salary: "15-25K",
  },
  skills: ["Vue.js", "React", "TypeScript", "Node.js", "Webpack"],
  workExperience: [
    {
      company: "ABC科技有限公司",
      position: "前端开发工程师",
      duration: "2020.07 - 至今",
      description:
        "负责核心业务产品的前端开发、性能优化和技术选型。主导重构项目，提升加载速度50%。",
    },
    {
      company: "XYZ工作室",
      position: "Web前端实习生",
      duration: "2019.07 - 2020.06",
      description: "参与多个外包项目，负责页面制作和部分交互逻辑实现。",
    },
  ],
  educationBackground: [
    {
      school: "某某大学",
      major: "计算机科学与技术",
      degree: "本科",
      duration: "2016.09 - 2020.06",
    },
  ],
});

const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;

  .header-bg {
    position: absolute;
    top: 0;
    left: 0;
    background: linear-gradient(135deg, var(--primary-200), var(--primary-500));
    width: 100%;
    height: 320rpx;
    border-bottom-left-radius: 120rpx;
    border-bottom-right-radius: 120rpx;
  }

  .info-header {
    display: flex;
    align-items: center;
    gap: 24rpx;
    padding-bottom: 24rpx;
    border-bottom: 1rpx solid #f5f5f5;
  }

  .avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    border: 4rpx solid #fff;
  }

  .info-name-section {
    .name {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 12rpx;
    }
    .meta-info {
      font-size: 26rpx;
      color: #666;
      .divider {
        margin: 0 12rpx;
        color: #e0e0e0;
      }
    }
  }

  .info-footer {
    padding-top: 24rpx;
    font-size: 26rpx;
    color: #555;
    .job-intention {
      display: flex;
      align-items: center;
      gap: 8rpx;
      margin-bottom: 8rpx;
      .icon {
        color: #ff6d00;
      }
    }
    .expected-salary {
      color: #ff6d00;
      font-weight: 500;
    }
  }

  .scroll-content {
    margin-top: 40rpx;
    background-color: transparent;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20rpx;
  }

  .skills-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;

    .skill-tag {
      background-color: #f7f7f7;
      color: #555;
      padding: 8rpx 16rpx;
      border-radius: 8rpx;
      font-size: 24rpx;
    }
  }

  .experience-item,
  .education-item {
    position: relative;
    padding-left: 32rpx;
    &:not(:last-child) {
      padding-bottom: 32rpx;
      border-left: 2rpx solid #e0e0e0;
    }
    &:last-child {
      border-left: 2rpx solid transparent;
    }

    &::before {
      content: "";
      position: absolute;
      left: -8rpx;
      top: 8rpx;
      width: 16rpx;
      height: 16rpx;
      border-radius: 50%;
      background-color: #ffc9a7;
      border: 2rpx solid #ff6d00;
    }
  }

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8rpx;

    .position,
    .school {
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
    }

    .duration {
      font-size: 24rpx;
      color: #999;
    }
  }

  .company-name,
  .major-degree {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 12rpx;
  }

  .description {
    font-size: 26rpx;
    color: #555;
    line-height: 1.6;
  }

  .bottom-actions {
    display: flex;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.06);
    padding: 20rpx 20rpx 60rpx 20rpx;
    align-items: center;
    gap: 24rpx;

    .action-btn {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 80rpx;
      border-radius: 40rpx;
      font-size: 28rpx;
      font-weight: 500;
      gap: 8rpx;

      &.secondary {
        background-color: #fff1e8;
        color: #ff6d00;
      }

      &.primary {
        background: linear-gradient(90deg, #ff8c42, #ff6d00);
        color: #fff;
      }
    }
  }
}
</style>
