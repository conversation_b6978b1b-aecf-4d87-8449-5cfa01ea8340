<template>
  <view class="preview-container">
    <!-- 预览内容 -->
    <scroll-view scroll-y enable-flex class="preview-content">
      <!-- 个人信息卡片 -->
      <view class="card personal-section">
        <view class="personal-header">
          <view class="personal-info">
            <text class="name">{{ resume.name }}</text>
            <view class="basic-info">
              <text class="info-item">{{ resume.gender }}</text>
              <text class="dot-text">·</text>
              <text class="info-item"
                >{{ calculateAge(resume.birthday) }}岁</text
              >
              <text class="dot-text">·</text>
              <text class="info-item">{{ resume.education }}</text>
            </view>
            <view class="contact-info">
              <text class="i-carbon-phone contact-icon"></text>
              <text class="contact-text">{{
                formatPhoneNumber(resume.phone)
              }}</text>
            </view>
            <view class="job-intent">求职意向：{{ resume.jobIntent }}</view>
          </view>

          <view class="avatar-section">
            <image
              class="preview-avatar"
              :src="resume.avatar || getDefaultAvatar(resume.gender)"
              mode="aspectFill"
            />
          </view>
        </view>
      </view>

      <!-- 教育经历 -->
      <view v-if="resume.educations.length" class="card">
        <view class="section-title">
          <text class="i-carbon-education section-icon"></text>
          <text>教育经历</text>
        </view>
        <view class="section-content">
          <view
            v-for="(edu, index) in resume.educations"
            :key="index"
            class="education-item"
            :class="{ 'has-border': index < resume.educations.length - 1 }"
          >
            <view class="edu-header">
              <text class="school-name">{{ edu.school }}</text>
              <text class="edu-time">{{ edu.time }}</text>
            </view>
            <view class="edu-details">
              <text class="major-name">{{ edu.major }}</text>
              <view class="degree-tag">{{ edu.degree }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 工作经验 -->
      <view v-if="resume.experiences.length" class="card">
        <view class="section-title">
          <text class="i-carbon-workspace section-icon"></text>
          <text>工作经验</text>
        </view>
        <view class="section-content">
          <view
            v-for="(exp, index) in resume.experiences"
            :key="index"
            class="experience-item"
            :class="{ 'has-border': index < resume.experiences.length - 1 }"
          >
            <view class="exp-header">
              <text class="company-name">{{ exp.company }}</text>
              <text class="exp-time">{{ exp.time }}</text>
            </view>
            <view class="job-position">{{ exp.title }}</view>

            <view v-if="exp.responsibilities.length" class="responsibilities">
              <text class="resp-title">工作内容：</text>
              <view
                v-for="(resp, idx) in exp.responsibilities"
                :key="idx"
                class="resp-item"
              >
                <text class="resp-dot">•</text>
                <text class="resp-text">{{ resp }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 专业技能 -->
      <view v-if="resume.skills.length || resume.skillDescription" class="card">
        <view class="section-title">
          <text class="i-carbon-tools section-icon"></text>
          <text>专业技能</text>
        </view>
        <view class="section-content">
          <view v-if="resume.skills.length" class="skills-tags">
            <view
              v-for="(skill, index) in resume.skills"
              :key="index"
              class="skill-tag"
            >
              {{ skill }}
            </view>
          </view>
          <view v-if="resume.skillDescription" class="skill-description">
            {{ resume.skillDescription }}
          </view>
        </view>
      </view>

      <!-- 自我评价 -->
      <view v-if="resume.selfEvaluation" class="card">
        <view class="section-title">
          <text class="i-carbon-user-avatar section-icon"></text>
          <text>自我评价</text>
        </view>
        <view class="section-content">
          <view class="evaluation-text">
            {{ resume.selfEvaluation }}
          </view>
        </view>
      </view>

      <!-- 底部空间 -->
      <view class="bottom-space"></view>

      <!-- 底部占位空间，防止内容被底部按钮遮挡 -->
      <view class="bottom-placeholder"></view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="preview-actions">
      <view class="action-btn outline-btn" @tap="editResume">
        <text class="i-carbon-edit"></text>
        <text>编辑简历</text>
      </view>
      <view class="action-btn primary-btn interactive-scale" @tap="shareResume">
        <text class="i-carbon-share"></text>
        <text>分享简历</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { formatPhoneNumber } from "@/utils";

// 简历数据
const resume = ref({
  name: "张小明",
  gender: "男",
  birthday: "1995-01-15",
  phone: "13812345678",
  jobIntent: "前端开发工程师",
  experience: "3年",
  education: "本科",
  avatar: "",

  educations: [
    {
      school: "浙江大学",
      major: "计算机科学与技术",
      degree: "本科",
      time: "2016.09-2020.07",
    },
  ],

  experiences: [
    {
      company: "杭州某科技有限公司",
      title: "前端开发工程师",
      time: "2020.07-至今",
      responsibilities: [
        "负责公司电商平台的前端页面开发与维护",
        "参与项目需求分析，技术选型，架构设计",
        "基于uni-app开发跨平台小程序，提升用户体验",
      ],
    },
  ],

  skills: ["HTML/CSS", "JavaScript", "Vue.js", "uni-app", "TypeScript"],
  skillDescription:
    "熟练掌握前端开发技术栈，包括HTML/CSS/JavaScript，熟悉Vue.js框架及其生态，有丰富的小程序和H5开发经验。",
  selfEvaluation:
    "性格开朗，责任心强，善于团队合作。具有良好的沟通能力和解决问题的能力，能够快速学习新技术并应用到实际项目中。",
});

// 获取默认头像
const getDefaultAvatar = (gender: string) => {
  return gender === "女"
    ? "/static/images/avatar-female.png"
    : "/static/images/avatar-male.png";
};

// 计算年龄
const calculateAge = (birthday: string): number => {
  if (!birthday) return 0;
  const birthDate = new Date(birthday);
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }
  return age;
};

// 编辑简历
const editResume = () => {
  uni.navigateTo({
    url: "/pages/job/resume-edit",
  });
};

// 分享简历
const shareResume = () => {
  uni.showShareMenu({
    withShareTicket: true,
    success: () => {
      uni.showToast({
        title: "分享成功",
        icon: "success",
      });
    },
    fail: () => {
      uni.showToast({
        title: "分享功能暂不可用",
        icon: "none",
      });
    },
  });
};
</script>

<style lang="scss" scoped>
.preview-container {
  min-height: 100vh;
  background-color: var(--bg-page);
}

.preview-content {
  padding: 0 20rpx;
  height: calc(100vh - 44px - 88rpx - env(safe-area-inset-bottom));
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.card {
  margin-top: 20rpx;
}

.personal-section {
  background: linear-gradient(135deg, var(--bg-card) 0%, #f8f9ff 100%);
}

.personal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.personal-info {
  flex: 1;
  margin-right: var(--spacing);

  .name {
    font-size: 40rpx;
    font-weight: 600;

    margin-bottom: var(--spacing-3);
  }

  .basic-info {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-3);
    flex-wrap: wrap;

    .info-item {
      font-size: 30rpx;
      color: var(--text-secondary);
    }

    .dot-text {
      font-size: 30rpx;
      color: var(--text-info);
      margin: 0 12rpx;
    }
  }

  .contact-info {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-3);

    .contact-icon {
      font-size: 30rpx;
      color: var(--text-info);
      margin-right: var(--spacing-2);
    }

    .contact-text {
      color: var(--text-info);
    }
  }

  .job-intent {
    color: var(--text-secondary);
    background-color: var(--bg-primary-light);
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--radius-2);
    display: inline-block;
  }
}

.avatar-section {
  flex-shrink: 0;

  .preview-avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    border: 3rpx solid var(--bg-card);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  }
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  font-size: 36rpx;
  font-weight: 500;

  .section-icon {
    font-size: 36rpx;
    color: var(--primary);
    margin-right: var(--spacing-3);
  }
}

.education-item,
.experience-item {
  padding: var(--spacing) 0;

  &.has-border {
    border-bottom: 1rpx solid var(--border-color);
  }

  &:last-child {
    padding-bottom: 0;
  }
}

.edu-header,
.exp-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;

  .school-name,
  .company-name {
    font-size: 32rpx;
    font-weight: 600;

    flex: 1;
  }

  .edu-time,
  .exp-time {
    color: var(--text-info);
    flex-shrink: 0;
  }
}

.edu-details {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .major-name {
    color: var(--text-secondary);
    flex: 1;
  }

  .degree-tag {
    background-color: var(--bg-primary-light);
    color: var(--primary);
    font-size: 26rpx;
    padding: 12rpx 20rpx;
    border-radius: var(--radius-2);
    font-weight: 500;
  }
}

.job-position {
  color: var(--text-secondary);
  margin-bottom: 28rpx;
}

.responsibilities {
  background-color: var(--bg-tag);
  border-radius: var(--radius-2);
  padding: 28rpx;

  .resp-title {
    color: var(--text-info);
    font-weight: 500;
    margin-bottom: 20rpx;
    display: block;
  }

  .resp-item {
    display: flex;
    margin-bottom: var(--spacing-2);

    .resp-dot {
      color: var(--primary);
      margin-right: 20rpx;
      flex-shrink: 0;
    }

    .resp-text {
      color: var(--text-secondary);
      line-height: 1.6;
      flex: 1;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.skills-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing);

  .skill-tag {
    background-color: var(--bg-tag);
    color: var(--text-secondary);

    padding: 20rpx 28rpx;
    border-radius: var(--radius-2);
    border: 1rpx solid var(--border-color);
  }
}

.skill-description,
.evaluation-text {
  color: var(--text-secondary);
  line-height: 1.8;
  background-color: var(--bg-tag);
  padding: var(--spacing);
  border-radius: var(--radius-2);
}

.bottom-space {
  height: 120rpx;
}

.bottom-placeholder {
  height: 120rpx;
}

.preview-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  gap: var(--spacing);
  padding: var(--spacing);
  background-color: var(--bg-card);
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);
  padding-bottom: env(safe-area-inset-bottom);

  .action-btn {
    flex: 1;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20rpx;
    border-radius: 44rpx;

    font-weight: 500;
    transition: all 0.2s;

  }

  .outline-btn {
    border: 1rpx solid var(--primary);
    color: var(--primary);
    background-color: var(--bg-primary-light);


  }

  .primary-btn {
    background: linear-gradient(135deg, var(--primary), var(--primary-500));
    color: var(--text-inverse);
    box-shadow: 0 4rpx 12rpx rgba(var(--primary), 0.3);


  }
}
</style>
