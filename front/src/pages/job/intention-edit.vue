<template>
  <view class="container">
    <!-- 垂直选项卡 -->
    <view class="vtabs-container">
      <uv-vtabs
        :list="jobCategories"
        keyName="name"
        :chain="true"
        :barItemActiveStyle="{ color: 'var(--primary)', fontWeight: '500' }"
        :bar-item-active-line-style="{ background: 'var(--primary)' }"
        @change="onVtabsChange"
      >
        <template v-for="(category, index) in jobCategories" :key="index">
          <uv-vtabs-item :index="index" :name="category.name">
            <view class="job-grid-container">
              <view class="job-grid-header">
                <text class="job-grid-header-text">{{ category.name }}</text>
              </view>
              <view class="job-grid">
                <view
                  v-for="(job, jobIndex) in category.subCategories"
                  :key="jobIndex"
                  class="job-item"
                  :class="{ 'job-item-selected': isJobSelected(job) }"
                  @tap="toggleJobSelection(job)"
                >
                  {{ job.name }}
                </view>
              </view>
            </view>
          </uv-vtabs-item>
        </template>
      </uv-vtabs>
    </view>
    <!-- 已选职位底部固定区域 -->
    <view class="selected-jobs-fixed">
      <view class="selected-count">
        <text>已选择 {{ selectedJobs.length }}/5</text>
      </view>
      <scroll-view scroll-x class="selected-jobs-scroll" show-scrollbar="false">
        <view class="selected-jobs-list flex">
          <view
            v-for="(job, index) in selectedJobs"
            :key="index"
            class="selected-job-item"
          >
            <text class="job-name">{{ job.name }}</text>
            <text
              class="i-carbon-close-filled remove-icon"
              @tap="removeJob(index)"
            ></text>
          </view>
        </view>
      </scroll-view>
      <view class="save-btn interactive-scale" @tap="saveIntention">保存</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { jobCategories } from "@/constants/static/job";
const selectedJobs = ref([]);
const MAX_SELECTION = 5;
const windowHeight = ref(0);
const navBarHeight = ref(88); // 默认导航栏高度，单位px
const safeAreaBottomHeight = ref(0);

// 返回上一页
const vtabsHeight = () => {
  return `calc(100vh - 120px)`;
};

// 监听垂直选项卡变化
const onVtabsChange = (index) => {
  console.log("当前选中分类：", jobCategories[index].name);
};

// 获取窗口高度
const getSystemInfo = () => {
  const systemInfo = uni.getSystemInfoSync();
  windowHeight.value = systemInfo.windowHeight;
  // 获取安全区域底部高度（主要用于iPhoneX等机型）
  safeAreaBottomHeight.value = systemInfo.safeAreaInsets
    ? systemInfo.safeAreaInsets.bottom
    : 0;
};

// 页面加载时从本地存储读取已选职位
onLoad(() => {
  getSystemInfo();
  try {
    const storedJobs = uni.getStorageSync("intentionJobs");
    if (storedJobs) {
      selectedJobs.value = JSON.parse(storedJobs);
    }
  } catch (e) {
    console.error("读取意向职位失败", e);
  }
});

// 检查职位是否已被选择
const isJobSelected = (job) => {
  return selectedJobs.value.some((item) => item.id === job.id);
};

// 切换职位选择状态
const toggleJobSelection = (job) => {
  const index = selectedJobs.value.findIndex((item) => item.id === job.id);

  if (index > -1) {
    // 已选中，则取消选择
    selectedJobs.value.splice(index, 1);
  } else {
    // 未选中，判断是否达到上限
    if (selectedJobs.value.length >= MAX_SELECTION) {
      uni.showToast({
        title: `最多只能选择${MAX_SELECTION}个意向职位`,
        icon: "none",
      });
      return;
    }
    // 添加到已选列表
    selectedJobs.value.push(job);
  }
};

// 移除已选职位
const removeJob = (index) => {
  selectedJobs.value.splice(index, 1);
};

// 保存意向职位设置
const saveIntention = () => {
  try {
    uni.setStorageSync("intentionJobs", JSON.stringify(selectedJobs.value));
    uni.showToast({
      title: "保存成功",
      icon: "success",
    });
    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  } catch (e) {
    console.error("保存意向职位失败", e);
    uni.showToast({
      title: "保存失败，请重试",
      icon: "none",
    });
  }
};
</script>

<style lang="scss" scoped>
.container {
  background-color: var(--bg-page);
  min-height: 100vh;
  position: relative;

  // 垂直选项卡容器
  .vtabs-container {
    background-color: #fff;
    padding-bottom: 320rpx;
  }
  .job-grid-container {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
    padding: 20rpx 20rpx 0 20rpx;

    .job-grid-header {
      font-size: 30rpx;
      font-weight: 500;
    }

    // 职位网格
    .job-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 20rpx;

      .job-item {
        padding: 16rpx 28rpx;
        background-color: var(--bg-tag);
        border: 1px solid transparent;
        border-radius: 8rpx;
        font-size: 28rpx;
        color: var(--text-secondary);
        transition: all 0.2s;

        &-selected {
          background-color: var(--bg-primary-light);
          color: var(--primary);
          border: 1px solid var(--primary);
        }
      }
    }
  }
  // 底部固定的已选职位区域
  .selected-jobs-fixed {
    height: 300rpx;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    z-index: 100;
    padding: 20rpx 20rpx 40rpx 20rpx;

    .selected-count {
      padding: 10rpx 0;
      font-size: 26rpx;
      color: var(--text-secondary);
    }

    .selected-jobs-scroll {
      width: 100%;
      white-space: nowrap;
      height: 80rpx;
      margin-bottom: 20rpx;

      .selected-jobs-list {
        padding: 10rpx 0;
      }

      .selected-job-item {
        display: inline-flex;
        align-items: center;
        padding: 8rpx 20rpx;
        background-color: var(--bg-primary-light);
        border-radius: 30rpx;
        margin-right: 20rpx;

        .job-name {
          font-size: 28rpx;

          margin-right: 16rpx;
        }

        .remove-icon {
          font-size: 28rpx;
          color: var(--primary);
        }
      }
    }

    .save-btn {
      background: linear-gradient(
        90deg,
        var(--primary-500),
        var(--primary-700)
      );
      height: 88rpx;
      border-radius: 45rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 32rpx;
      font-weight: 500;
      transition: all 0.2s;

    }
  }
}
</style>
