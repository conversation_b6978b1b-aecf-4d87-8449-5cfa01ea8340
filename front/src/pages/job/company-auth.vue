<template>
  <view class="auth-container">
    <!-- 身份选择 -->
    <view class="identity-selection bg-white mx-20rpx mt-30rpx rounded-lg">
      <view class="section-title p-30rpx border-bottom">
        <text class="text-32rpx font-500">选择认证身份</text>
      </view>

      <view class="identity-options p-20rpx">
        <view
          v-for="(item, index) in identityTypes"
          :key="index"
          class="identity-option rounded-lg p-30rpx mb-20rpx"
          :class="{ 'active-option': selectedIdentity === item.type }"
          @tap="selectIdentity(item.type)"
        >
          <view class="flex items-center">
            <view
              class="identity-icon mr-20rpx flex items-center justify-center"
            >
              <text :class="item.icon"></text>
            </view>
            <view class="flex-1">
              <view class="text-30rpx font-500">{{ item.name }}</view>
              <view class="text-26rpx text-grey mt-10rpx">{{
                item.description
              }}</view>
            </view>
            <text
              class="i-carbon-checkmark-outline text-40rpx"
              :class="
                selectedIdentity === item.type ? 'text-primary' : 'text-grey'
              "
            ></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 企业认证表单 -->
    <view
      class="auth-form bg-white mx-20rpx mt-30rpx rounded-lg"
      v-if="selectedIdentity === 'enterprise'"
    >
      <view class="section-title p-30rpx border-bottom">
        <text class="text-32rpx font-500">企业认证资料</text>
      </view>

      <view class="form-content p-30rpx">
        <view class="form-item border-bottom pb-20rpx mb-30rpx">
          <view class="form-label mb-20rpx">
            <text class="text-28rpx">企业名称</text>
            <text class="text-red-500 ml-10rpx">*</text>
          </view>
          <input
            class="form-input text-30rpx"
            placeholder="请输入企业名称"
            v-model="enterpriseForm.name"
          />
        </view>

        <view class="form-item border-bottom pb-20rpx mb-30rpx">
          <view class="form-label mb-20rpx">
            <text class="text-28rpx">统一社会信用代码</text>
            <text class="text-red-500 ml-10rpx">*</text>
          </view>
          <input
            class="form-input text-30rpx"
            placeholder="请输入统一社会信用代码"
            v-model="enterpriseForm.creditCode"
          />
        </view>

        <view class="form-item pb-20rpx mb-30rpx">
          <view class="form-label mb-20rpx">
            <text class="text-28rpx">营业执照</text>
            <text class="text-red-500 ml-10rpx">*</text>
          </view>
          <view
            class="license-upload flex justify-center items-center"
            @tap="uploadLicense"
          >
            <view
              v-if="!enterpriseForm.licenseUrl"
              class="upload-placeholder flex flex-col items-center justify-center"
            >
              <text class="i-carbon-add-alt text-60rpx text-grey"></text>
              <text class="text-26rpx text-grey mt-10rpx">上传营业执照</text>
            </view>
            <image
              v-else
              :src="enterpriseForm.licenseUrl"
              mode="aspectFit"
              class="license-image"
            ></image>
          </view>
          <view class="text-24rpx text-grey mt-10rpx"
            >请上传清晰的营业执照照片或扫描件</view
          >
        </view>
      </view>
    </view>

    <!-- 个体户认证表单 -->
    <view
      class="auth-form bg-white mx-20rpx mt-30rpx rounded-lg"
      v-if="selectedIdentity === 'smallBusiness'"
    >
      <view class="section-title p-30rpx border-bottom">
        <text class="text-32rpx font-500">个体户认证资料</text>
      </view>

      <view class="form-content p-30rpx">
        <view class="form-item border-bottom pb-20rpx mb-30rpx">
          <view class="form-label mb-20rpx">
            <text class="text-28rpx">个体户名称</text>
            <text class="text-red-500 ml-10rpx">*</text>
          </view>
          <input
            class="form-input text-30rpx"
            placeholder="请输入个体户名称"
            v-model="smallBusinessForm.name"
          />
        </view>

        <view class="form-item border-bottom pb-20rpx mb-30rpx">
          <view class="form-label mb-20rpx">
            <text class="text-28rpx">营业执照号码</text>
            <text class="text-red-500 ml-10rpx">*</text>
          </view>
          <input
            class="form-input text-30rpx"
            placeholder="请输入营业执照号码"
            v-model="smallBusinessForm.licenseNumber"
          />
        </view>

        <view class="form-item pb-20rpx mb-30rpx">
          <view class="form-label mb-20rpx">
            <text class="text-28rpx">营业执照</text>
            <text class="text-red-500 ml-10rpx">*</text>
          </view>
          <view
            class="license-upload flex justify-center items-center interactive-scale"
            @tap="uploadSmallBusinessLicense"
          >
            <view
              v-if="!smallBusinessForm.licenseUrl"
              class="upload-placeholder flex flex-col items-center justify-center"
            >
              <text class="i-carbon-add-alt text-60rpx text-grey"></text>
              <text class="text-26rpx text-grey mt-10rpx">上传营业执照</text>
            </view>
            <image
              v-else
              :src="smallBusinessForm.licenseUrl"
              mode="aspectFit"
              class="license-image"
            ></image>
          </view>
          <view class="text-24rpx text-grey mt-10rpx"
            >请上传清晰的营业执照照片或扫描件</view
          >
        </view>
      </view>
    </view>

    <!-- 个人认证表单 -->
    <view
      class="auth-form bg-white mx-20rpx mt-30rpx rounded-lg"
      v-if="selectedIdentity === 'individual'"
    >
      <view class="section-title p-30rpx border-bottom">
        <text class="text-32rpx font-500">个人认证资料</text>
      </view>

      <view class="form-content p-30rpx">
        <view class="form-item border-bottom pb-20rpx mb-30rpx">
          <view class="form-label mb-20rpx">
            <text class="text-28rpx">姓名</text>
            <text class="text-red-500 ml-10rpx">*</text>
          </view>
          <input
            class="form-input text-30rpx"
            placeholder="请输入真实姓名"
            v-model="individualForm.name"
          />
        </view>

        <view class="form-item border-bottom pb-20rpx mb-30rpx">
          <view class="form-label mb-20rpx">
            <text class="text-28rpx">身份证号码</text>
            <text class="text-red-500 ml-10rpx">*</text>
          </view>
          <input
            class="form-input text-30rpx"
            placeholder="请输入身份证号码"
            v-model="individualForm.idNumber"
          />
        </view>

        <view class="form-item pb-20rpx mb-30rpx">
          <view class="form-label mb-20rpx">
            <text class="text-28rpx">身份证照片</text>
            <text class="text-red-500 ml-10rpx">*</text>
          </view>
          <view class="id-cards flex justify-between">
            <view class="id-card-upload" @tap="uploadIdCardFront">
              <view
                v-if="!individualForm.idCardFront"
                class="upload-placeholder flex flex-col items-center justify-center"
              >
                <text class="i-carbon-add-alt text-50rpx text-grey"></text>
                <text class="text-24rpx text-grey mt-10rpx">身份证正面</text>
              </view>
              <image
                v-else
                :src="individualForm.idCardFront"
                mode="aspectFit"
                class="id-card-image"
              ></image>
            </view>
            <view class="id-card-upload" @tap="uploadIdCardBack">
              <view
                v-if="!individualForm.idCardBack"
                class="upload-placeholder flex flex-col items-center justify-center"
              >
                <text class="i-carbon-add-alt text-50rpx text-grey"></text>
                <text class="text-24rpx text-grey mt-10rpx">身份证反面</text>
              </view>
              <image
                v-else
                :src="individualForm.idCardBack"
                mode="aspectFit"
                class="id-card-image"
              ></image>
            </view>
          </view>
          <view class="text-24rpx text-grey mt-10rpx"
            >请上传清晰的身份证正反面照片</view
          >
        </view>
      </view>
    </view>

    <!-- 认证说明 -->
    <view
      class="auth-notes bg-white mx-20rpx mt-30rpx mb-150rpx rounded-lg p-30rpx"
    >
      <view class="section-title mb-20rpx">
        <text class="text-32rpx font-500">认证说明</text>
      </view>
      <view class="text-26rpx text-info leading-loose">
        <view class="mb-16rpx"
          >• 认证信息仅用于资质审核，平台会严格保护您的隐私安全</view
        >
        <view class="mb-16rpx"
          >• 认证通过后可获得平台认证标识，提高招聘信任度</view
        >
        <view class="mb-16rpx">• 一般1-2个工作日完成审核，请耐心等待</view>
        <view>• 认证遇到问题请联系客服：400-123-4567</view>
      </view>
    </view>

    <!-- 底部提交按钮 -->
    <view class="submit-btn-container">
      <view class="submit-btn interactive-scale" @tap="submitAuth">提交认证</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";

// 认证身份类型
const identityTypes = [
  {
    type: "enterprise",
    name: "企业认证",
    icon: "i-carbon-enterprise text-blue-500 text-50rpx",
    description: "适用于已注册的企业法人",
  },
  {
    type: "smallBusiness",
    name: "个体户认证",
    icon: "i-carbon-shop text-green-500 text-50rpx",
    description: "适用于个体工商户",
  },
  {
    type: "individual",
    name: "个人认证",
    icon: "i-carbon-user text-orange-500 text-50rpx",
    description: "适用于个人招聘者",
  },
];

// 选中的身份类型
const selectedIdentity = ref("enterprise");

// 企业认证表单
const enterpriseForm = ref({
  name: "",
  creditCode: "",
  licenseUrl: "",
});

// 个体户认证表单
const smallBusinessForm = ref({
  name: "",
  licenseNumber: "",
  licenseUrl: "",
});

// 个人认证表单
const individualForm = ref({
  name: "",
  idNumber: "",
  idCardFront: "",
  idCardBack: "",
});

// 选择认证身份
const selectIdentity = (type) => {
  selectedIdentity.value = type;
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 上传企业营业执照
const uploadLicense = () => {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      enterpriseForm.value.licenseUrl = res.tempFilePaths[0];
      // TODO: 实际开发中应该将图片上传到服务器
    },
  });
};

// 上传个体户营业执照
const uploadSmallBusinessLicense = () => {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      smallBusinessForm.value.licenseUrl = res.tempFilePaths[0];
      // TODO: 实际开发中应该将图片上传到服务器
    },
  });
};

// 上传身份证正面
const uploadIdCardFront = () => {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      individualForm.value.idCardFront = res.tempFilePaths[0];
      // TODO: 实际开发中应该将图片上传到服务器
    },
  });
};

// 上传身份证反面
const uploadIdCardBack = () => {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      individualForm.value.idCardBack = res.tempFilePaths[0];
      // TODO: 实际开发中应该将图片上传到服务器
    },
  });
};

// 提交认证
const submitAuth = () => {
  let isValid = false;
  let form = null;

  if (selectedIdentity.value === "enterprise") {
    form = enterpriseForm.value;
    isValid = !!form.name && !!form.creditCode && !!form.licenseUrl;
  } else if (selectedIdentity.value === "smallBusiness") {
    form = smallBusinessForm.value;
    isValid = !!form.name && !!form.licenseNumber && !!form.licenseUrl;
  } else if (selectedIdentity.value === "individual") {
    form = individualForm.value;
    isValid =
      !!form.name && !!form.idNumber && !!form.idCardFront && !!form.idCardBack;
  }

  if (!isValid) {
    uni.showToast({
      title: "请填写完整认证信息",
      icon: "none",
    });
    return;
  }

  // TODO: 提交认证信息到服务器

  uni.showToast({
    title: "认证提交成功，请等待审核",
    icon: "success",
    duration: 2000,
    success: () => {
      setTimeout(() => {
        uni.navigateBack();
      }, 2000);
    },
  });
};
</script>

<style lang="scss" scoped>
.auth-container {
  min-height: 100vh;

  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.border-bottom {
  border-bottom: 1rpx solid #f0f0f0;
}

.identity-option {
  border: 1rpx solid #eee;
  transition: all 0.3s;
}

.active-option {
  border-color: var(--primary);
  background-color: rgba(255, 109, 0, 0.05);
}

.identity-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.text-primary {
  color: var(--primary);
}

.form-input {
  width: 100%;
}

.license-upload {
  width: 100%;
  height: 300rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  overflow: hidden;
}

.upload-placeholder {
  width: 100%;
  height: 100%;
}

.license-image {
  width: 100%;
  height: 100%;
}

.id-cards {
  width: 100%;
}

.id-card-upload {
  width: 48%;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  overflow: hidden;
}

.id-card-image {
  width: 100%;
  height: 100%;
}

.submit-btn-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  padding: 30rpx;
  padding-bottom: calc(30rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background-color: var(--primary);
  color: #fff;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
}
</style>
