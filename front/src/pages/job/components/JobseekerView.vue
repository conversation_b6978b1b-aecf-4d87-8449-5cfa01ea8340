<template>
  <view>
    <!-- 添加意向职位提示 -->
    <view v-if="showIntentionTip" class="intention-tip-wrapper">
      <view class="intention-tip">
        <view class="tip-content"> 设置意向职位，精准推荐合适岗位 </view>
        <view class="tip-actions">
          <view class="tip-add" @click="handleAddFromTip"> 添加 </view>
          <view
            class="i-solar:close-circle-outline close-icon"
            @click="closeIntentionTip"
          ></view>
        </view>
      </view>
    </view>
    <!-- 意向职位 -->
    <view v-if="showIntentionWrapper" class="intention-wrapper">
      <scroll-view scroll-x class="intention-scroll" show-scrollbar="false">
        <view class="intention-list flex">
          <template v-if="!hasIntentions">
            <view class="no-intention-placeholder">
              点击右侧"添加"按钮，告诉我们你的求职意向吧
            </view>
          </template>
          <template v-else v-for="(job, index) in intentionJobs" :key="index">
            <Tag
              :text="job.name"
              :isActive="job.active"
              circle
              :activeStyle="{
                backgroundColor: 'var(--bg-primary-light)',
                color: 'var(--primary)',
                border: '1rpx solid var(--primary)',
              }"
              @click="selectIntentionJob(index)"
            />
          </template>
        </view>
      </scroll-view>
      <view class="add-intention" @tap="navigateToEditIntention">
        <text
          class="i-solar:add-circle-outline text-primary font-500 text-42rpx"
        ></text>
        <text class="ml-8rpx">添加</text>
      </view>
    </view>

    <!-- 职位筛选栏 -->
    <view class="filter-section p-20rpx sticky top-180rpx">
      <view class="flex justify-between items-center">
        <view class="filter-tabs flex">
          <view
            v-for="(tab, index) in filterTabs"
            :key="index"
            class="filter-tab mr-30rpx"
            :class="{ 'active-tab': tab.active }"
            @click="handleFilterTabClick(index)"
          >
            <text class="text-30rpx">{{ tab.name }}</text>
          </view>
        </view>
        <view class="filter-more flex items-center interactive-scale" @click="openFilterPopup">
          <text class="text-secondary">筛选</text>
          <text class="i-carbon-filter ml-4rpx text-secondary"></text>
        </view>
      </view>
    </view>

    <!-- 推荐职位 -->
    <view class="recommend-jobs flex-y gap-20rpx px-20rpx">
      <JobItem
        v-for="(job, index) in recommendJobs"
        :key="index"
        :job="job"
        @click="goToJobDetail(job)"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import tuiButton from "@/components/thorui/tui-button/tui-button.vue";
import JobItem from "@/components/job/JobItem.vue";

const emit = defineEmits(["openFilterPopup"]);

// 意向职位
const intentionJobs = ref([]);
const hasIntentions = computed(() => intentionJobs.value.length > 0);

// 控制提示和包装器的可见性
const tipVisible = ref(true);
const wrapperVisibleAfterTip = ref(false);

const showIntentionTip = computed(
  () => !hasIntentions.value && tipVisible.value
);
const showIntentionWrapper = computed(
  () => hasIntentions.value || wrapperVisibleAfterTip.value
);

const closeIntentionTip = () => {
  tipVisible.value = false;
};

const handleAddFromTip = () => {
  tipVisible.value = false;
  wrapperVisibleAfterTip.value = true;
};

// 筛选标签
const filterTabs = ref([
  { name: "推荐", active: true },
  { name: "最新", active: false },
  { name: "附近", active: false },
]);

// 推荐职位
const recommendJobs = ref([
  {
    id: "job1001",
    title: "前端开发工程师",
    isUrgent: true,
    salary: "15-25K·13薪",
    area: "海淀区",
    companyLogo: "https://picsum.photos/seed/company1/80/80",
    tags: ["3-5年", "本科", "前端开发", "Vue", "React"],
    companyName: "字节跳动",
    industry: "互联网",
    publishTime: "1小时前",
  },
  {
    id: "job1002",
    title: "销售经理",
    isUrgent: false,
    salary: "10-15K·底薪+提成",
    area: "朝阳区",
    companyLogo: "https://picsum.photos/seed/company2/80/80",
    tags: ["1-3年", "大专", "销售", "市场拓展"],
    companyName: "新东方教育",
    industry: "教育培训",
    publishTime: "3小时前",
  },
  {
    id: "job1003",
    title: "Java开发工程师",
    isUrgent: true,
    salary: "20-35K·14薪",
    area: "中关村",
    companyLogo: "https://picsum.photos/seed/company3/80/80",
    tags: ["5-10年", "本科", "Java", "微服务", "Spring"],
    companyName: "阿里巴巴",
    industry: "互联网",
    publishTime: "昨天",
  },
  {
    id: "job1004",
    title: "UI设计师",
    isUrgent: false,
    salary: "12-18K·13薪",
    area: "西城区",
    companyLogo: "https://picsum.photos/seed/company4/80/80",
    tags: ["3-5年", "本科", "UI设计", "Photoshop", "Sketch"],
    companyName: "腾讯科技",
    industry: "互联网",
    publishTime: "昨天",
  },
  {
    id: "job1005",
    title: "产品经理",
    isUrgent: false,
    salary: "15-25K·13薪",
    area: "海淀区",
    companyLogo: "https://picsum.photos/seed/company5/80/80",
    tags: ["3-5年", "本科", "产品经理", "需求分析", "项目管理"],
    companyName: "字节跳动",
    industry: "互联网",
    publishTime: "昨天",
  },
]);

// 选择意向职位
const selectIntentionJob = (index: number) => {
  intentionJobs.value.forEach((item, idx) => {
    item.active = idx === index;
  });
  // 根据意向职位过滤列表
};

// 查看更多职位
const showMoreJobs = () => {
  uni.navigateTo({
    url: "/pages/job/category",
  });
};

// 导航到意向职位编辑页面
const navigateToEditIntention = () => {
  uni.navigateTo({
    url: "/pages/job/intention-edit",
    animationType: "slide-in-right",
    animationDuration: 300,
  });
};

// 处理筛选标签点击
const handleFilterTabClick = (index: number) => {
  filterTabs.value.forEach((tab, idx) => {
    tab.active = idx === index;
  });
  // 根据筛选条件获取数据
};

// 打开筛选弹出层
const openFilterPopup = () => {
  emit("openFilterPopup");
};

// 跳转到职位详情页
const goToJobDetail = (job: any) => {
  uni.navigateTo({
    url: "/pages/job/detail?id=" + job.id,
  });
};
</script>

<style lang="scss" scoped>
.intention-tip-wrapper {
  padding: 20rpx 20rpx 0;

  .intention-tip {
    display: flex;
    align-items: center;
    gap: 60rpx;
    // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background: linear-gradient(to right, #f7ecdd, #fce1bf);
    border-radius: 40rpx;
    padding: 16rpx 20rpx;
    position: relative;
    overflow: hidden;

    // 背景装饰
    // &::before {
    //   content: "";
    //   position: absolute;
    //   top: -50%;
    //   right: -20%;
    //   width: 150rpx;
    //   height: 150rpx;
    //   background: rgba(255, 255, 255, 0.1);
    //   border-radius: 50%;
    //   transform: rotate(45deg);
    // }

    // &::after {
    //   content: "";
    //   position: absolute;
    //   bottom: -30%;
    //   left: -10%;
    //   width: 150rpx;
    //   height: 150rpx;
    //   background: rgba(255, 255, 255, 0.08);
    //   border-radius: 50%;
    // }

    .tip-content {
      font-size: 28rpx;
      color: var(--primary-900);
    }

    .tip-actions {
      display: flex;
      align-items: center;
      gap: 60rpx;

      .tip-add {
        width: 102rpx;
        height: 48rpx;
        background: linear-gradient(133deg, var(--primary-400), var(--primary));
        border-radius: 60rpx;
        color: #ffffff;
        font-size: 26rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .close-icon {
        font-size: 36rpx;
        color: #ffffff;
      }
    }
  }
}
.intention-wrapper {
  width: 100%;
  padding: 0 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;

  .add-intention {
    min-width: 180rpx;
    text-align: center;
    flex-shrink: 0;
  }

  .intention-scroll {
    flex: 1;
    white-space: nowrap;
    margin-right: 0;

    .intention-list {
      padding: 20rpx 0;
    }
    .no-intention-placeholder {
      line-height: 1.5;
      padding: 10rpx 0;
      color: var(--text-secondary);
      font-size: 26rpx;
      white-space: normal;
    }
    .intention-active {
      background-color: var(--primary-light);
      color: var(--primary);
      border: 1rpx solid var(--primary);
      font-weight: 500;
    }
  }
}

// 筛选栏样式
.filter-section {
  width: 100%;
  box-sizing: border-box;
  background-color: var(--bg-page);

  .filter-tab {
    position: relative;
    padding-bottom: var(--spacing-2);
    color: var(--text-secondary);
    transition: all 0.2s;

  }

  .active-tab {
    color: var(--primary);
    font-weight: 600;

    &:after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 30rpx;
      height: 6rpx;
      background-color: var(--primary);
      border-radius: var(--radius);
    }
  }

  .filter-more {
    padding: var(--spacing-2);
    background-color: var(--bg-tag);
    border-radius: var(--radius-2);
    color: var(--text-secondary);
    transition: all 0.2s;


  }
}
</style>
