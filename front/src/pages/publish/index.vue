<template>
  <view class="publish-page">
    <uni-nav-bar
      title="选择发布类型"
      backgroundColor="#ffffff"
      :border="false"
      :fixed="true"
      statusBar="true"
      leftIcon="back"
      @clickLeft="goBack"
    />
    <view class="publish-content">
      <view
        v-for="category in publishCategories"
        :key="category.title"
        class="category-section"
      >
        <text class="category-title">{{ category.title }}</text>
        <view class="publish-grid">
          <view
            v-for="item in category.items"
            :key="item.name"
            class="publish-item"
            @tap="navigateToPublish(item.path)"
          >
            <view
              class="publish-icon"
              :style="{ backgroundColor: item.color }"
            >
              <text :class="item.icon" class="text-56rpx text-white"></text>
            </view>
            <text class="publish-name">{{ item.name }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const publishCategories = ref([
  {
    title: '房产服务',
    items: [
      {
        name: '发布租房',
        icon: 'i-carbon-home',
        color: '#4b89ff',
        path: '/pages/house/publish/rent',
      },
      {
        name: '发布二手房',
        icon: 'i-carbon-building',
        color: '#3dca9a',
        path: '/pages/house/publish/second-hand',
      },
      {
        name: '发布新房',
        icon: 'i-carbon-apartment',
        color: '#ff7e2e',
        path: '/pages/house/publish/new-house',
      },
      {
        name: '商铺/写字楼',
        icon: 'i-carbon-store',
        color: '#a079ff',
        path: '/pages/house/publish/commercial',
      },
    ],
  },
  {
    title: '招聘求职',
    items: [
      {
        name: '发布职位',
        icon: 'i-carbon-user-multiple',
        color: '#ff5d5d',
        path: '/pages/job/publish',
      },
      {
        name: '企业合作',
        icon: 'i-carbon-handshake',
        color: '#3ac1e5',
        path: '/pages/job/enterprise-cooperation',
      },
    ],
  },
  {
    title: '生活服务',
    items: [
      {
        name: '发布零工',
        icon: 'i-carbon-task-tools',
        color: '#ffb400',
        path: '/pages/gig/publish',
      },
      {
        name: '家政服务',
        icon: 'i-carbon-clean',
        color: '#6d7bff',
        path: '/pages/service/publish/clean',
      },
      {
        name: '维修服务',
        icon: 'i-carbon-tool-kit',
        color: '#9d63ff',
        path: '/pages/service/publish/repair',
      },
      {
        name: '装修服务',
        icon: 'i-carbon-paint-brush',
        color: '#4bd4d4',
        path: '/pages/service/publish/decoration',
      },
      {
        name: '搬家拉货',
        icon: 'i-carbon-van',
        color: '#ff8a44',
        path: '/pages/service/moving',
      },
      {
        name: '生意转让',
        icon: 'i-carbon-partnership',
        color: '#4da1ff',
        path: '/pages/service/publish/transfer',
      },
    ],
  },
  {
    title: '社区分享',
    items: [
      {
        name: '发布动态',
        icon: 'i-carbon-camera',
        color: '#ff6aa7',
        path: '/pages/post/publish',
      },
      {
        name: '发布交友',
        icon: 'i-carbon-favorite',
        color: '#f77',
        path: '/pages/dating/publish',
      },
    ],
  },
]);

const goBack = () => {
  uni.navigateBack();
};

const navigateToPublish = (path: string) => {
  if (!path) {
    uni.showToast({ title: '功能开发中', icon: 'none' });
    return;
  }
  uni.navigateTo({
    url: path,
    fail: () => {
      uni.showToast({ title: '该功能开发中', icon: 'none' });
    },
  });
};
</script>

<style scoped>
.publish-page {
  background-color: #ffffff;
  min-height: 100vh;
}

.publish-content {
  padding: 40rpx 32rpx;
}

.category-section {
  margin-bottom: 48rpx;
}

.category-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
  display: block;
}

.publish-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40rpx 24rpx;
}

.publish-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}



.publish-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
  box-shadow: 0 12rpx 24rpx rgba(0, 0, 0, 0.1);
}

.publish-name {
  font-size: 26rpx;
  color: #333;
  text-align: center;
  line-height: 1.4;
}
</style> 