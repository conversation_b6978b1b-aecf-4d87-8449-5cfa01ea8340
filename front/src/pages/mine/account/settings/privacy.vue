<template>
  <view class="privacy-page">
    <view class="privacy-content">
      <!-- 个人信息隐私 -->
      <view class="privacy-section">
        <view class="section-title">个人信息隐私</view>
        <view class="privacy-list">
          <tui-list-cell arrow padding="var(--spacing-3) var(--spacing-4)">
            <view class="list-item-content">
              <view class="icon-wrapper">
                <text
                  class="i-solar-phone-linear text-20rpx text-green-500"
                ></text>
              </view>
              <view class="item-info">
                <text class="item-title">手机号可见性</text>
                <text class="item-label">设置谁可以看到您的手机号</text>
                <text class="item-note">仅自己可见</text>
              </view>
            </view>
          </tui-list-cell>
          <tui-list-cell :arrow="false" padding="var(--spacing-3) var(--spacing-4)">
            <view class="list-item-content">
              <view class="icon-wrapper">
                <text
                  class="i-solar-user-id-linear text-20rpx text-blue-500"
                ></text>
              </view>
              <view class="item-info">
                <text class="item-title">真实姓名</text>
                <text class="item-label">是否在个人资料中显示真实姓名</text>
              </view>
              <view class="item-right">
                <tui-switch v-model="showRealName"></tui-switch>
              </view>
            </view>
          </tui-list-cell>
        </view>
      </view>

      <!-- 活动隐私 -->
      <view class="privacy-section">
        <view class="section-title">活动隐私</view>
        <view class="privacy-list">
          <tui-list-cell :arrow="false" padding="var(--spacing-3) var(--spacing-4)">
            <view class="list-item-content">
              <view class="icon-wrapper">
                <text
                  class="i-solar-eye-linear text-20rpx text-purple-500"
                ></text>
              </view>
              <view class="item-info">
                <text class="item-title">在线状态</text>
                <text class="item-label">允许其他用户看到您的在线状态</text>
              </view>
              <view class="item-right">
                <tui-switch v-model="showOnlineStatus"></tui-switch>
              </view>
            </view>
          </tui-list-cell>
          <tui-list-cell :arrow="false" padding="var(--spacing-3) var(--spacing-4)">
            <view class="list-item-content">
              <view class="icon-wrapper">
                <text
                  class="i-solar-history-linear text-20rpx text-orange-500"
                ></text>
              </view>
              <view class="item-info">
                <text class="item-title">浏览记录</text>
                <text class="item-label">允许记录您的浏览历史</text>
              </view>
              <view class="item-right">
                <tui-switch v-model="allowBrowsingHistory"></tui-switch>
              </view>
            </view>
          </tui-list-cell>
          <tui-list-cell :arrow="false" padding="var(--spacing-3) var(--spacing-4)">
            <view class="list-item-content">
              <view class="icon-wrapper">
                <text
                  class="i-solar-map-point-linear text-20rpx text-red-500"
                ></text>
              </view>
              <view class="item-info">
                <text class="item-title">位置信息</text>
                <text class="item-label">允许应用获取您的位置信息</text>
              </view>
              <view class="item-right">
                <tui-switch v-model="allowLocation"></tui-switch>
              </view>
            </view>
          </tui-list-cell>
        </view>
      </view>

      <!-- 推荐隐私 -->
      <view class="privacy-section">
        <view class="section-title">推荐与匹配</view>
        <view class="privacy-list">
          <tui-list-cell :arrow="false" padding="var(--spacing-3) var(--spacing-4)">
            <view class="list-item-content">
              <view class="icon-wrapper">
                <text
                  class="i-solar-like-linear text-20rpx text-pink-500"
                ></text>
              </view>
              <view class="item-info">
                <text class="item-title">个性化推荐</text>
                <text class="item-label">基于您的行为为您推荐相关内容</text>
              </view>
              <view class="item-right">
                <tui-switch v-model="personalizedRecommend"></tui-switch>
              </view>
            </view>
          </tui-list-cell>
        </view>
      </view>

      <!-- 数据管理 -->
      <view class="privacy-section">
        <view class="section-title">数据管理</view>
        <view class="privacy-list">
          <tui-list-cell arrow padding="var(--spacing-3) var(--spacing-4)" @click="exportData">
            <view class="list-item-content">
              <view class="icon-wrapper">
                <text
                  class="i-solar-download-linear text-20rpx text-indigo-500"
                ></text>
              </view>
              <view class="item-info">
                <text class="item-title">导出个人数据</text>
                <text class="item-label">下载您在本应用的所有数据</text>
              </view>
            </view>
          </tui-list-cell>
          <tui-list-cell arrow padding="var(--spacing-3) var(--spacing-4)" @click="deleteAccount" class="interactive-scale">
            <view class="list-item-content">
              <view class="icon-wrapper">
                <text
                  class="i-solar-trash-bin-trash-linear text-20rpx text-red-500"
                ></text>
              </view>
              <view class="item-info">
                <text class="item-title">删除账号</text>
                <text class="item-label">永久删除您的账号和所有数据</text>
              </view>
            </view>
          </tui-list-cell>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";

// 隐私设置状态
const showRealName = ref(false);
const showOnlineStatus = ref(true);
const allowBrowsingHistory = ref(true);
const allowLocation = ref(false);
const personalizedRecommend = ref(true);

// 导出个人数据
const exportData = () => {
  uni.showModal({
    title: "导出个人数据",
    content: "我们将为您准备数据包，处理完成后会通过邮件发送给您",
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: "已提交导出申请",
          icon: "success",
        });
      }
    },
  });
};

// 删除账号
const deleteAccount = () => {
  uni.showModal({
    title: "删除账号",
    content: "此操作不可逆，将永久删除您的账号和所有数据。确定要继续吗？",
    confirmColor: "#F44336",
    success: (res) => {
      if (res.confirm) {
        uni.showModal({
          title: "最后确认",
          content: "您确定要删除账号吗？此操作无法撤销！",
          confirmColor: "#F44336",
          success: (res2) => {
            if (res2.confirm) {
              uni.showToast({
                title: "账号删除申请已提交",
                icon: "success",
              });
            }
          },
        });
      }
    },
  });
};
</script>

<style lang="scss" scoped>
.privacy-page {
  background-color: var(--bg-page);
  min-height: 100vh;
}

.privacy-content {
  padding: var(--spacing-4);
}

.privacy-section {
  margin-bottom: var(--spacing-6);

  .section-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    margin-bottom: var(--spacing-3);
    padding-left: var(--spacing);
  }
}

.privacy-list {
  background-color: var(--bg-card);
  border-radius: var(--radius);
  box-shadow: 0 var(--spacing) var(--spacing-2) rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.list-item-content {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) 0;
}

.icon-wrapper {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-3);
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.item-title {
  font-size: var(--font-size-lg);
  font-weight: 500;
  margin-bottom: var(--spacing);
}

.item-label {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-bottom: var(--spacing);
}

.item-note {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.item-right {
  display: flex;
  align-items: center;
  margin-left: var(--spacing-3);
}
</style>