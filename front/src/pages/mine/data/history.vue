<template>
  <view class="history-page">
    <CustomNavBar title="我的足迹" />
    <EmptyResult
      message="这里空空如也"
      sub-message="您的浏览足迹将会记录在这里"
      icon="i-carbon-footprints"
    />
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar.vue'
import EmptyResult from '@/components/common/EmptyResult.vue'
</script>

<style lang="scss" scoped>
.history-page {
  background-color: var(--bg-page);
  min-height: 100vh;
}
</style> 