<template>
  <view class="edit-field-page">
    <view class="input-section">
      <input
        v-model="fieldValue"
        class="field-input"
        :placeholder="fieldConfig.placeholder"
        :maxlength="fieldConfig.maxLength"
      />
      <view class="clear-icon" v-if="fieldValue" @click="fieldValue = ''">
        <text class="i-carbon-close-filled"></text>
      </view>
    </view>

    <!-- 昵称专用：微信一键填充 -->
    <view v-if="field === 'nickname'" class="quick-fill-section">
      <button class="wechat-nickname-btn" @click="fillWithWechatNickname">
        一键填充微信昵称
      </button>
    </view>

    <view class="notice-section">
      <text class="notice-text">{{ fieldConfig.notice }}</text>
    </view>

    <view class="save-btn-container">
      <button
        class="save-btn interactive-scale"
        :disabled="!isFieldValid"
        @click="saveField"
      >
        保存
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { useUserStore } from "@/stores/user";
import { updateUserProfile } from "@/api/user";

const userStore = useUserStore();

// 页面参数
const field = ref("");
const title = ref("");
const fieldValue = ref("");

// 字段配置映射
const fieldConfigs = {
  nickname: {
    placeholder: "请输入2-15个字的昵称",
    maxLength: 15,
    minLength: 2,
    notice: "好的昵称更容易获得大家的关注，支持中英文、数字",
    pattern: /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/,
    errorMessage: "昵称长度应为2-15个字符，支持中英文、数字",
  },
  real_name: {
    placeholder: "请输入您的真实姓名",
    maxLength: 10,
    minLength: 2,
    notice: "真实姓名用于零工申请等需要身份验证的场景，请如实填写",
    pattern: /^[\u4E00-\u9FA5]{2,10}$/,
    errorMessage: "姓名长度应为2-10个字符，只能包含中文",
  }
};

// 当前字段配置
const fieldConfig = computed(() => {
  return fieldConfigs[field.value as keyof typeof fieldConfigs] || fieldConfigs.nickname;
});

// 字段验证
const isFieldValid = computed(() => {
  const trimmed = fieldValue.value.trim();
  const len = trimmed.length;
  const config = fieldConfig.value;
  
  // 长度验证
  if (len < config.minLength || len > config.maxLength) {
    return false;
  }
  
  // 检查是否包含连续空格或特殊字符
  if (fieldValue.value.includes('  ') || fieldValue.value !== fieldValue.value.replace(/\s+/g, ' ')) {
    return false;
  }
  
  // 格式验证
  if (config.pattern && !config.pattern.test(trimmed)) {
    return false;
  }
  
  return true;
});

onLoad((options) => {
  if (options) {
    field.value = options.field || "nickname";
    title.value = options.title || "编辑字段";
    if (options.value) {
      fieldValue.value = decodeURIComponent(options.value);
    }
  }
  
  // 设置页面标题
  uni.setNavigationBarTitle({
    title: title.value
  });
});

// 微信昵称填充（仅昵称字段可用）
const fillWithWechatNickname = () => {
  uni.getUserProfile({
    desc: "用于完善会员资料",
    success: (res) => {
      let wechatNickname = res.userInfo.nickName.slice(0, fieldConfig.value.maxLength);
      fieldValue.value = wechatNickname;
    },
    fail: () => {
      uni.showToast({
        title: "获取微信昵称失败",
        icon: "none",
      });
    },
  });
};

// 保存字段
const saveField = async () => {
  if (!isFieldValid.value) {
    uni.showToast({
      title: fieldConfig.value.errorMessage,
      icon: "none",
    });
    return;
  }

  try {
    uni.showLoading({ title: "保存中..." });
    
    // 构建更新数据
    const updateData = {
      [field.value]: fieldValue.value.trim()
    };
    
    // 调用API更新
    const res = await updateUserProfile(updateData);
    if (res.code === 0) {
      // 更新本地store
      await userStore.fetchUserProfile();
      
      uni.hideLoading();
      uni.showToast({
        title: "保存成功",
        icon: "success",
      });

      setTimeout(() => {
        uni.navigateBack();
      }, 1000);
    } else {
      throw new Error(res.message || "保存失败");
    }
  } catch (error) {
    uni.hideLoading();
    
    // 根据错误类型提供更友好的用户提示
    let errorMessage = "保存失败，请重试";
    if (error instanceof Error) {
      const message = error.message.toLowerCase();
      if (message.includes('network') || message.includes('网络')) {
        errorMessage = "网络连接失败，请检查网络后重试";
      } else if (message.includes('validation') || message.includes('验证')) {
        errorMessage = "输入格式不正确，请检查后重试";
      } else if (message.includes('timeout') || message.includes('超时')) {
        errorMessage = "请求超时，请重试";
      } else if (error.message) {
        errorMessage = error.message;
      }
    }
    
    console.error(`保存${title.value}失败:`, error);
    uni.showToast({
      title: errorMessage,
      icon: "none",
      duration: 3000
    });
  }
};
</script>

<style lang="scss" scoped>
.edit-field-page {
  padding: 32rpx;
  background-color: var(--bg-page);
  min-height: 100vh;
}

.input-section {
  position: relative;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
}

.field-input {
  flex: 1;
  font-size: 30rpx;
  color: var(--text-primary);
}

.clear-icon {
  padding: 8rpx;
  color: var(--text-secondary);
}

.quick-fill-section {
  margin-top: 24rpx;
}

.wechat-nickname-btn {
  background: none;
  color: var(--primary);
  font-size: 26rpx;
  text-align: left;
  padding: 0;
  &::after {
    border: none;
  }
}

.notice-section {
  margin-top: 16rpx;
}

.notice-text {
  font-size: 24rpx;
  color: var(--text-info);
}

.save-btn-container {
  margin-top: 64rpx;
}

.save-btn {
  background-color: var(--primary);
  color: #fff;
  border-radius: 48rpx;
  font-size: 32rpx;

  &[disabled] {
    background-color: var(--primary-disabled);
    color: var(--text-on-disabled);
  }
}
</style>
