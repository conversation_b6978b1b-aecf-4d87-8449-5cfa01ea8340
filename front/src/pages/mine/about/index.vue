<template>
  <view class="about-page">
    <view class="content">
      <!-- App信息 -->
      <view class="app-info">
        <view class="app-logo">
          <image src="/static/logo.png" mode="aspectFit" />
        </view>
        <text class="app-name">本地宝</text>
        <text class="app-version">v1.0.0</text>
        <text class="app-desc">本地生活服务平台，连接你我他</text>
      </view>

      <!-- 核心功能简介 -->
      <view class="features-section">
        <view class="section-title">核心服务</view>
        <view class="features-grid">
          <view class="feature-item">
            <text
              class="i-solar-briefcase-linear text-24rpx text-blue-500"
            ></text>
            <text class="feature-title">求职招聘</text>
          </view>

          <view class="feature-item">
            <text
              class="i-solar-home-2-linear text-24rpx text-green-500"
            ></text>
            <text class="feature-title">房产服务</text>
          </view>

          <view class="feature-item">
            <text class="i-solar-heart-linear text-24rpx text-pink-500"></text>
            <text class="feature-title">相亲交友</text>
          </view>

          <view class="feature-item">
            <text
              class="i-solar-settings-linear text-24rpx text-orange-500"
            ></text>
            <text class="feature-title">本地服务</text>
          </view>
        </view>
      </view>

      <!-- 联系我们 -->
      <view class="contact-section">
        <view class="section-title">联系我们</view>
        <view class="contact-list">
          <tui-list-cell padding="var(--spacing-3) var(--spacing-4)" @click="copyEmail">
            <view class="list-item-content">
              <view class="icon-wrapper">
                <text
                  class="i-solar-letter-linear text-20rpx text-blue-500"
                ></text>
              </view>
              <view class="item-info">
                <text class="item-title">客服邮箱</text>
                <text class="item-note"><EMAIL></text>
              </view>
              <view class="item-right">
                <text
                  class="i-solar-copy-linear text-16rpx text-gray-400"
                ></text>
              </view>
            </view>
          </tui-list-cell>
          <tui-list-cell padding="var(--spacing-3) var(--spacing-4)" @click="copyPhone">
            <view class="list-item-content">
              <view class="icon-wrapper">
                <text
                  class="i-solar-phone-linear text-20rpx text-green-500"
                ></text>
              </view>
              <view class="item-info">
                <text class="item-title">客服热线</text>
                <text class="item-note">************</text>
              </view>
              <view class="item-right">
                <text
                  class="i-solar-copy-linear text-16rpx text-gray-400"
                ></text>
              </view>
            </view>
          </tui-list-cell>
        </view>
      </view>

      <!-- 法律信息 -->
      <view class="legal-section">
        <view class="section-title">法律信息</view>
        <view class="legal-list">
          <tui-list-cell arrow padding="var(--spacing-3) var(--spacing-4)" @click="goToPrivacyPolicy">
            <view class="list-item-content">
              <view class="icon-wrapper">
                <text
                  class="i-solar-document-linear text-20rpx text-indigo-500"
                ></text>
              </view>
              <view class="item-info">
                <text class="item-title">隐私政策</text>
              </view>
            </view>
          </tui-list-cell>
          <tui-list-cell arrow padding="var(--spacing-3) var(--spacing-4)" @click="goToUserAgreement" class="interactive-scale">
            <view class="list-item-content">
              <view class="icon-wrapper">
                <text
                  class="i-solar-file-text-linear text-20rpx text-purple-500"
                ></text>
              </view>
              <view class="item-info">
                <text class="item-title">用户协议</text>
              </view>
            </view>
          </tui-list-cell>
        </view>
      </view>

      <!-- 版权信息 -->
      <view class="copyright">
        <text class="copyright-text">© 2024 本地宝科技有限公司</text>
        <text class="copyright-text">All rights reserved</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from "@/components/CustomNavBar.vue";

// 复制邮箱
const copyEmail = () => {
  uni.setClipboardData({
    data: "<EMAIL>",
    success: () => {
      uni.showToast({
        title: "邮箱已复制",
        icon: "success",
      });
    },
  });
};

// 复制电话
const copyPhone = () => {
  uni.setClipboardData({
    data: "************",
    success: () => {
      uni.showToast({
        title: "电话已复制",
        icon: "success",
      });
    },
  });
};

// 隐私政策
const goToPrivacyPolicy = () => {
  uni.showToast({
    title: "隐私政策页面开发中",
    icon: "none",
  });
};

// 用户协议
const goToUserAgreement = () => {
  uni.showToast({
    title: "用户协议页面开发中",
    icon: "none",
  });
};
</script>

<style lang="scss" scoped>
.about-page {
  background-color: var(--bg-page);
  min-height: 100vh;
}

.content {
  padding: var(--spacing-4);
}

.app-info {
  text-align: center;
  padding: var(--spacing-10) 0 var(--spacing-8);

  .app-logo {
    width: 160rpx;
    height: 160rpx;
    margin: 0 auto var(--spacing-4);
    border-radius: var(--radius-xl);
    overflow: hidden;
    background: var(--bg-card);
    box-shadow: 0 var(--spacing) var(--spacing-3) rgba(0, 0, 0, 0.1);

    image {
      width: 100%;
      height: 100%;
    }
  }

  .app-name {
    display: block;
    font-size: var(--font-size-xxl);
    font-weight: 700;
    margin-bottom: var(--spacing-2);
  }

  .app-version {
    display: block;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-3);
  }

  .app-desc {
    display: block;
    font-size: var(--font-size-xs);
    color: var(--text-info);
    line-height: 1.6;
  }
}

.features-section {
  margin-bottom: var(--spacing-6);

  .section-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    margin-bottom: var(--spacing-4);
    text-align: center;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-3);

    .feature-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-2);
      padding: var(--spacing-4) var(--spacing-3);
      background-color: var(--bg-card);
      border-radius: var(--radius);
      box-shadow: 0 var(--spacing) var(--spacing-2) rgba(0, 0, 0, 0.04);

      .feature-title {
        font-size: var(--font-size-xs);
        font-weight: 500;
      }
    }
  }
}

.contact-section,
.legal-section {
  margin-bottom: var(--spacing-6);

  .section-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    margin-bottom: var(--spacing-3);
    padding-left: var(--spacing);
  }
}

.contact-list,
.legal-list {
  background-color: var(--bg-card);
  border-radius: var(--radius);
  box-shadow: 0 var(--spacing) var(--spacing-2) rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.list-item-content {
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-4);
}

.icon-wrapper {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-3);
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.item-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing);
}

.item-note {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.item-right {
  display: flex;
  align-items: center;
  margin-left: var(--spacing-3);
}

.copyright {
  text-align: center;
  padding: var(--spacing-5) 0;
  border-top: 1rpx solid var(--border-color);

  .copyright-text {
    display: block;
    font-size: var(--font-size-xs);
    color: var(--text-info);
    line-height: 1.6;

    &:first-child {
      margin-bottom: var(--spacing);
    }
  }
}
</style>