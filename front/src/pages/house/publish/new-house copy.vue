<template>
  <view class="new-house-publish-page">
    <uni-nav-bar
      :border="false"
      fixed
      background-color="transparent"
      :status-bar="true"
      title="发布新房"
      left-icon="back"
      @clickLeft="goBack"
    />

    <scroll-view scroll-y class="content-scroll">
      <view class="content">
        <view class="guide-section">
          <text class="guide-title">发布新房信息</text>
          <text class="guide-desc"
            >楼盘、户型、价格等信息详细填写，助力快速销售</text
          >
        </view>

        <view class="form-section">
          <view class="section-title">楼盘基本信息</view>

          <view class="form-item">
            <text class="label">
              <text class="required">*</text>
              楼盘名称
            </text>
            <view class="value-area">
              <input
                v-model="formData.projectName"
                class="input"
                placeholder="请输入楼盘名称"
                @focus="handleInputFocus('projectName')"
              />
            </view>
          </view>

          <view class="form-item">
            <text class="label">
              <text class="required">*</text>
              项目地址
            </text>
            <view class="value-area">
              <input
                v-model="formData.address"
                class="input"
                placeholder="请输入项目地址"
                @focus="handleInputFocus('address')"
              />
            </view>
          </view>

          <view class="form-item">
            <text class="label">
              <text class="required">*</text>
              开发商
            </text>
            <view class="value-area">
              <input
                v-model="formData.developer"
                class="input"
                placeholder="请输入开发商名称"
                @focus="handleInputFocus('developer')"
              />
            </view>
          </view>

          <view class="form-item">
            <text class="label">
              <text class="required">*</text>
              均价
            </text>
            <view class="value-area">
              <input
                v-model="formData.averagePrice"
                class="input"
                placeholder="请输入均价"
                type="number"
                @focus="handleInputFocus('averagePrice')"
              />
              <text class="unit">元/㎡</text>
            </view>
          </view>

          <view class="form-item">
            <text class="label">户型</text>
            <view class="value-area">
              <input
                v-model="formData.houseTypes"
                class="input"
                placeholder="如：89-126㎡ 2-4居"
                @focus="handleInputFocus('houseTypes')"
              />
            </view>
          </view>

          <view class="form-item">
            <text class="label">交房时间</text>
            <view class="value-area">
              <input
                v-model="formData.deliveryTime"
                class="input"
                placeholder="如：2024年底"
                @focus="handleInputFocus('deliveryTime')"
              />
            </view>
          </view>
        </view>

        <view class="form-section">
          <view class="section-title">项目亮点</view>
          <text class="section-tip">选择项目特色标签，吸引更多客户关注</text>

          <view class="tags-container">
            <view
              v-for="tag in projectTags"
              :key="tag"
              class="tag-item"
              :class="{ active: formData.selectedTags.includes(tag) }"
              @tap="toggleTag(tag)"
            >
              {{ tag }}
            </view>
          </view>
        </view>

        <view class="form-section">
          <view class="section-title">
            <text class="required">*</text>
            <text>项目图片</text>
          </view>
          <text class="section-tip"
            >至少上传1张图片，最多9张，展示项目真实面貌</text
          >

          <view class="image-upload-area">
            <view class="upload-grid">
              <view
                v-for="(image, index) in formData.images"
                :key="index"
                class="image-item"
              >
                <image :src="image" mode="aspectFill" class="preview-image" />
                <view class="delete-btn" @tap="deleteImage(index)">
                  <text class="i-carbon-close"></text>
                </view>
              </view>

              <view
                v-if="formData.images.length < 9"
                class="upload-btn"
                @tap="uploadImage"
              >
                <text class="i-carbon-camera upload-icon"></text>
                <text class="upload-text">添加项目图片</text>
                <text class="upload-text sub">展示楼盘亮点</text>
              </view>
            </view>
          </view>
        </view>

        <view class="form-section">
          <view class="section-title">
            <text class="required">*</text>
            <text>项目描述</text>
          </view>
          <text class="section-tip"
            >详细介绍项目位置优势、周边配套等，提升项目吸引力</text
          >

          <view class="textarea-container">
            <textarea
              v-model="formData.description"
              class="description-textarea"
              placeholder="请描述项目的位置优势、周边配套、交通状况等信息"
              maxlength="500"
              show-confirm-bar
              @focus="handleInputFocus('description')"
            />
            <view class="char-count"
              >{{ formData.description?.length || 0 }}/500</view
            >
          </view>
        </view>

        <view class="agreement-section">
          <view class="agreement-item" @tap="toggleAgreement">
            <view class="checkbox" :class="{ checked: agreedToTerms }">
              <text v-if="agreedToTerms" class="i-carbon-checkmark"></text>
            </view>
            <text class="agreement-text">
              我承诺项目信息真实并同意《个人用户新房项目推广服务协议》
            </text>
          </view>
        </view>

        <view class="submit-section">
          <tui-button
            :disabled="!canSubmit"
            @click="submitForm"
            type="primary"
            width="100%"
            height="96rpx"
            :bold="true"
            shape="circle"
          >
            免费发布
          </tui-button>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";

const formData = reactive({
  projectName: "",
  address: "",
  developer: "",
  averagePrice: "",
  houseTypes: "",
  deliveryTime: "",
  selectedTags: [] as string[],
  images: [] as string[],
  description: "",
});

const projectTags = [
  "地铁房",
  "学区房",
  "现房",
  "公园景观",
  "商业配套",
  "医疗配套",
  "精装修",
  "毛坯",
  "低密度",
  "高层",
  "洋房",
  "别墅",
];

const agreedToTerms = ref(false);

const handleInputFocus = (field: string) => {
  console.log(`聚焦到字段: ${field}`);
};

const toggleTag = (tag: string) => {
  const index = formData.selectedTags.indexOf(tag);
  if (index > -1) {
    formData.selectedTags.splice(index, 1);
  } else {
    formData.selectedTags.push(tag);
  }
};

const uploadImage = () => {
  uni.chooseImage({
    count: 9 - formData.images.length,
    sizeType: ["compressed"],
    sourceType: ["camera", "album"],
    success: (res) => {
      if (Array.isArray(res.tempFilePaths)) {
        res.tempFilePaths.forEach((path) => formData.images.push(path));
      }
    },
  });
};

const deleteImage = (index: number) => {
  formData.images.splice(index, 1);
};

const toggleAgreement = () => {
  agreedToTerms.value = !agreedToTerms.value;
};

const canSubmit = computed(() => {
  const { projectName, address, developer, averagePrice, images, description } =
    formData;
  return (
    projectName &&
    address &&
    developer &&
    averagePrice &&
    images.length > 0 &&
    description &&
    agreedToTerms.value
  );
});

const submitForm = async () => {
  if (!canSubmit.value) {
    uni.showToast({
      title: "请完善必填信息",
      icon: "none",
    });
    return;
  }

  try {
    uni.showLoading({ title: "发布中..." });
    // TODO: 调用发布接口
    await new Promise((resolve) => setTimeout(resolve, 2000));
    uni.hideLoading();
    uni.showToast({
      title: "发布成功",
      icon: "success",
      success: () => {
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      },
    });
  } catch (error) {
    uni.hideLoading();
    uni.showToast({
      title: "发布失败，请重试",
      icon: "none",
    });
  }
};

const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.new-house-publish-page {
  min-height: 100vh;
  background: var(--bg-page);
}

.content-scroll {
  height: 100vh;
}

.content {
  padding-bottom: 200rpx;
  background-color: transparent;
}

.guide-section {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  border-radius: var(--radius-3);
  padding: 40rpx;
  margin-bottom: var(--spacing);
  text-align: center;
  box-shadow: 0 8rpx 20rpx rgba(139, 92, 246, 0.15);
}

.guide-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-inverse);
  display: block;
  margin-bottom: 12rpx;
}

.guide-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  display: block;
  line-height: var(--line-height-normal);
}

.form-section {
  background: var(--bg-card);
  border-radius: var(--radius-3);
  padding: 40rpx;
  margin-bottom: var(--spacing-3);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid var(--border-color);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;

  margin-bottom: var(--spacing);
  display: flex;
  align-items: center;
}

.section-tip {
  font-size: 24rpx;
  color: var(--text-info);
  margin-bottom: var(--spacing);
  display: block;
  line-height: var(--line-height-normal);
}

.required {
  color: var(--text-red);
  margin-right: var(--spacing);
  font-weight: 600;
}

.form-item {
  display: flex;
  align-items: center;
  padding: var(--spacing) 0;
  border-bottom: 1rpx solid var(--border-color);
  min-height: 88rpx;

  &:last-child {
    border-bottom: none;
  }
}

.label {
  min-width: 160rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.value-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.input {
  flex: 1;
  text-align: right;

  background: transparent;
  border: none;
  outline: none;

  &::placeholder {
    color: var(--text-grey);
  }
}

.unit {
  color: var(--text-info);
  margin-left: var(--spacing);
  font-weight: 500;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.tag-item {
  padding: 12rpx var(--spacing-3);
  border-radius: var(--radius-2);
  background: var(--bg-tag);
  color: var(--text-secondary);
  font-size: 26rpx;
  font-weight: 500;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;

  &.active {
    background: rgba(139, 92, 246, 0.1);
    color: #8b5cf6;
    border-color: #8b5cf6;
  }


}

.image-upload-area {
  margin-top: var(--spacing-2);
}

.upload-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-2);
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: var(--radius-2);
  overflow: hidden;
  background: var(--bg-tag);
}

.preview-image {
  width: 100%;
  height: 100%;
}

.delete-btn {
  position: absolute;
  top: var(--spacing);
  right: var(--spacing);
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
  font-size: 20rpx;


}

.upload-btn {
  aspect-ratio: 1;
  border: 2rpx dashed var(--text-grey);
  border-radius: var(--radius-2);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-info);
  background: var(--bg-tag);
  transition: all 0.3s ease;


}

.upload-icon {
  font-size: 48rpx;
  margin-bottom: var(--spacing);
}

.upload-text {
  font-size: 24rpx;
  text-align: center;
  line-height: var(--line-height-normal);

  &.sub {
    margin-top: 2rpx;
    opacity: 0.8;
  }
}

.textarea-container {
  position: relative;
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-2);
  padding: var(--spacing-3);
  background: var(--bg-input);
  margin-top: var(--spacing-2);
}

.description-textarea {
  width: 100%;
  min-height: 200rpx;

  line-height: var(--line-height-large);

  background: transparent;
  border: none;
  outline: none;

  &::placeholder {
    color: var(--text-grey);
  }
}

.char-count {
  position: absolute;
  bottom: var(--spacing-2);
  right: var(--spacing-3);
  font-size: 24rpx;
  color: var(--text-info);
}

.agreement-section {
  margin-bottom: 40rpx;
  padding: var(--spacing);
  background: var(--bg-card);
  border-radius: var(--radius-2);
  border: 1rpx solid var(--border-color);
}

.agreement-item {
  display: flex;
  align-items: flex-start;
  padding: var(--spacing-2) 0;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid var(--text-grey);
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-3);
  margin-top: 2rpx;
  transition: all 0.2s ease;

  &.checked {
    background: #8b5cf6;
    border-color: #8b5cf6;
    color: var(--text-inverse);
  }


}

.agreement-text {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: var(--line-height-large);
  flex: 1;
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing) var(--spacing) 40rpx;
  background: var(--bg-card);
  border-top: 1rpx solid var(--border-color);
  z-index: 100;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.08);
}
</style>
