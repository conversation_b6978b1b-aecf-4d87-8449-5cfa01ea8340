<template>
  <view class="second-hand-publish-page">
    <uni-nav-bar
      :border="false"
      fixed
      background-color="#ffffff"
      :status-bar="true"
      title="发布二手房"
      left-icon="back"
      @clickLeft="goBack"
    />

    <!-- 内容区使用页面原生滚动，计算顶部导航栏高度 -->
    <view class="content">
      <view class="guide-section">
        <text class="guide-title">发布二手房信息</text>
        <text class="guide-desc">详细填写房屋信息，快速找到合适买家</text>
      </view>

      <!-- 表单内容 -->
      <view class="form-content">
        <tui-form ref="formRef" :model="formData" :rules="formRules">
          <view class="card">
            <view class="section-title">房源基本信息</view>

            <tui-form-item
              prop="community"
              :label="communityLabel"
              labelSize="30rpx"
              :labelColor="getLabelColor('community')"
              :asterisk="true"
              asteriskColor="#ff4757"
              @click="handleFormItemClick('community')"
            >
              <tui-input
                v-model="formData.community"
                placeholder="请输入小区名称"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
              ></tui-input>
            </tui-form-item>

            <tui-form-item
              prop="houseType"
              :label="houseTypeLabel"
              labelSize="30rpx"
              :labelColor="getLabelColor('houseType')"
              :asterisk="true"
              asteriskColor="#ff4757"
              @click="handleFormItemClick('houseType')"
            >
              <tui-input
                v-model="formData.houseType"
                placeholder="如：3室2厅1卫"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                @input="handleInput('houseType')"
                @blur="handleBlur('houseType')"
              />
            </tui-form-item>

            <tui-form-item
              prop="area"
              :label="areaLabel"
              labelSize="30rpx"
              :labelColor="getLabelColor('area')"
              :asterisk="true"
              asteriskColor="#ff4757"
              @click="handleFormItemClick('area')"
            >
              <view class="input-with-unit">
                <tui-input
                  v-model="formData.area"
                  placeholder="请输入面积"
                  type="number"
                  :borderBottom="false"
                  padding="0"
                  color="var(--text-base)"
                  placeholderStyle="color: var(--text-grey)"
                  @input="handleInput('area')"
                  @blur="handleBlur('area')"
                />
                <template v-slot:right>
                  <text>㎡</text>
                </template>
              </view>
            </tui-form-item>

            <tui-form-item
              prop="price"
              :label="priceLabel"
              labelSize="30rpx"
              :labelColor="getLabelColor('price')"
              :asterisk="true"
              asteriskColor="#ff4757"
              @click="handleFormItemClick('price')"
            >
              <view class="input-with-unit">
                <tui-input
                  v-model="formData.price"
                  placeholder="请输入售价"
                  type="number"
                  :borderBottom="false"
                  padding="0"
                  color="var(--text-base)"
                  placeholderStyle="color: var(--text-grey)"
                  @input="handleInput('price')"
                  @blur="handleBlur('price')"
                />
                <template v-slot:right>
                  <text>万元</text>
                </template>
              </view>
            </tui-form-item>

            <tui-form-item
              prop="floor"
              :label="floorLabel"
              labelSize="30rpx"
              :labelColor="getLabelColor('floor')"
              @click="handleFormItemClick('floor')"
            >
              <tui-input
                v-model="formData.floor"
                placeholder="如：6/15层"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                @input="handleInput('floor')"
                @blur="handleBlur('floor')"
              />
            </tui-form-item>

            <tui-form-item
              prop="decoration"
              :label="decorationLabel"
              labelSize="30rpx"
              :bottomBorder="false"
              :labelColor="getLabelColor('decoration')"
              @click="handleFormItemClick('decoration')"
            >
              <tui-input
                v-model="formData.decoration"
                placeholder="如：精装修"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                @input="handleInput('decoration')"
                @blur="handleBlur('decoration')"
              />
            </tui-form-item>
          </view>

          <view class="card">
            <view class="section-title">
              <text class="required">*</text>
              <text>房源图片</text>
            </view>
            <text class="section-tip"
              >至少上传1张图片，最多9张，展示房屋真实状况</text
            >

            <view class="image-upload-area">
              <view class="upload-grid">
                <view
                  v-for="(image, index) in formData.images"
                  :key="index"
                  class="image-item"
                >
                  <image :src="image" mode="aspectFill" class="preview-image" />
                  <view class="delete-btn" @tap="deleteImage(index)">
                    <text class="i-carbon-close"></text>
                  </view>
                </view>

                <view
                  v-if="formData.images.length < 9"
                  class="upload-btn"
                  @tap="uploadImage"
                >
                  <text class="i-carbon-camera upload-icon"></text>
                  <text class="upload-text">添加房源图片</text>
                  <text class="upload-text sub">真实房源更吸引人</text>
                </view>
              </view>
            </view>
          </view>

          <view class="card">
            <view class="section-title">
              <text>房源描述</text>
            </view>
            <text class="section-tip"
              >详细介绍房屋特色、周边配套等，吸引更多买家</text
            >

            <tui-form-item
              prop="description"
              asteriskColor="#ff4757"
              :bottomBorder="false"
              padding="0 0"
              flexStart
            >
              <tui-textarea
                v-model="formData.description"
                placeholder="请详细描述房屋的位置优势、装修状况、周边配套等信息"
                maxlength="500"
                height="200rpx"
                minHeight="200rpx"
                :borderTop="false"
                :borderBottom="false"
                backgroundColor="var(--bg-input)"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                showConfirmBar
                isCounter
                counterSize="24"
                counterColor="var(--text-info)"
                @input="handleInput('description')"
                @blur="handleBlur('description')"
              />
            </tui-form-item>
          </view>
        </tui-form>

        <view class="agreement-section">
          <view class="agreement-item" @tap="toggleAgreement">
            <view class="checkbox" :class="{ checked: agreedToTerms }">
              <text v-if="agreedToTerms" class="i-carbon-checkmark"></text>
            </view>
            <text class="agreement-text">
              我承诺房源真实并同意《个人用户二手房房源推广服务协议》
            </text>
          </view>
        </view>
      </view>

      <!-- 底部提交按钮区域 -->
      <view class="submit-section">
        <tui-button
          @click="submitForm"
          type="primary"
          width="100%"
          height="96rpx"
          :bold="true"
          shape="circle"
        >
          免费发布
        </tui-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from "vue";

// 其余脚本内容保持不变
const formRef = ref<any>(null);

// 表单数据
const formData = reactive({
  community: "",
  houseType: "",
  area: "",
  price: "",
  floor: "",
  decoration: "",
  images: [] as string[],
  description: "",
});

// 协议同意状态
const agreedToTerms = ref(false);

// 字段聚焦状态
const fieldFocusState = reactive({
  community: false,
  houseType: false,
  area: false,
  price: false,
  floor: false,
  decoration: false,
  description: false,
});

// 表单验证规则
const formRules = {
  community: {
    name: "community",
    rule: ["required"],
    msg: ["请输入小区名称"],
  },
  houseType: {
    name: "houseType",
    rule: ["required"],
    msg: ["请输入房屋户型"],
  },
  area: {
    name: "area",
    rule: ["required", "number"],
    msg: ["请输入建筑面积", "请输入正确的面积数字"],
  },
  price: {
    name: "price",
    rule: ["required", "number"],
    msg: ["请输入售价", "请输入正确的价格"],
  },
  description: {
    name: "description",
    rule: ["required"],
    msg: ["请输入房源描述"],
  },
};

// 动态标签文本
const communityLabel = computed(() => "小区名称");
const houseTypeLabel = computed(() => "房屋户型");
const areaLabel = computed(() => "建筑面积");
const priceLabel = computed(() => "售价");
const floorLabel = computed(() => "楼层");
const decorationLabel = computed(() => "装修情况");

// 获取标签颜色
const getLabelColor = (field: string) => {
  const value = formData[field as keyof typeof formData];
  if (typeof value === "string") {
    return value ? "var(--text-info)" : "var(--text-secondary)";
  }
  return "var(--text-secondary)";
};

// 处理输入事件 - 使用节流优化
let inputTimer: any = null;
const handleInput = (field: string) => {
  if (inputTimer) clearTimeout(inputTimer);

  inputTimer = setTimeout(() => {
    if (formRef.value) {
      requestAnimationFrame(() => {
        formRef.value.validateField(field);
      });
    }
  }, 300); // 延迟验证，减少频繁验证导致的UI阻塞
};

// 处理失焦事件
const handleBlur = (field: string) => {
  fieldFocusState[field as keyof typeof fieldFocusState] = false;
};

// 处理表单项点击
const handleFormItemClick = (field: string) => {
  fieldFocusState[field as keyof typeof fieldFocusState] = true;
};

// 上传图片 - 添加图片压缩处理
const uploadImage = () => {
  uni.showLoading({
    title: "准备上传...",
  });

  uni.chooseImage({
    count: 9 - formData.images.length,
    sizeType: ["compressed"], // 压缩图片
    sourceType: ["camera", "album"],
    success: (res) => {
      if (Array.isArray(res.tempFilePaths)) {
        // 延迟处理，避免UI阻塞
        setTimeout(() => {
          // 限制数量，避免过多图片导致渲染压力
          const paths = res.tempFilePaths as string[];
          const newImages = paths.slice(0, 9 - formData.images.length);

          // 分批添加图片，避免一次性大量DOM更新
          if (newImages.length > 3) {
            // 先添加一部分
            for (let i = 0; i < 3; i++) {
              formData.images.push(newImages[i]);
            }

            setTimeout(() => {
              // 再添加剩余部分
              for (let i = 3; i < newImages.length; i++) {
                formData.images.push(newImages[i]);
              }
              uni.hideLoading();
            }, 100);
          } else {
            // 直接添加全部
            for (let i = 0; i < newImages.length; i++) {
              formData.images.push(newImages[i]);
            }
            uni.hideLoading();
          }
        }, 100);
      } else {
        uni.hideLoading();
      }
    },
    fail: () => {
      uni.hideLoading();
    },
  });
};

// 删除图片
const deleteImage = (index: number) => {
  formData.images.splice(index, 1);
};

// 切换协议同意状态
const toggleAgreement = () => {
  agreedToTerms.value = !agreedToTerms.value;
};

// 提交表单
const submitForm = async () => {
  try {
    // 验证图片
    if (formData.images.length === 0) {
      uni.showToast({
        title: "请至少上传1张房源图片",
        icon: "none",
        duration: 2000,
      });
      return;
    }

    // 验证协议
    if (!agreedToTerms.value) {
      uni.showToast({
        title: "请先同意服务协议",
        icon: "none",
        duration: 2000,
      });
      return;
    }

    // 表单验证
    if (formRef.value) {
      const validateResult = await formRef.value.validate();
      if (!validateResult.isPass) {
        uni.showToast({
          title: validateResult.errorMsg || "请完善必填信息",
          icon: "none",
          duration: 2000,
        });
        return;
      }
    }

    uni.showLoading({ title: "发布中..." });
    // TODO: 调用发布接口
    await new Promise((resolve) => setTimeout(resolve, 2000));
    uni.hideLoading();
    uni.showToast({
      title: "发布成功",
      icon: "success",
      success: () => {
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      },
    });
  } catch (error) {
    uni.hideLoading();
    uni.showToast({
      title: "发布失败，请重试",
      icon: "none",
      duration: 2000,
    });
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.second-hand-publish-page {
  min-height: 100vh;
  background: var(--bg-page);
  /* 优化页面滚动性能 */
  overscroll-behavior: none; /* 防止过度滚动 */
}

.content {
  padding-bottom: calc(200rpx + env(safe-area-inset-bottom));
  background: transparent;
  box-sizing: border-box;
}

/* 表单内容区 */
.form-content {
  // 使用CSS变量来避免硬编码值
  padding-bottom: 64rpx;
}

.guide-section {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  border-radius: 20rpx;
  padding: 20rpx;
  margin-top: var(--spacing-3);
  margin-bottom: var(--spacing-3);
  text-align: center;
  box-shadow: 0 8rpx 20rpx rgba(79, 172, 254, 0.15);
  // 加速渲染
  will-change: transform;
  transform: translateZ(0);
  // 避免重绘
  contain: layout style;
}

.guide-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-inverse);
  display: block;
  margin-bottom: 12rpx;
}

.guide-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  display: block;
  line-height: var(--line-height-normal);
}

.card {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;

  margin: 12rpx 0 12rpx 12rpx;
  display: flex;
  align-items: center;
}

.section-tip {
  font-size: 24rpx;
  color: var(--text-info);
  margin-bottom: var(--spacing);
  display: block;
  line-height: var(--line-height-normal);
}

.required {
  color: var(--text-red);
  margin-right: var(--spacing);
  font-weight: 600;
}

.input-with-unit {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
}

.image-upload-area {
  margin-top: var(--spacing-2);
}

.upload-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-2);
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: var(--radius-2);
  overflow: hidden;
  background: var(--bg-tag);
}

.preview-image {
  width: 100%;
  height: 100%;
  /* 优化图片渲染性能 */
  will-change: transform;
  transform: translateZ(0);
  contain: paint;
}

.delete-btn {
  position: absolute;
  top: var(--spacing);
  right: var(--spacing);
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
  font-size: 20rpx;


}

.upload-btn {
  aspect-ratio: 1;
  border: 2rpx dashed var(--text-grey);
  border-radius: var(--radius-2);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-info);
  background: var(--bg-tag);
  transition: all 0.3s ease;


}

.upload-icon {
  font-size: 48rpx;
  margin-bottom: var(--spacing);
}

.upload-text {
  font-size: 24rpx;
  text-align: center;
  line-height: var(--line-height-normal);

  &.sub {
    margin-top: 2rpx;
    opacity: 0.8;
  }
}

.agreement-section {
  margin-bottom: 40rpx;
  padding: var(--spacing);
  background: var(--bg-card);
  border-radius: var(--radius-2);
  border: 1rpx solid var(--border-color);
}

.agreement-item {
  display: flex;
  align-items: flex-start;
  padding: var(--spacing-2) 0;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid var(--text-grey);
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-3);
  margin-top: 2rpx;
  transition: all 0.2s ease;

  &.checked {
    background: #4facfe;
    border-color: #4facfe;
    color: var(--text-inverse);
  }


}

.agreement-text {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: var(--line-height-large);
  flex: 1;
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing) var(--spacing);
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
  background: var(--bg-card);
  border-top: 1rpx solid var(--border-color);
  z-index: 100;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.08);
  // 创建独立的合成层，减少主线程负担
  will-change: transform;
  transform: translateZ(0);
}
</style>
