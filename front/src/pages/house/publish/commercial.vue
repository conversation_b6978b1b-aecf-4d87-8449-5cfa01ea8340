<template>
  <view class="commercial-publish-page">
    <uni-nav-bar
      :border="false"
      fixed
      background-color="#ffffff"
      :status-bar="true"
      title="发布商铺/写字楼"
      left-icon="back"
      @clickLeft="goBack"
    />

    <view class="content">
      <view class="guide-section">
        <text class="guide-title">发布商业地产</text>
        <text class="guide-desc"
          >商铺、写字楼、厂房等商业物业发布，快速找到合适租客</text
        >
      </view>

      <view class="form-content">
        <tui-form ref="formRef" :model="formData" :rules="formRules">
          <view class="card">
            <view class="section-title">
              <text class="required">*</text>
              <text>物业类型</text>
            </view>
            <view class="type-tabs">
              <view
                v-for="type in propertyTypes"
                :key="type.value"
                class="tab-item"
                :class="{ active: formData.propertyType === type.value }"
                @tap="selectPropertyType(type.value)"
              >
                {{ type.label }}
              </view>
            </view>
          </view>

          <view class="card">
            <view class="section-title">基本信息</view>

            <tui-form-item
              prop="propertyName"
              label="物业名称"
              labelSize="30rpx"
              :labelColor="getLabelColor('propertyName')"
              :asterisk="true"
              asteriskColor="#ff4757"
              @click="handleFormItemClick('propertyName')"
            >
              <tui-input
                v-model="formData.propertyName"
                placeholder="请输入物业名称"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                @input="handleInput('propertyName')"
                @blur="handleBlur('propertyName')"
              />
            </tui-form-item>

            <tui-form-item
              prop="area"
              label="面积"
              labelSize="30rpx"
              :labelColor="getLabelColor('area')"
              :asterisk="true"
              asteriskColor="#ff4757"
              @click="handleFormItemClick('area')"
            >
              <view class="input-with-unit">
                <tui-input
                  v-model="formData.area"
                  placeholder="请输入面积"
                  type="number"
                  :borderBottom="false"
                  padding="0"
                  color="var(--text-base)"
                  placeholderStyle="color: var(--text-grey)"
                  @input="handleInput('area')"
                  @blur="handleBlur('area')"
                />
              </view>
              <template #right>
                <text>㎡</text>
              </template>
            </tui-form-item>

            <tui-form-item
              prop="floor"
              label="所在楼层"
              labelSize="30rpx"
              :labelColor="getLabelColor('floor')"
              @click="handleFormItemClick('floor')"
            >
              <tui-input
                v-model="formData.floor"
                placeholder="如：3层"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                @input="handleInput('floor')"
                @blur="handleBlur('floor')"
              />
            </tui-form-item>

            <tui-form-item
              prop="decoration"
              label="装修情况"
              labelSize="30rpx"
              :labelColor="getLabelColor('decoration')"
              :bottomBorder="false"
              @click="handleFormItemClick('decoration')"
            >
              <tui-input
                v-model="formData.decoration"
                placeholder="如：精装修"
                :borderBottom="false"
                padding="0"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                @input="handleInput('decoration')"
                @blur="handleBlur('decoration')"
              />
            </tui-form-item>
          </view>

          <view class="card">
            <view class="section-title">价格信息</view>

            <tui-form-item
              prop="price"
              label="期望价格"
              labelSize="30rpx"
              :labelColor="getLabelColor('price')"
              :asterisk="true"
              asteriskColor="#ff4757"
              @click="handleFormItemClick('price')"
            >
              <view class="input-with-unit">
                <tui-input
                  v-model="formData.price"
                  placeholder="请输入价格"
                  type="number"
                  :borderBottom="false"
                  padding="0"
                  color="var(--text-base)"
                  placeholderStyle="color: var(--text-grey)"
                  @input="handleInput('price')"
                  @blur="handleBlur('price')"
                />
              </view>
              <template #right>
                <text>{{
                  formData.propertyType === "rent" ? "元/月" : "万元"
                }}</text>
              </template>
            </tui-form-item>
          </view>

          <view class="card">
            <view class="section-title">
              <text class="required">*</text>
              <text>物业图片</text>
            </view>
            <text class="section-tip"
              >至少上传1张图片，最多9张，展示物业真实状况</text
            >

            <view class="image-upload-area">
              <view class="upload-grid">
                <view
                  v-for="(image, index) in formData.images"
                  :key="index"
                  class="image-item"
                >
                  <image :src="image" mode="aspectFill" class="preview-image" />
                  <view class="delete-btn" @tap="deleteImage(index)">
                    <text class="i-carbon-close"></text>
                  </view>
                </view>

                <view
                  v-if="formData.images.length < 9"
                  class="upload-btn"
                  @tap="uploadImage"
                >
                  <text class="i-carbon-camera upload-icon"></text>
                  <text class="upload-text">添加物业图片</text>
                  <text class="upload-text sub">展示商业价值</text>
                </view>
              </view>
            </view>
          </view>

          <view class="card">
            <view class="section-title">
              <text class="required">*</text>
              <text>物业描述</text>
            </view>
            <text class="section-tip"
              >详细介绍物业位置优势、配套设施、适合行业等</text
            >

            <tui-form-item
              prop="description"
              asteriskColor="#ff4757"
              :bottomBorder="false"
              padding="0 0"
              flexStart
            >
              <tui-textarea
                v-model="formData.description"
                placeholder="请描述物业的位置优势、配套设施、适合行业等信息"
                maxlength="500"
                height="200rpx"
                minHeight="200rpx"
                :borderTop="false"
                :borderBottom="false"
                backgroundColor="var(--bg-input)"
                color="var(--text-base)"
                placeholderStyle="color: var(--text-grey)"
                showConfirmBar
                isCounter
                counterSize="24"
                counterColor="var(--text-info)"
                @input="handleInput('description')"
                @blur="handleBlur('description')"
              />
            </tui-form-item>
          </view>
        </tui-form>

        <view class="agreement-section">
          <view class="agreement-item" @tap="toggleAgreement">
            <view class="checkbox" :class="{ checked: agreedToTerms }">
              <text v-if="agreedToTerms" class="i-carbon-checkmark"></text>
            </view>
            <text class="agreement-text">
              我承诺物业信息真实并同意《个人用户商业地产推广服务协议》
            </text>
          </view>
        </view>
      </view>

      <view class="submit-section">
        <tui-button
          @click="submitForm"
          type="primary"
          width="100%"
          height="96rpx"
          :bold="true"
          shape="circle"
        >
          免费发布
        </tui-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";

const formRef = ref<any>(null);

const formData = reactive({
  propertyType: "rent",
  propertyName: "",
  area: "",
  price: "",
  floor: "",
  decoration: "",
  images: [] as string[],
  description: "",
});

const propertyTypes = [
  { label: "出租", value: "rent" },
  { label: "出售", value: "sale" },
];

const agreedToTerms = ref(false);

const fieldFocusState = reactive({
  propertyName: false,
  area: false,
  price: false,
  floor: false,
  decoration: false,
  description: false,
});

const formRules = {
  propertyName: {
    name: "propertyName",
    rule: ["required"],
    msg: ["请输入物业名称"],
  },
  area: {
    name: "area",
    rule: ["required", "number"],
    msg: ["请输入面积", "请输入正确的面积数字"],
  },
  price: {
    name: "price",
    rule: ["required", "number"],
    msg: ["请输入价格", "请输入正确的价格"],
  },
  description: {
    name: "description",
    rule: ["required"],
    msg: ["请输入物业描述"],
  },
};

const getLabelColor = (field: string) => {
  const value = formData[field as keyof typeof formData];
  if (typeof value === "string") {
    return value ? "var(--text-info)" : "var(--text-secondary)";
  }
  return "var(--text-secondary)";
};

let inputTimer: any = null;
const handleInput = (field: string) => {
  if (inputTimer) clearTimeout(inputTimer);
  inputTimer = setTimeout(() => {
    if (formRef.value) {
      requestAnimationFrame(() => {
        formRef.value.validateField(field);
      });
    }
  }, 300);
};

const handleBlur = (field: string) => {
  fieldFocusState[field as keyof typeof fieldFocusState] = false;
};

const handleFormItemClick = (field: string) => {
  fieldFocusState[field as keyof typeof fieldFocusState] = true;
};

const selectPropertyType = (type: string) => {
  formData.propertyType = type;
};

const uploadImage = () => {
  uni.showLoading({
    title: "准备上传...",
  });

  uni.chooseImage({
    count: 9 - formData.images.length,
    sizeType: ["compressed"],
    sourceType: ["camera", "album"],
    success: (res) => {
      if (Array.isArray(res.tempFilePaths)) {
        setTimeout(() => {
          const paths = res.tempFilePaths as string[];
          const newImages = paths.slice(0, 9 - formData.images.length);

          if (newImages.length > 3) {
            for (let i = 0; i < 3; i++) {
              formData.images.push(newImages[i]);
            }
            setTimeout(() => {
              for (let i = 3; i < newImages.length; i++) {
                formData.images.push(newImages[i]);
              }
              uni.hideLoading();
            }, 100);
          } else {
            for (let i = 0; i < newImages.length; i++) {
              formData.images.push(newImages[i]);
            }
            uni.hideLoading();
          }
        }, 100);
      } else {
        uni.hideLoading();
      }
    },
    fail: () => {
      uni.hideLoading();
    },
  });
};

const deleteImage = (index: number) => {
  formData.images.splice(index, 1);
};

const toggleAgreement = () => {
  agreedToTerms.value = !agreedToTerms.value;
};

const submitForm = async () => {
  try {
    if (formData.images.length === 0) {
      uni.showToast({
        title: "请至少上传1张物业图片",
        icon: "none",
        duration: 2000,
      });
      return;
    }

    if (!agreedToTerms.value) {
      uni.showToast({
        title: "请先同意服务协议",
        icon: "none",
        duration: 2000,
      });
      return;
    }

    if (formRef.value) {
      const validateResult = await formRef.value.validate();
      if (!validateResult.isPass) {
        uni.showToast({
          title: validateResult.errorMsg || "请完善必填信息",
          icon: "none",
          duration: 2000,
        });
        return;
      }
    }

    await publishProperty();
  } catch (error) {
    uni.hideLoading();
    uni.showToast({
      title: "发布失败，请重试",
      icon: "none",
      duration: 2000,
    });
  }
};

// 发布物业
const publishProperty = async () => {
  try {
    uni.showLoading({ title: "发布中..." });

    // 构建发布数据
    const publishData = {
      ...formData,
    };

    console.log("发布数据:", publishData);

    await new Promise((resolve) => setTimeout(resolve, 2000));
    uni.hideLoading();

    uni.showToast({
      title: "发布成功",
      icon: "success",
      success: () => {
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      },
    });
  } catch (error) {
    uni.hideLoading();
    uni.showToast({
      title: "发布失败，请重试",
      icon: "none",
      duration: 2000,
    });
  }
};

const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.commercial-publish-page {
  min-height: 100vh;
  background: var(--bg-page);
}

.content {
  padding-bottom: calc(200rpx + env(safe-area-inset-bottom));
  background: transparent;
  box-sizing: border-box;
}

.guide-section {
  background: linear-gradient(135deg, #10b981, #22c55e);
  border-radius: 20rpx;
  padding: 20rpx;
  margin-top: var(--spacing-3);
  margin-bottom: var(--spacing-3);
  text-align: center;
  box-shadow: 0 8rpx 20rpx rgba(16, 185, 129, 0.15);
  will-change: transform;
  transform: translateZ(0);
  contain: layout style;
}

.guide-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-inverse);
  display: block;
  margin-bottom: 12rpx;
}

.guide-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  display: block;
  line-height: var(--line-height-normal);
}

.form-content {
  padding-bottom: 64rpx;
}

.card {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;

  margin: 12rpx 0 12rpx 12rpx;
  display: flex;
  align-items: center;
}

.section-tip {
  font-size: 24rpx;
  color: var(--text-info);
  margin-bottom: var(--spacing);
  display: block;
  line-height: var(--line-height-normal);
}

.required {
  color: var(--text-red);
  margin-right: var(--spacing);
  font-weight: 600;
}

.type-tabs {
  display: flex;
  gap: var(--spacing-2);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx var(--spacing-2);
  border-radius: var(--radius-2);
  background: var(--bg-tag);
  color: var(--text-secondary);

  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 2rpx solid transparent;

  &.active {
    background: #10b981;
    color: var(--text-inverse);
    border-color: #10b981;
  }

 
}

.input-with-unit {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
}

.image-upload-area {
  margin-top: var(--spacing-2);
}

.upload-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-2);
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: var(--radius-2);
  overflow: hidden;
  background: var(--bg-tag);
}
</style>
