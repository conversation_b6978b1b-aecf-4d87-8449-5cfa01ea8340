<template>
  <view class="container">
    <!-- 新的、更简洁的背景 -->
    <view class="simple-bg" />

    <!-- 内容区域 - 使用z-paging组件 -->
    <z-paging
      ref="pagingRef"
      v-model="houseList"
      :safe-area-inset-bottom="true"
      :top="stickyHeaderHeight"
      empty-view-text="暂无房源数据"
      @query="queryHouseList"
    >
      <template #top>
        <uni-nav-bar
          :border="false"
          :fixed="true"
          :status-bar="true"
          background-color="transparent"
          left-icon="back"
          @clickLeft="navigateBack"
        >
        </uni-nav-bar>

        <!-- 粘性头部：搜索和筛选 -->
        <view
          class="sticky-header"
          :class="{ 'filter-active': isFilterActive }"
        >
          <!-- 搜索栏 -->
          <view class="search-section">
            <view class="search-bar" @tap="handleSearch">
              <text class="i-carbon-search text-20px text-grey" />
              <text class="search-placeholder">搜索楼盘名称、位置</text>
            </view>
          </view>

          <!-- 筛选组件 -->
          <view class="filter-container">
            <FilterPanel
              @filter-change="handleFilterChange"
              @open-filter="handleFilterOpen"
              @close-filter="handleFilterClose"
            />
          </view>
        </view>
      </template>
      <view class="house-list-container">
        <view
          v-for="house in houseList"
          :key="house.id"
          class="house-card-wrapper"
          @tap="navigateToDetail(house.id)"
        >
          <view class="house-card">
            <!-- 房源图片 -->
            <view class="house-image-wrapper">
              <image
                :src="house.image"
                mode="aspectFill"
                class="house-image"
                :lazy-load="true"
              />
              <view v-if="house.propertyType" class="property-type-badge">
                {{ house.propertyType }}
              </view>
            </view>

            <!-- 房源信息 -->
            <view class="house-content">
              <!-- 标题和状态 -->
              <view class="title-status-line">
                <text class="house-title">{{ house.title }}</text>
                <view
                  v-if="house.status"
                  class="status-badge"
                  :class="getStatusClass(house.status)"
                >
                  {{ house.status }}
                </view>
              </view>

              <!-- 价格区域 -->
              <view class="price-area">
                <text
                  class="house-price"
                  :class="{ 'price-pending': house.price === '价格待定' }"
                >
                  {{ house.price }}
                </text>
                <view v-if="house.specialOffer" class="special-tag">
                  {{ house.specialOffer }}
                </view>
              </view>

              <!-- 位置信息 -->
              <text class="house-location">{{ house.location }}</text>

              <!-- 标签区域 -->
              <view
                v-if="house.tags && house.tags.length"
                class="tags-container"
              >
                <text v-for="tag in house.tags" :key="tag" class="tag">
                  {{ tag }}
                </text>
              </view>

              <!-- 关注信息 -->
              <view v-if="house.attentionCount" class="attention-text">
                <text class="i-carbon-favorite" />
                {{ house.attentionCount }}人关注
              </view>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from "vue";
import type { NewHouseItem } from "@/types/house";
import FilterPanel from "./components/FilterPanel.vue";
import CustomCard from "@/components/common/Card.vue";

// z-paging引用
const pagingRef = ref(null);
const stickyHeaderHeight = ref(100); // 默认高度
const isFilterActive = ref(false); // 控制筛选器激活状态

// 状态栏高度
const statusBarHeight = ref(0);

// 当前页码
const showSearchBar = ref(true);

// 响应式数据
const currentLocation = ref("北京");
const showFilterModal = ref(false);

// 筛选条件
const currentFilters = reactive({
  area: "",
  price: "",
  houseType: "",
  features: [] as string[],
  developer: "",
  decoration: "",
});

// 选中的筛选条件
const selectedFilters = reactive({
  price: "",
  houseType: "",
  features: [] as string[],
});

// 保存原始房源列表数据
const originalHouseList = [
  {
    id: "1",
    image: "https://picsum.photos/seed/house-1/300/200",
    title: "元垄石景山",
    location: "石景山·石景山其它/建面120-196㎡/3,4居",
    price: "75000元/㎡",
    specialOffer: "有15套特价房",
    tags: ["近地铁", "综合商场", "公园"],
    attentionCount: 30,
    status: "在售",
    priceRange: "总价950-1560万",
    propertyType: "住宅",
  },
  {
    id: "2",
    image: "https://picsum.photos/seed/house-2/300/200",
    title: "招商云璟",
    location: "通州·梨园/建面79-128㎡/3,4居",
    price: "60000元/㎡",
    specialOffer: "有9套特价房",
    tags: ["近地铁", "医疗配套", "期房"],
    attentionCount: 33,
    status: "在售",
    priceRange: "总价470-830万",
    propertyType: "住宅",
  },
  {
    id: "smds",
    image: "https://picsum.photos/seed/house-smds/300/200",
    title: "世茂维拉左右间",
    location: "房山·长阳",
    price: "价格待定",
    specialOffer: "",
    tags: ["近地铁", "综合商场", "小型社区"],
    attentionCount: 10,
    status: "待售",
    priceRange: "",
    propertyType: "写字楼",
  },
  {
    id: "cayh",
    image: "https://picsum.photos/seed/house-cayh/300/200",
    title: "长安运河",
    location: "通州·万达",
    price: "价格待定",
    specialOffer: "",
    tags: ["近地铁", "综合商场", "三甲医院"],
    attentionCount: 5,
    status: "待售",
    priceRange: "",
    propertyType: "商业类",
  },
  {
    id: "3",
    image: "https://picsum.photos/seed/house-3/300/200",
    title: "京投发展森与天成",
    location: "丰台·新宫/建面55-148㎡/1,2,3,4居",
    price: "78000元/㎡",
    specialOffer: "",
    tags: ["近地铁", "公园", "期房"],
    attentionCount: 0,
    status: "在售",
    priceRange: "总价430-1150万",
    propertyType: "住宅",
  },
  {
    id: "4",
    image: "https://picsum.photos/seed/house-4/300/200",
    title: "清樾府",
    location: "昌平·沙河/建面86-143㎡/3,4居",
    price: "46000元/㎡",
    specialOffer: "",
    tags: ["期房", "小三居", "低密居所"],
    attentionCount: 39,
    status: "在售",
    priceRange: "总价410-680万",
    propertyType: "住宅",
  },
];

// 房源列表数据
const houseList = ref<NewHouseItem[]>([]);

// 方法
const navigateBack = () => {
  uni.navigateBack();
};

const handleSearch = () => {
  uni.showToast({
    title: "搜索功能开发中",
    icon: "none",
  });
};

const selectLocation = () => {
  uni.showActionSheet({
    itemList: ["北京", "上海", "广州", "深圳", "杭州"],
    success: (res) => {
      const locations = ["北京", "上海", "广州", "深圳", "杭州"];
      currentLocation.value = locations[res.tapIndex];
      // 切换城市后重新加载数据
      if (pagingRef.value) {
        pagingRef.value.reload();
      }
    },
  });
};

// 查看更多热门楼盘
const viewMoreHotHouse = () => {
  uni.showToast({
    title: "查看更多热门楼盘",
    icon: "none",
  });
};

// 查看更多开盘日历
const viewMoreCalendar = () => {
  uni.showToast({
    title: "查看更多开盘日历",
    icon: "none",
  });
};

// 预约看房
const appointHouse = (id: string) => {
  uni.showToast({
    title: "预约看房功能开发中",
    icon: "none",
  });
};

const getStatusClass = (status: string) => {
  switch (status) {
    case "在售":
      return "status-selling";
    case "待售":
      return "status-pending-sale";
    case "热销":
      return "status-hot";
    case "即将开盘":
      return "status-coming";
    case "售罄":
      return "status-sold";
    case "二手房":
      return "status-second";
    default:
      return "status-default";
  }
};

const navigateToDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/house/newHouse/detail?id=${id}`,
  });
};

// z-paging查询数据方法
const queryHouseList = (pageNo: number, pageSize: number) => {
  // 这里模拟分页数据请求
  pagingRef.value.complete(originalHouseList);
};

// 打开筛选框
const handleFilterOpen = () => {
  isFilterActive.value = true;
  showSearchBar.value = false;
};

// 关闭筛选框
const handleFilterClose = () => {
  isFilterActive.value = false;
  showSearchBar.value = true;
};

// 处理筛选变化
const handleFilterChange = (filters: any) => {
  isFilterActive.value = false;
  showSearchBar.value = true;
  Object.assign(currentFilters, filters);
  console.log("筛选条件已更新:", currentFilters);

  // 重新加载数据
  if (pagingRef.value) {
    pagingRef.value.reload();
  }
};

onMounted(() => {
  // 获取状态栏高度
  const { statusBarHeight: height } = uni.getSystemInfoSync();
  statusBarHeight.value = height || 0;

  // 获取粘性头部高度
  nextTick(() => {
    const query = uni.createSelectorQuery();
    query
      .select(".sticky-header")
      .boundingClientRect((data) => {
        const rect = data as UniApp.NodeInfo;
        if (rect && rect.height) {
          stickyHeaderHeight.value = rect.height;
        }
      })
      .exec();
  });
});
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--bg-page);
}

.simple-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 400rpx;
  background: linear-gradient(
    180deg,
    var(--primary-50) 0%,
    var(--bg-page) 100%
  );
  z-index: 0;
}

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: transparent;
  transition: background-color 0.3s ease;
  padding-bottom: 8rpx;

  &.filter-active {
    background-color: var(--bg-card);
  }
}

/* 搜索栏样式 */
.search-section {
  padding: 24rpx 32rpx;
  background-color: transparent;
}

.search-bar {
  display: flex;
  align-items: center;
  height: 80rpx;
  padding: 0 32rpx;
  background-color: var(--bg-card);
  border-radius: 40rpx;
  gap: 16rpx;
  box-shadow: var(--shadow-sm);
}

.search-placeholder {
  color: var(--text-grey);
}

/* 筛选栏样式 */
.filter-container {
  background: transparent;
}

.house-list-container {
  padding: 0 32rpx 24rpx;
}

.house-card-wrapper {
  margin-top: 32rpx;
  transition: transform 0.2s ease-in-out;

}

.house-card {
  background-color: var(--bg-card);
  border-radius: var(--radius-3);
  box-shadow: var(--shadow);
  overflow: hidden;
}

.house-image-wrapper {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 aspect ratio */
}

.house-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.property-type-badge {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  padding: var(--spacing) var(--spacing-2);
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-5);
  font-size: 24rpx;
  color: var(--text-inverse);
  font-weight: 500;
}

.house-content {
  padding: var(--spacing-3);
}

.title-status-line {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-2);
  gap: var(--spacing-2);
}

.house-title {
  font-size: 36rpx;
  font-weight: 600;

  line-height: var(--line-height-normal);
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.status-badge {
  padding: var(--spacing) 12rpx;
  border-radius: var(--radius);
  font-size: 24rpx;
  font-weight: 500;
  color: var(--text-inverse);
  white-space: nowrap;
}

.status-selling {
  background-color: var(--text-blue);
}
.status-pending-sale {
  background-color: var(--text-purple);
}
.status-hot {
  background-color: var(--text-red);
}
.status-coming {
  background-color: var(--text-green);
}
.status-sold {
  background-color: var(--text-grey);
}

.price-area {
  display: flex;
  align-items: baseline;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: var(--spacing-2);
}

.house-price {
  font-size: 40rpx;
  font-weight: 600;
  color: var(--text-red);
}

.price-pending {
  color: var(--text-yellow);
  font-size: 32rpx;
}

.special-tag {
  padding: var(--spacing) 12rpx;
  background-color: var(--bg-danger-light);
  border: 1rpx solid rgba(245, 44, 55, 0.2);
  border-radius: var(--radius);
  font-size: 24rpx;
  color: var(--text-red);
  font-weight: 500;
}

.house-location {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: var(--line-height-normal);
  margin-bottom: var(--spacing-2);
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: var(--spacing-2);
}

.tag {
  padding: var(--spacing) 12rpx;
  background-color: var(--bg-tag);
  border-radius: var(--radius);
  font-size: 24rpx;
  color: var(--text-secondary);
}

.attention-text {
  display: flex;
  align-items: center;
  gap: var(--spacing);
  font-size: 26rpx;
  color: var(--text-info);
}
</style>
