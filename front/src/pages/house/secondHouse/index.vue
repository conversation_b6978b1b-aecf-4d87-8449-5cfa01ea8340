<template>
  <view class="container">
    <!-- 使用z-paging组件实现页面滚动 -->
    <z-paging
      ref="paging"
      v-model="secondHandHouses"
      @query="queryHouseList"
      :refresher-enabled="true"
      :show-scrollbar="false"
      @scroll="onPageScroll"
    >
      <!-- 搜索区域 -->
      <view class="search-header">
        <view class="location">
          <text>北京</text>
          <text class="i-carbon-chevron-down text-20rpx ml-4rpx"></text>
        </view>
        <view class="search-box" @tap="goToSearch">
          <text
            class="i-solar:minimalistic-magnifer-linear text-28rpx text-info mr-12rpx"
          ></text>
          <text class="search-placeholder">搜索小区、地标、学校</text>
        </view>
      </view>

      <!-- 功能区导航栏 -->
      <view class="nav-list">
        <view
          v-for="(item, index) in functionNavs"
          :key="index"
          class="nav-item"
          :class="{ active: activeFunctionIndex === index }"
          @tap="switchFunction(index)"
        >
          <view class="nav-icon-wrapper">
            <text
              :class="[item.icon, 'nav-icon']"
              :style="{
                color:
                  activeFunctionIndex === index
                    ? 'var(--primary)'
                    : 'var(--text-secondary)',
              }"
            ></text>
          </view>
          <text
            class="nav-text"
            :style="{
              color:
                activeFunctionIndex === index
                  ? 'var(--primary)'
                  : 'var(--text-secondary)',
            }"
            >{{ item.name }}</text
          >
          <view
            v-if="activeFunctionIndex === index"
            class="nav-indicator"
          ></view>
        </view>
      </view>

      <!-- 筛选面板组件，吸顶后背景色集成父组件背景色-->
      <view
        class="filter-panel-wrapper"
        :class="{ 'bg-white': scrollTop > 150 }"
      >
        <SecondHouseFilterPanel
          ref="filterPanelRef"
          :initial-filters="filterParams"
          @filter-change="onFilterChange"
          @filter-reset="onFilterReset"
          @menuOpened="handleFilterMenuOpened"
        />
      </view>

      <!-- 房源列表 -->
      <view class="house-list">
        <view
          v-for="(house, index) in secondHandHouses"
          :key="index"
          class="card house-card"
          @tap="navigateToDetail(house)"
        >
          <!-- 房源图片 -->
          <view class="image-container">
            <image
              :src="house.image"
              mode="aspectFill"
              class="house-image"
              :lazy-load="true"
            />
            <!-- 新上标签 -->
            <view
              v-if="house.specialTags && house.specialTags.includes('新上')"
              class="new-tag"
              >新上</view
            >
          </view>

          <!-- 房源信息 -->
          <view class="house-info">
            <!-- 上半部分：标题和基本信息 -->
            <view class="info-top">
              <!-- 标题行 -->
              <text class="house-title">{{ house.title }}</text>

              <!-- 房源详情 -->
              <text class="details">{{ formatDetails(house) }}</text>

              <!-- 标签区域 -->
              <view class="tags-container">
                <view
                  v-for="(tag, tagIndex) in house.tags?.slice(0, 4)"
                  :key="tagIndex"
                  class="tag"
                  >{{ tag }}</view
                >
              </view>
            </view>

            <!-- 下半部分：价格信息 -->
            <view class="info-bottom">
              <view class="price-row">
                <text class="price">{{ house.price }}</text>
                <text class="price-unit">万</text>
                <text class="unit-price">{{ house.unitPrice }}</text>
              </view>
              <view class="special-tags">
                <text
                  v-for="(specialTag, tagIndex) in house.specialTags"
                  :key="tagIndex"
                  class="special-tag"
                  >{{ specialTag }}</text
                >
              </view>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import SecondHouseFilterPanel from "@/components/house/SecondHouseFilterPanel.vue";

// 筛选参数
const filterParams = reactive({
  area: "",
  price: "",
  layout: "",
  rentType: "",
  more: [],
});

// 功能导航当前选中索引
const activeFunctionIndex = ref(0);

// 功能导航数据 - 使用更美观的图标
const functionNavs = reactive([
  {
    name: "新房",
    icon: "i-carbon-building",
    type: "new",
    color: "#FF6B35",
  },
  {
    name: "二手房",
    icon: "i-carbon-home",
    type: "second",
    color: "#4ECDC4",
  },
  {
    name: "学区房",
    icon: "i-carbon-education",
    type: "school",
    color: "#45B7D1",
  },
  {
    name: "小户型",
    icon: "i-carbon-chart-area",
    type: "small",
    color: "#F1948A",
  },
]);

// 二手房列表数据
const secondHandHouses = ref([]);

// 分页组件引用
const paging = ref(null);

// 筛选面板引用
const filterPanelRef = ref(null);

// 页面滚动位置
const scrollTop = ref(0);

// 页面方法
const goBack = () => {
  uni.navigateBack();
};

const goToSearch = () => {
  uni.navigateTo({
    url: "/pages/house/search",
  });
};

// 功能导航切换
const switchFunction = (index: number) => {
  activeFunctionIndex.value = index;
  // 使用z-paging的reload方法重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 筛选条件变化
const onFilterChange = (filters) => {
  Object.assign(filterParams, filters);

  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 重置筛选条件
const onFilterReset = () => {
  Object.keys(filterParams).forEach((key) => {
    if (key === "more") {
      filterParams[key] = [];
    } else {
      filterParams[key] = "";
    }
  });

  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 处理筛选菜单打开事件
const handleFilterMenuOpened = () => {
  if (filterPanelRef.value) {
    const query = uni.createSelectorQuery();
    // @ts-ignore
    query
      .select(".filter-menu")
      .boundingClientRect((data) => {
        // @ts-ignore
        const rect = Array.isArray(data) ? data[0] : data;
        if (rect && rect.top !== 0) {
          uni.pageScrollTo({
            scrollTop: scrollTop.value + rect.top,
            duration: 300,
          });
        }
      })
      .exec();
  }
};

// 查询房源列表
const queryHouseList = (pageNo, pageSize) => {
  // 模拟异步请求
  setTimeout(() => {
    // 模拟第一页数据
    let houseData = [];

    if (pageNo === 1) {
      houseData = [
        {
          title: "南北通透三居室 精装修 满五唯一 业主诚心出售",
          location: "金茂府",
          roomType: "3室2厅",
          area: "128㎡",
          direction: "南北",
          floor: "中层/18层",
          tags: ["南北通透", "精装修", "近地铁"],
          buildYear: "建筑年代：2015年",
          propertyRight: "产权：70年",
          price: "890",
          unitPrice: "69,531元/㎡",
          specialTags: ["满五唯一", "随时看房"],
          image:
            "https://readdy.ai/api/search-image?query=modern%2520apartment%2520interior%2520with%2520large%2520windows%252C%2520bright%2520living%2520room%2520with%2520contemporary%2520furniture%252C%2520hardwood%2520floors%252C%2520minimalist%2520design%252C%2520natural%2520lighting%252C%2520spacious%2520and%2520clean%252C%2520real%2520estate%2520photography%2520style&width=240&height=180&seq=8&orientation=landscape",
        },
        {
          title: "高层景观房 全明户型 精装修 拎包入住",
          location: "万科如园",
          roomType: "4室2厅",
          area: "168㎡",
          direction: "东南",
          floor: "高层/26层",
          tags: ["高层景观", "全明户型", "学区房"],
          buildYear: "建筑年代：2018年",
          propertyRight: "产权：70年",
          price: "1280",
          unitPrice: "76,190元/㎡",
          specialTags: ["满二", "有钥匙", "新上"],
          image:
            "https://readdy.ai/api/search-image?query=luxury%2520condominium%2520with%2520city%2520view%252C%2520modern%2520high-rise%2520apartment%252C%2520large%2520balcony%252C%2520floor%2520to%2520ceiling%2520windows%252C%2520contemporary%2520design%252C%2520evening%2520lighting%252C%2520urban%2520living%252C%2520real%2520estate%2520photography&width=240&height=180&seq=9&orientation=landscape",
        },
        {
          title: "三室两厅 低楼层 采光好 交通便利 业主急售",
          location: "保利熙悦",
          roomType: "3室2厅",
          area: "115㎡",
          direction: "南北",
          floor: "低层/18层",
          tags: ["低楼层", "采光好", "交通便利"],
          buildYear: "建筑年代：2012年",
          propertyRight: "产权：70年",
          price: "720",
          unitPrice: "62,608元/㎡",
          specialTags: ["满五", "业主急售"],
          image:
            "https://readdy.ai/api/search-image?query=bright%2520apartment%2520interior%2520with%2520large%2520windows%252C%2520modern%2520living%2520room%252C%2520natural%2520light%252C%2520white%2520walls%252C%2520wooden%2520floors%252C%2520contemporary%2520furniture%252C%2520clean%2520and%2520spacious%252C%2520real%2520estate%2520photography%2520style&width=240&height=180&seq=10&orientation=landscape",
        },
        {
          title: "大四居 南北通透 环境优美 品质小区 近公园",
          location: "华润置地公园九里",
          roomType: "4室2厅",
          area: "198㎡",
          direction: "南北",
          floor: "中层/24层",
          tags: ["大四居", "南北通透", "环境优美"],
          buildYear: "建筑年代：2016年",
          propertyRight: "产权：70年",
          price: "1580",
          unitPrice: "79,797元/㎡",
          specialTags: ["满二", "随时看房", "新上"],
          image:
            "https://readdy.ai/api/search-image?query=luxury%2520residential%2520complex%2520with%2520garden%2520view%252C%2520modern%2520architecture%252C%2520green%2520surroundings%252C%2520family-friendly%2520environment%252C%2520spacious%2520outdoor%2520areas%252C%2520real%2520estate%2520photography%2520style&width=240&height=180&seq=12&orientation=landscape",
        },
        {
          title: "豪华装修 一线江景房 品质小区 名校学区",
          location: "远洋天著",
          roomType: "3室2厅",
          area: "145㎡",
          direction: "东南",
          floor: "高层/32层",
          tags: ["豪华装修", "江景房", "品质小区"],
          buildYear: "建筑年代：2019年",
          propertyRight: "产权：70年",
          price: "1680",
          unitPrice: "115,862元/㎡",
          specialTags: ["满二", "学区房"],
          image:
            "https://readdy.ai/api/search-image?query=luxury%2520apartment%2520with%2520river%2520view%252C%2520high-end%2520interior%2520design%252C%2520premium%2520finishes%252C%2520large%2520windows%252C%2520elegant%2520furniture%252C%2520sophisticated%2520decor%252C%2520real%2520estate%2520photography%2520style&width=240&height=180&seq=13&orientation=landscape",
        },
      ];
    } else {
      // 模拟加载更多数据
      houseData = [
        {
          title: "精装三居 南向 采光好 近地铁 满五唯一",
          location: "首开华润城",
          roomType: "3室2厅",
          area: "118㎡",
          direction: "南向",
          floor: "中层/22层",
          tags: ["精装修", "南向", "近地铁"],
          buildYear: "建筑年代：2017年",
          propertyRight: "产权：70年",
          price: "680",
          unitPrice: "57,627元/㎡",
          specialTags: ["满五唯一", "随时看房"],
          image:
            "https://readdy.ai/api/search-image?query=modern%2520three%2520bedroom%2520apartment%2520interior%252C%2520bright%2520living%2520space%252C%2520contemporary%2520design%252C%2520large%2520windows%252C%2520natural%2520light%252C%2520clean%2520aesthetic%252C%2520real%2520estate%2520photography%2520style&width=240&height=180&seq=14&orientation=landscape",
        },
        {
          title: "全新毛坯 大三居 视野开阔 赠送面积多",
          location: "万科翡翠公园",
          roomType: "3室2厅",
          area: "142㎡",
          direction: "东南",
          floor: "高层/28层",
          tags: ["毛坯房", "大三居", "视野开阔"],
          buildYear: "建筑年代：2020年",
          propertyRight: "产权：70年",
          price: "950",
          unitPrice: "66,901元/㎡",
          specialTags: ["满二", "有钥匙", "新上"],
          image:
            "https://readdy.ai/api/search-image?query=empty%2520apartment%2520interior%2520with%2520large%2520windows%252C%2520unfurnished%2520space%252C%2520white%2520walls%252C%2520concrete%2520floors%252C%2520spacious%2520rooms%252C%2520bright%2520natural%2520lighting%252C%2520real%2520estate%2520photography%2520style&width=240&height=180&seq=15&orientation=landscape",
        },
      ];
    }

    // 根据功能导航过滤
    const functionType = functionNavs[activeFunctionIndex.value].type;
    let filteredData = [...houseData];

    if (functionType === "school") {
      filteredData = filteredData.filter(
        (house) =>
          house.tags.includes("学区房") || house.specialTags.includes("学区房")
      );
    } else if (functionType === "small") {
      filteredData = filteredData.filter((house) => {
        const area = parseInt(house.area);
        return !isNaN(area) && area <= 70;
      });
    }

    // 根据筛选参数过滤
    if (filterParams.area) {
      filteredData = filteredData.filter((house) =>
        house.location.includes(filterParams.area)
      );
    }

    if (filterParams.price) {
      const [min, max] = filterParams.price.split("-").map(Number);
      if (min && max) {
        filteredData = filteredData.filter((house) => {
          const price = Number(house.price);
          return price >= min && price <= max;
        });
      } else if (min) {
        filteredData = filteredData.filter(
          (house) => Number(house.price) >= min
        );
      } else if (max) {
        filteredData = filteredData.filter(
          (house) => Number(house.price) <= max
        );
      }
    }

    if (filterParams.layout) {
      filteredData = filteredData.filter((house) => {
        const roomCount = house.roomType.match(/(\d+)室/);
        return roomCount && roomCount[1] === filterParams.layout;
      });
    }

    if (filterParams.more && filterParams.more.length > 0) {
      filteredData = filteredData.filter((house) => {
        return filterParams.more.some((tag) => {
          // 处理朝向
          if (tag === "south-north") return house.direction === "南北";
          if (tag === "south")
            return house.direction === "南向" || house.direction === "南";
          if (tag === "east")
            return (
              house.direction === "东向" ||
              house.direction === "东" ||
              house.direction === "东南" ||
              house.direction === "东北"
            );
          if (tag === "west")
            return (
              house.direction === "西向" ||
              house.direction === "西" ||
              house.direction === "西南" ||
              house.direction === "西北"
            );
          if (tag === "north")
            return house.direction === "北向" || house.direction === "北";

          // 处理楼层
          if (tag === "low") return house.floor.includes("低");
          if (tag === "middle") return house.floor.includes("中");
          if (tag === "high") return house.floor.includes("高");

          // 处理装修
          if (tag === "fine")
            return (
              house.tags.includes("精装修") || house.tags.includes("豪华装修")
            );
          if (tag === "simple") return house.tags.includes("简装修");
          if (tag === "rough") return house.tags.includes("毛坯房");

          // 处理特色
          if (tag === "five-only")
            return house.specialTags.includes("满五唯一");
          if (tag === "two-year") return house.specialTags.includes("满二");
          if (tag === "near-subway") return house.tags.includes("近地铁");
          if (tag === "school")
            return (
              house.tags.includes("学区房") ||
              house.specialTags.includes("学区房")
            );
          if (tag === "through") return house.tags.includes("南北通透");
          if (tag === "elevator") return house.tags.includes("电梯房");
          if (tag === "new") return house.specialTags.includes("新上");

          // 处理面积
          if (tag.includes("-")) {
            const [min, max] = tag.split("-").map(Number);
            const area = parseInt(house.area);
            if (!isNaN(area)) {
              if (max) {
                return area >= min && area <= max;
              } else {
                return area >= min;
              }
            }
          }

          return house.tags.includes(tag) || house.specialTags.includes(tag);
        });
      });
    }

    // 告知z-paging加载完成
    paging.value.complete(filteredData);
  }, 1000);
};

// 导航到详情页
const navigateToDetail = (house) => {
  uni.navigateTo({
    url: `/pages/house/detail/index?id=${house.id || "1"}&type=secondHouse`,
  });
};

// 格式化详情
const formatDetails = (house) => {
  return `${house.roomType} | ${house.area} | ${house.direction} | ${house.floor}`;
};

// 页面滚动监听
const onPageScroll = (e) => {
  scrollTop.value = e.detail.scrollTop;
};
</script>

<style lang="scss" scoped>
.container {
  /* 顶部搜索栏 */
  .search-header {
    display: flex;
    align-items: center;
    padding: 20rpx;
    background-color: transparent; // 确保搜索栏是透明的，显示背景渐变
  }

  // 给导航栏透明背景
  :deep(.uni-navbar) {
    background-color: transparent !important;
  }

  :deep(.uni-navbar__header) {
    background-color: transparent !important;
  }

  .location {
    display: flex;
    align-items: center;
    padding: 0 20rpx;
    font-size: 28rpx;
    color: var(--text-main);
  }

  .search-box {
    flex: 1;
    display: flex;
    align-items: center;
    height: 72rpx;
    background-color: var(--bg-card);
    border-radius: 32rpx;
    padding: 0 24rpx;
    margin: 0 20rpx;
    transition: all 0.3s ease;


  }

  .search-placeholder {
    font-size: 28rpx;
    color: #999;
    margin-left: 12rpx;
  }

  // 筛选面板样式
  .filter-panel-wrapper {
    position: sticky;
    top: 0;
    z-index: 99;
  }

  .nav-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16rpx;
    padding: 24rpx 32rpx 16rpx;
    margin-top: 8rpx;
    border-radius: 16rpx 16rpx 0 0;
  }

  .nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12rpx 0;
    position: relative;
    transition: all 0.3s ease;

    &.active {
      .nav-text {
        font-weight: 600;
      }
    }
  }

  .nav-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8rpx;
  }

  .nav-icon {
    font-size: 44rpx;
  }

  .nav-text {
    font-size: 24rpx;
    transition: all 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
  }

  .nav-indicator {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 24rpx;
    height: 4rpx;
    background-color: var(--primary);
    border-radius: 4rpx;
  }

  /* 房源列表 */
  .house-list {
    display: flex;
    flex-direction: column;
    padding: 20rpx;
    gap: 20rpx;
  }

  /* 房源卡片 */
  .house-card {
    display: flex;
    padding: 0 !important;
    transition: all 0.3s ease;


  }

  .image-container {
    position: relative;
    width: 240rpx;
    height: 240rpx;
    flex-shrink: 0;
  }

  .house-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-top-left-radius: 16rpx;
    border-bottom-left-radius: 16rpx;
  }

  .new-tag {
    position: absolute;
    top: 12rpx;
    left: 12rpx;
    background-color: var(--primary);
    color: white;
    font-size: 20rpx;
    padding: 4rpx 12rpx;
    border-radius: 4rpx;
  }

  /* 房源信息 */
  .house-info {
    flex: 1;
    padding: 16rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .info-top {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10rpx;
  }

  .info-bottom {
    margin-top: 8rpx;
  }

  .house-title {
    font-size: 32rpx;
    font-weight: 500;
    line-height: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .price-row {
    display: flex;
    align-items: baseline;
  }

  .price {
    font-size: 36rpx;
    font-weight: 500;
    color: var(--text-red);
  }

  .price-unit {
    font-size: 26rpx;
    color: var(--text-red);
    margin-left: 6rpx;
  }

  .unit-price {
    font-size: 24rpx;
    color: var(--text-grey);
    margin-left: 16rpx;
  }

  .details {
    font-size: 24rpx;
    color: var(--text-info);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 500;
  }

  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8rpx;
    margin-top: 8rpx;
  }

  .tag {
    padding: 6rpx 12rpx;
    background-color: var(--bg-tag);
    color: var(--text-secondary);
    font-size: 24rpx;
    border-radius: 8rpx;
    line-height: 1.4;
  }

  .special-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8rpx;
    margin-top: 8rpx;
  }

  .special-tag {
    padding: 6rpx 12rpx;
    background-color: rgba(255, 90, 95, 0.1);
    color: var(--text-red);
    font-size: 24rpx;
    border-radius: 8rpx;
    line-height: 1.4;
  }
}
</style>
