<template>
  <view class="publish-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="nav-left" @click="goBack">
          <uni-icons type="close" size="24" color="#1F2937"></uni-icons>
        </view>
        <text class="nav-title">发布动态</text>
        <view class="nav-right">
          <view 
            class="publish-btn" 
            :class="{ disabled: !canPublish }"
            @click="handlePublish"
          >
            {{ isPublishing ? '发布中...' : '发布' }}
          </view>
        </view>
      </view>
    </view>
    
    <scroll-view scroll-y class="content-scroll">
      <!-- 内容输入区 -->
      <view class="content-section">
        <textarea
          v-model="postContent"
          class="content-textarea"
          placeholder="分享你的精彩时刻..."
          :maxlength="500"
          :auto-height="true"
          :show-confirm-bar="false"
          :cursor-spacing="20"
        />
        <view class="char-count">{{ postContent.length }}/500</view>
      </view>

      <!-- 图片上传区 -->
      <view class="image-section">
        <view class="image-grid">
          <view 
            v-for="(image, index) in selectedImages" 
            :key="index" 
            class="image-item"
          >
            <image :src="image.url" mode="aspectFill" class="uploaded-image" />
            <view class="image-delete" @click="removeImage(index)">
              <uni-icons type="close" size="14" color="#fff"></uni-icons>
            </view>
          </view>
          <view 
            v-if="selectedImages.length < 9" 
            class="add-image-btn" 
            @click="chooseImages"
          >
            <uni-icons type="camera-filled" size="28" color="#8B5CF6"></uni-icons>
          </view>
        </view>
      </view>

      <!-- 功能选项 -->
      <view class="options-section">
        <!-- 话题选择 -->
        <view class="option-item" @click="goToTopicSelect">
          <view class="option-icon">
            <uni-icons type="flag" size="20" color="#8B5CF6"></uni-icons>
          </view>
          <view class="option-content">
            <text class="option-title">添加话题</text>
            <text class="option-desc" v-if="selectedTopics.length === 0">让更多人发现你的内容</text>
            <view v-else class="selected-topics">
              <text 
                v-for="(topic, index) in selectedTopics" 
                :key="index" 
                class="topic-tag"
              >
                {{ topic }}
              </text>
            </view>
          </view>
          <uni-icons type="arrowright" size="16" color="#C7C7CC"></uni-icons>
        </view>
        
        <!-- 位置信息 -->
        <view class="option-item" @click="chooseLocation">
          <view class="option-icon">
            <uni-icons type="location" size="20" color="#10B981"></uni-icons>
          </view>
          <view class="option-content">
            <text class="option-title">添加位置</text>
            <text class="option-desc">{{ selectedLocation || '你在哪里？' }}</text>
          </view>
          <uni-icons type="arrowright" size="16" color="#C7C7CC"></uni-icons>
        </view>
        
        <!-- 隐私设置 -->
        <view class="option-item">
          <view class="option-icon">
            <uni-icons type="eye" size="20" color="#F59E0B"></uni-icons>
          </view>
          <view class="option-content">
            <text class="option-title">谁可以看到</text>
            <picker @change="onPrivacyChange" :value="privacyIndex" :range="privacyOptions">
              <text class="option-desc">{{ privacyOptions[privacyIndex] }}</text>
            </picker>
          </view>
          <uni-icons type="arrowright" size="16" color="#C7C7CC"></uni-icons>
        </view>
      </view>
      
      <!-- 底部安全距离 -->
      <view class="safe-bottom"></view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';
import CustomNavBar from '@/components/CustomNavBar.vue';

interface ImageItem {
  url: string;
  path: string;
}

const postContent = ref('');
const selectedImages = ref<ImageItem[]>([]);
const selectedTopics = ref<string[]>([]);
const selectedLocation = ref('');
const privacyIndex = ref(0);
const isPublishing = ref(false);

const privacyOptions = ['所有人可见', '仅关注者可见', '仅自己可见'];

const userInfo = {
  name: '我',
  avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
};

const canPublish = computed(() => {
  return postContent.value.trim().length > 0 || selectedImages.value.length > 0;
});

onLoad((options) => {
  // 如果从话题选择页面返回，获取选中的话题
  if (options && options.topics) {
    try {
      const topics = JSON.parse(decodeURIComponent(options.topics));
      selectedTopics.value = topics;
    } catch (e) {
      console.error('解析话题数据失败:', e);
    }
  }
});

// 监听话题选择事件
const topicSelectedHandler = (topics: string[]) => {
  selectedTopics.value = topics;
};

onShow(() => {
  uni.$on('topicSelected', topicSelectedHandler);
});

onUnmounted(() => {
  uni.$off('topicSelected', topicSelectedHandler);
});

const chooseImages = () => {
  const remainingCount = 9 - selectedImages.value.length;
  
  uni.chooseImage({
    count: remainingCount,
    sizeType: ['compressed'],
    sourceType: ['camera', 'album'],
    success: (res) => {
      const paths = Array.isArray(res.tempFilePaths) ? res.tempFilePaths : [res.tempFilePaths];
      const newImages = paths.map(path => ({
        url: path,
        path: path
      }));
      selectedImages.value.push(...newImages);
    },
    fail: (err) => {
      console.error('选择图片失败:', err);
      uni.showToast({
        title: '选择图片失败',
        icon: 'none'
      });
    }
  });
};

const removeImage = (index: number) => {
  selectedImages.value.splice(index, 1);
};

const goToTopicSelect = () => {
  const currentTopics = encodeURIComponent(JSON.stringify(selectedTopics.value));
  uni.navigateTo({
    url: `/pages/dating/square/topic?selected=${currentTopics}`
  });
};

const removeTopic = (index: number) => {
  selectedTopics.value.splice(index, 1);
};

const chooseLocation = () => {
  uni.chooseLocation({
    success: (res) => {
      selectedLocation.value = res.name || res.address;
    },
    fail: (err) => {
      console.error('选择位置失败:', err);
      uni.showToast({
        title: '选择位置失败',
        icon: 'none'
      });
    }
  });
};

const onPrivacyChange = (e: any) => {
  privacyIndex.value = e.detail.value;
};

const goBack = () => {
  uni.navigateBack();
};

const handlePublish = async () => {
  if (!canPublish.value || isPublishing.value) return;

  isPublishing.value = true;

  try {
    // 模拟发布过程
    await new Promise(resolve => setTimeout(resolve, 2000));

    uni.showToast({
      title: '发布成功',
      icon: 'success'
    });

    // 返回上一页
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);

  } catch (error) {
    console.error('发布失败:', error);
    uni.showToast({
      title: '发布失败，请重试',
      icon: 'none'
    });
  } finally {
    isPublishing.value = false;
  }
};
</script>

<style scoped lang="scss">
.publish-container {
  background-color: #fff;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

// 自定义导航栏
.custom-navbar {
  background-color: #fff;
  border-bottom: 1rpx solid #F3F4F6;
  padding-top: calc(var(--status-bar-height) + 20rpx);
  padding-bottom: 20rpx;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}

.nav-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1F2937;
}

.nav-right {
  width: 120rpx;
  display: flex;
  justify-content: flex-end;
}

.publish-btn {
  background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%);
  color: white;
  font-size: 28rpx;
  font-weight: 600;
  padding: 16rpx 24rpx;
  border-radius: 24rpx;
  transition: all 0.2s ease;
  
  &.disabled {
    background: #E5E7EB;
    color: #9CA3AF;
  }
  
  
}

.content-scroll {
  flex: 1;
  background-color: #fff;
}

.content-section {
  padding: 32rpx;
  border-bottom: 1rpx solid #F3F4F6;
}

.content-textarea {
  width: 100%;
  min-height: 300rpx;
  font-size: 32rpx;
  color: #1F2937;
  line-height: 1.6;
  background: transparent;
  border: none;
  outline: none;
  resize: none;

  &::placeholder {
    color: #9CA3AF;
  }
}

.char-count {
  font-size: 24rpx;
  color: #9CA3AF;
  text-align: right;
  margin-top: 16rpx;
}

.image-section {
  padding: 32rpx;
  border-bottom: 1rpx solid #F3F4F6;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12rpx;
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #F5F5F5;
}

.uploaded-image {
  width: 100%;
  height: 100%;
}

.image-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 28rpx;
  height: 28rpx;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-image-btn {
  aspect-ratio: 1;
  border: 2rpx dashed #D1D5DB;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FAFAFA;
  transition: all 0.2s ease;
  

}

.options-section {
  padding: 0;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F3F4F6;
  transition: background-color 0.2s ease;
  
  &:last-child {
    border-bottom: none;
  }

}

.option-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  background-color: #F8FAFC;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #1F2937;
  margin-bottom: 6rpx;
  display: block;
}

.option-desc {
  font-size: 26rpx;
  color: #9CA3AF;
}

.selected-topics {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 12rpx;
}

.topic-tag {
  background-color: #EEF2FF;
  color: #6366F1;
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-weight: 500;
}

.safe-bottom {
  height: 120rpx;
}
</style> 