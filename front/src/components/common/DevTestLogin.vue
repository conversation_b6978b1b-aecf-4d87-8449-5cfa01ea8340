<template>
  <tui-bottom-popup
    :show="show"
    z-index="********"
    @close="handleClose"
    :radius="true"
  >
    <view class="dev-login-content">
      <view class="dev-login-header">
        <view class="dev-login-title">开发测试登录</view>
        <view class="dev-login-subtitle">选择测试账号快速登录</view>
      </view>

      <view class="test-accounts">
        <view
          v-for="account in testAccounts"
          :key="account.id"
          class="account-item"
          @click="handleAccountLogin(account)"
        >
          <image
            class="account-avatar"
            :src="account.avatar"
            mode="aspectFill"
          ></image>
          <view class="account-info">
            <view class="account-nickname">{{ account.nickname }}</view>
            <view class="account-mobile">{{ account.mobile }}</view>
            <view class="account-role">{{ account.roleDescription }}</view>
          </view>
          <view class="account-login-btn">
            <text class="login-text">登录</text>
          </view>
        </view>
      </view>

      <view class="dev-login-footer">
        <button class="cancel-btn" @click="handleClose">取消</button>
        <button class="normal-login-btn" @click="handleNormalLogin">
          使用正常登录
        </button>
      </view>

      <view class="dev-warning">
        <text class="warning-text">⚠️ 此功能仅在开发环境可用</text>
      </view>
    </view>
  </tui-bottom-popup>
</template>

<script setup lang="ts">
import { ref } from "vue";
import type { UserInfo } from "@/types/user";
import AuthApi from "@/api/auth";

interface TestAccount {
  id: number;
  nickname: string;
  mobile: string;
  avatar: string;
  roleDescription: string;
  userData: UserInfo;
}

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["login", "close", "normalLogin"]);

// 测试账号数据
const testAccounts = ref<TestAccount[]>([
  {
    id: 1,
    nickname: "测试用户-求职者",
    mobile: "***********",
    avatar: "/static/images/avatar-male.png",
    roleDescription: "普通求职者账号",
    userData: {
      id: 10001,
      uid: "test_user_001",
      nickname: "测试用户-求职者",
      avatar: "/static/images/avatar-male.png",
      gender: 1,
      birthday: "1995-06-15",
      phone: "***********",
      email: "<EMAIL>",
      isVip: false,
      vipExpiresAt: "",
      isSuperAdmin: false,
      posts: 5,
      followers: 12,
      following: 20,
      balance: 0,
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-07-23T00:00:00Z",
      personalVerified: false,
      enterpriseVerified: false,
      memberLevel: 1,
    },
  },
  {
    id: 2,
    nickname: "测试企业-招聘方",
    mobile: "***********",
    avatar: "/static/images/avatar-female.png",
    roleDescription: "企业招聘账号",
    userData: {
      id: 10002,
      uid: "test_user_002",
      nickname: "测试企业-招聘方",
      avatar: "/static/images/avatar-female.png",
      gender: 2,
      birthday: "1988-03-20",
      phone: "***********",
      email: "<EMAIL>",
      isVip: true,
      vipExpiresAt: "2025-12-31T23:59:59Z",
      isSuperAdmin: false,
      posts: 15,
      followers: 50,
      following: 30,
      balance: 10000,
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-07-23T00:00:00Z",
      personalVerified: true,
      enterpriseVerified: true,
      memberLevel: 3,
    },
  },
  {
    id: 3,
    nickname: "测试管理员",
    mobile: "***********",
    avatar: "/static/images/default-avatar.png",
    roleDescription: "超级管理员账号",
    userData: {
      id: 10003,
      uid: "test_admin_003",
      nickname: "测试管理员",
      avatar: "/static/images/default-avatar.png",
      gender: 1,
      birthday: "1985-12-10",
      phone: "***********",
      email: "<EMAIL>",
      isVip: true,
      vipExpiresAt: "2099-12-31T23:59:59Z",
      isSuperAdmin: true,
      posts: 100,
      followers: 500,
      following: 100,
      balance: 99999,
      createdAt: "2023-01-01T00:00:00Z",
      updatedAt: "2024-07-23T00:00:00Z",
      personalVerified: true,
      enterpriseVerified: true,
      memberLevel: 5,
    },
  },
]);

// 处理账号登录
const handleAccountLogin = async (account: TestAccount) => {
  uni.showLoading({ title: "登录中..." });

  try {
    // 响应拦截器已经处理了业务逻辑，成功时直接返回 data 部分
    const authData = await AuthApi.devTestLogin(account.mobile);

    const loginResult = {
      user: authData.user,
      accessToken: authData.access_token,
    };

    emit("login", loginResult);
    uni.showToast({
      title: `${account.nickname} 登录成功`,
      icon: "success",
    });
  } catch (error) {
    console.error("开发登录失败:", error);
    uni.showToast({
      title: error.message || "登录失败，请重试",
      icon: "none",
    });
  } finally {
    uni.hideLoading();
  }
};

// 处理取消
const handleClose = () => {
  emit("close");
};

// 处理使用正常登录
const handleNormalLogin = () => {
  emit("normalLogin");
};
</script>

<style lang="scss" scoped>
.dev-login-content {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 40rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.dev-login-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.dev-login-title {
  font-size: 36rpx;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 12rpx;
}

.dev-login-subtitle {
  font-size: 26rpx;
  color: #666;
  opacity: 0.8;
}

.test-accounts {
  margin-bottom: 40rpx;
}

.account-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  margin-bottom: 16rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);


  &:last-child {
    margin-bottom: 0;
  }
}

.account-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  background-color: #ddd;
}

.account-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.account-nickname {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.account-mobile {
  font-size: 24rpx;
  color: #666;
}

.account-role {
  font-size: 22rpx;
  color: var(--primary);
  background-color: rgba(102, 126, 234, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  align-self: flex-start;
}

.account-login-btn {
  background: linear-gradient(135deg, var(--primary) 0%, #667eea 100%);
  color: #fff;
  padding: 16rpx 28rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  transition: all 0.2s ease;

}

.dev-login-footer {
  display: flex;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.cancel-btn,
.normal-login-btn {
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 16rpx;
  font-size: 26rpx;
  text-align: center;
  border: none;
}

.cancel-btn {
  flex: 1;
  background-color: #f8f9fa;
  color: #666;
}

.normal-login-btn {
  flex: 2;
  background-color: var(--primary);
  color: #fff;
}

.dev-warning {
  text-align: center;
  padding: 16rpx;
  background-color: #fff3cd;
  border-radius: 8rpx;
  border: 1rpx solid #ffeaa7;
}

.warning-text {
  font-size: 22rpx;
  color: #856404;
}
</style>
