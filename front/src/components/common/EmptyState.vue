<template>
  <view class="empty-state" :class="emptyClass">
    <view class="empty-content">
      <!-- 空状态图标容器 -->
      <view class="empty-icon-container">
        <view class="empty-icon">
          <!-- 如果是图片路径，使用image标签 -->
          <image
            v-if="config.icon"
            :src="config.icon"
            class="empty-image"
            mode="aspectFit"
          />
          <!-- 否则使用icon字体 -->
          <text
            v-else
            :class="config.icon || 'i-solar:inbox-out-broken'"
          ></text>
        </view>
      </view>

      <!-- 空状态文本内容 -->
      <view class="empty-text-content">
        <!-- 空状态标题 -->
        <text v-if="config.title" class="empty-title">
          {{ config.title }}
        </text>

        <!-- 空状态描述 -->
        <text v-if="config.message" class="empty-message">
          {{ config.message }}
        </text>
      </view>

      <!-- 操作按钮 -->
      <button
        v-if="config.actionText && (config.onAction || $slots.action)"
        class="empty-action-btn"
        @click="handleAction"
      >
        <slot name="action">
          <text class="action-text">{{ config.actionText }}</text>
        </slot>
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";
import type { EmptyConfig } from "@/utils/ui/page-state";
import { DEFAULT_EMPTY_CONFIG } from "@/utils/ui/page-state";

interface Props {
  config?: Partial<EmptyConfig>;
  fullHeight?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  config: () => ({}),
  fullHeight: true,
});

const emit = defineEmits<{
  action: [];
}>();

// 合并配置
const config = computed(() => ({
  ...DEFAULT_EMPTY_CONFIG,
  ...props.config,
}));

// 动态类名
const emptyClass = computed(() => ({
  "empty-state--full-height": props.fullHeight,
}));

// 处理操作按钮点击
const handleAction = () => {
  if (config.value.onAction) {
    config.value.onAction();
  }
  emit("action");
};
</script>

<style lang="scss" scoped>
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
  min-height: 50vh;

  &--full-height {
    min-height: 60vh;
  }
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 520rpx;
  width: 100%;
}

// 图标容器样式
.empty-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 48rpx;
}

.empty-icon {
  position: relative;
  z-index: 2;
  font-size: 160rpx;
  color: var(--text-info);
  opacity: 0.7;
  transition: all 0.3s ease;

  text {
    display: inline-block;
    background: linear-gradient(
      135deg,
      var(--text-info) 0%,
      rgba(var(--text-info-rgb), 0.6) 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .empty-image {
    width: 480rpx;
    height: 480rpx;
  }
}

// 文本内容容器
.empty-text-content {
  margin-bottom: 40rpx;
}

.empty-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;

  line-height: 1.4;
  margin-bottom: 16rpx;
  letter-spacing: 0.5rpx;
}

.empty-message {
  display: block;
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.6;
  text-align: center;
  opacity: 0.8;
  max-width: 400rpx;
}

.empty-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  background: linear-gradient(
    135deg,
    var(--primary) 0%,
    var(--primary-600) 100%
  );
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8rpx 24rpx rgba(var(--primary-rgb), 0.2);
  min-width: 200rpx;

  .action-text {
    color: white;
    font-weight: 500;
  }

  &:hover {
    transform: translateY(-4rpx);
    box-shadow: 0 12rpx 32rpx rgba(var(--primary-rgb), 0.3);
  }


}

// 主题变体
.empty-state {
  // 暗色模式适配
  @media (prefers-color-scheme: dark) {
    .empty-icon-bg {
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.05) 0%,
        rgba(255, 255, 255, 0.01) 100%
      );
    }

    .empty-icon {
      color: var(--text-info);
      opacity: 0.6;

      text {
        background: linear-gradient(
          135deg,
          var(--text-info) 0%,
          rgba(var(--text-info-rgb), 0.5) 100%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }

    .empty-action-btn {
      background: linear-gradient(
        135deg,
        var(--primary-400) 0%,
        var(--primary-500) 100%
      );
      box-shadow: 0 8rpx 24rpx rgba(var(--primary-rgb), 0.15);

      &:hover {
        box-shadow: 0 12rpx 32rpx rgba(var(--primary-rgb), 0.25);
      }
    }
  }
}

// 简洁变体
.empty-state--minimal {
  padding: 40rpx 24rpx;
  min-height: 30vh;

  .empty-icon-container {
    margin-bottom: 32rpx;
  }

  .empty-icon-bg {
    width: 120rpx;
    height: 120rpx;
  }

  .empty-icon {
    font-size: 100rpx;

    .empty-image {
      width: 100rpx;
      height: 100rpx;
    }
  }

  .empty-text-content {
    margin-bottom: 24rpx;
  }

  .empty-title {
    font-size: 30rpx;
    margin-bottom: 12rpx;
  }

  .empty-message {
    font-size: 24rpx;
  }
}

// 动画效果
@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8rpx);
  }
}

.empty-icon-container:hover .empty-icon {
  animation: float 2s ease-in-out infinite;
}
</style>
