<!--
  通用数值选择器组件
  统一管理数值增减逻辑，避免重复代码
-->
<template>
  <view class="count-selector">
    <view class="count-control">
      <view
        class="count-btn"
        :class="{ disabled: modelValue <= min }"
        @click="decrease"
      >
        <text class="count-icon">-</text>
      </view>
      <text class="count-number">{{ modelValue }}</text>
      <view
        class="count-btn"
        :class="{ disabled: modelValue >= max }"
        @click="increase"
      >
        <text class="count-icon">+</text>
      </view>
    </view>
    <text v-if="unit" class="count-unit">{{ unit }}</text>
  </view>
</template>

<script setup lang="ts">
interface Props {
  modelValue: number
  min?: number
  max?: number
  step?: number
  unit?: string
  vibrate?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: number): void
  (e: 'change', value: number): void
}

const props = withDefaults(defineProps<Props>(), {
  min: 1,
  max: 99,
  step: 1,
  vibrate: true
})

const emit = defineEmits<Emits>()

const updateValue = (newValue: number) => {
  const clampedValue = Math.max(props.min, Math.min(props.max, newValue))
  emit('update:modelValue', clampedValue)
  emit('change', clampedValue)
  
  // 触觉反馈
  if (props.vibrate && clampedValue !== props.modelValue) {
    uni.vibrateShort({ type: 'light' })
  }
}

const increase = () => {
  if (props.modelValue < props.max) {
    updateValue(props.modelValue + props.step)
  }
}

const decrease = () => {
  if (props.modelValue > props.min) {
    updateValue(props.modelValue - props.step)
  }
}
</script>

<style lang="scss" scoped>
.count-selector {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.count-control {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  background: var(--bg-search);
  border-radius: var(--radius-2);
  padding: var(--spacing-2);
}

.count-btn {
  min-width: 88rpx;
  min-height: 88rpx;
  width: 88rpx;
  height: 88rpx;
  background: var(--bg-card);
  border-radius: var(--radius-2);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid var(--border-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  /* 确保触摸目标足够大 */
  @media screen and (max-width: 750rpx) {
    min-width: 100rpx;
    min-height: 100rpx;
    width: 100rpx;
    height: 100rpx;
  }

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: var(--primary);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0.2;
  }

  &:active:not(.disabled) {
    transform: scale(0.92);
    border-color: var(--primary);

    &::before {
      width: 100rpx;
      height: 100rpx;
    }

    .count-icon {
      color: var(--primary);
      transform: scale(1.1);
    }
  }

  &.disabled {
    opacity: 0.3;
    cursor: not-allowed;

    &::before {
      display: none;
    }
  }
}

.count-icon {
  font-size: 32rpx;
  font-weight: 700;
  color: var(--text-base);
  line-height: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;
}

.count-number {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-base);
  min-width: 60rpx;
  text-align: center;
}

.count-unit {
  font-size: 28rpx;
  color: var(--text-secondary);
  font-weight: 500;
}
</style>