<!--
  通用弹窗基础组件
  统一管理弹窗的样式、结构和行为，避免重复代码
-->
<template>
  <tui-bottom-popup :show="show" @close="handleClose" :height="height">
    <view class="popup-base">
      <!-- 弹窗头部 -->
      <view class="popup-header">
        <text class="popup-title">{{ title }}</text>
        <view class="popup-close" @click="handleClose">
          <text class="close-icon">×</text>
        </view>
      </view>

      <!-- 弹窗内容 -->
      <view class="popup-content" :class="{ 'with-footer': $slots.footer }">
        <slot />
      </view>

      <!-- 弹窗底部 -->
      <view v-if="$slots.footer || showConfirm" class="popup-footer">
        <slot name="footer">
          <tui-button
            v-if="showConfirm"
            type="primary"
            width="100%"
            height="88rpx"
            :disabled="confirmDisabled"
            @click="handleConfirm"
          >
            {{ confirmText }}
          </tui-button>
        </slot>
      </view>
    </view>
  </tui-bottom-popup>
</template>

<script setup lang="ts">
interface Props {
  show: boolean
  title: string
  height?: number | string
  showConfirm?: boolean
  confirmText?: string
  confirmDisabled?: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'confirm'): void
}

const props = withDefaults(defineProps<Props>(), {
  height: 600,
  showConfirm: true,
  confirmText: '确定',
  confirmDisabled: false
})

const emit = defineEmits<Emits>()

const handleClose = () => {
  emit('close')
}

const handleConfirm = () => {
  emit('confirm')
}
</script>

<style lang="scss" scoped>
.popup-base {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--bg-card);
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4);
  border-bottom: 2rpx solid var(--border-light);
  background: var(--bg-card);
  flex-shrink: 0;
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-base);
}

.popup-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--bg-search);
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
    background: var(--bg-tag);
  }
}

.close-icon {
  font-size: 36rpx;
  color: var(--text-secondary);
  font-weight: 300;
  line-height: 1;
}

.popup-content {
  flex: 1;
  overflow-y: auto;
  background: var(--bg-page);

  /* 当有footer时，调整滚动区域 */
  &.with-footer {
    padding-bottom: var(--spacing-2);
  }
}

.popup-footer {
  padding: var(--spacing-4);
  border-top: 2rpx solid var(--border-light);
  background: var(--bg-card);
  flex-shrink: 0;
}

/* 响应式优化 */
@media screen and (max-width: 750rpx) {
  .popup-header {
    padding: var(--spacing-3);
  }
  
  .popup-footer {
    padding: var(--spacing-3);
  }
  
  .popup-title {
    font-size: 32rpx;
  }
}

@media screen and (max-width: 600rpx) {
  .popup-header {
    padding: var(--spacing-2) var(--spacing-3);
  }
  
  .popup-footer {
    padding: var(--spacing-2) var(--spacing-3);
  }
}
</style>