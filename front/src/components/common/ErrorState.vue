<template>
  <view class="error-state" :class="errorClass">
    <view class="error-content">
      <!-- 错误图标 -->
      <view class="error-icon">
        <text :class="config.icon"></text>
      </view>

      <!-- 错误标题 -->
      <text v-if="config.title" class="error-title">
        {{ config.title }}
      </text>

      <!-- 错误描述 -->
      <text v-if="config.message" class="error-message">
        {{ config.message }}
      </text>

      <!-- 重试按钮 -->
      <button
        v-if="config.showRetry"
        class="error-retry-btn"
        :disabled="isRetrying"
        @click="handleRetry"
      >
        <text
          :class="
            isRetrying
              ? 'i-solar:refresh-broken retry-icon retry-icon--spinning'
              : 'i-solar:refresh-broken retry-icon'
          "
        ></text>
        <text>{{ isRetrying ? "重试中..." : config.retryText }}</text>
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";
import type { ErrorConfig } from "@/utils/ui/page-state";
import { getErrorConfig, isNetworkError } from "@/utils/ui/page-state";

interface Props {
  error?: any;
  config?: Partial<ErrorConfig>;
  onRetry?: () => void | Promise<void>;
  fullHeight?: boolean;
  isRetrying?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  fullHeight: true,
  isRetrying: false,
});

const emit = defineEmits<{
  retry: [];
}>();

// 生成错误配置
const config = computed(() => {
  const isNetwork = isNetworkError(props.error);
  const defaultConfig = getErrorConfig(props.error, isNetwork);

  return {
    ...defaultConfig,
    ...props.config,
  };
});

// 动态类名
const errorClass = computed(() => ({
  "error-state--full-height": props.fullHeight,
  "error-state--network": isNetworkError(props.error),
}));

// 处理重试
const handleRetry = async () => {
  if (props.onRetry) {
    try {
      await props.onRetry();
    } catch (error) {
      console.error("重试失败:", error);
    }
  }
  emit("retry");
};
</script>

<style lang="scss" scoped>
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48rpx;

  &--full-height {
    min-height: 400rpx;
  }

  &--network {
    .error-icon text {
      color: var(--text-blue);
    }
  }
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 480rpx;
  gap: var(--spacing);
}

.error-icon {
  font-size: 120rpx;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-2);

  text {
    display: inline-block;
  }
}

.error-title {
  font-size: 32rpx;
  font-weight: 500;

  line-height: var(--line-height-small);
}

.error-message {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  text-align: center;
  margin-bottom: var(--spacing-2);
}

.error-retry-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: 28rpx 56rpx;
  background: linear-gradient(
    135deg,
    var(--primary) 0%,
    var(--primary-600) 100%
  );
  color: var(--text-inverse);
  border: none;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);

  &:hover {
    background: linear-gradient(
      135deg,
      var(--primary-600) 0%,
      var(--primary-700) 100%
    );
    transform: translateY(-2rpx);
    box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.2);
  }



  &:disabled {
    background: linear-gradient(135deg, #ccc 0%, #aaa 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
}

.retry-icon {
  transition: transform 0.3s ease;

  .error-retry-btn:active & {
    animation: spin 0.5s ease-in-out;
  }

  &--spinning {
    animation: spin 1s linear infinite;
  }
}

// 按钮按下时的旋转动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media screen and (max-width: 768rpx) {
  .error-content {
    max-width: 90%;
  }

  .error-icon {
    font-size: 96rpx;
  }

  .error-title {
  }
}
</style>
