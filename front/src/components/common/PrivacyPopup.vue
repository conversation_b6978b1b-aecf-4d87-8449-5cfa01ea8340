<template>
  <tui-bottom-popup
    :show="show"
    z-index="99999999"
    @close="handleClose"
    :radius="true"
  >
    <view class="privacy-popup-content">
      <view class="privacy-title">服务协议和隐私政策</view>
      <!-- TODO: 请在这里替换为您的插图 -->
      <image
        class="privacy-image"
        src="/static/images/privacy-illustrator.png"
        mode="aspectFit"
      ></image>
      <view class="privacy-text">
        <text
          >感谢选择店长直聘小程序，我们非常重视您的个人信息安全和隐私保护。依据最新法律要求，使用我们的产品前，请仔细阅读并同意</text
        >
        <text class="link" @click="openPolicy('userAgreement')"
          >《用户协议》</text
        >
        <text class="link" @click="openPolicy('privacyPolicy')"
          >《隐私政策》</text
        >
        <text>和</text>
        <text class="link" @click="openPolicy('appUsageGuide')"
          >《店长直聘|求职招聘找工作小程序隐私保护指引》</text
        >
        <text
          >，以便我们向你提供更优质的服务！我们承诺将尽全力保护你个人信息及合法权益，再次感谢您的信任！</text
        >
      </view>
      <view class="button-group">
        <button class="reject-btn" @click="handleReject">拒绝</button>
        <button
          v-if="globalStore.isUserRegistered"
          class="agree-btn"
          @click="handleDirectLogin"
        >
          同意并继续
        </button>
        <button
          v-else
          class="agree-btn"
          open-type="getPhoneNumber"
          @getphonenumber="handleGetPhoneNumber"
        >
          同意并绑定手机号
        </button>
      </view>
    </view>
  </tui-bottom-popup>
</template>

<script setup lang="ts">
import { useWechatLogin, useDeviceId } from "@/services/auth";
import { useGlobalStore } from "@/stores/global";

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["agree", "reject", "close", "directLogin"]);

const globalStore = useGlobalStore();

// 使用认证服务层的 composable
const { isLoggingIn, login } = useWechatLogin();
const { getDeviceId } = useDeviceId();

const handleDirectLogin = async () => {
  // 每次登录都获取新的微信code，避免重复使用
  uni.login({
    provider: "weixin",
    success: async (res) => {
      try {
        uni.showLoading({ title: "登录中..." });

        // 已注册用户直接登录，使用新获取的loginCode
        const response = await login({
          loginCode: res.code,
          deviceId: getDeviceId(),
        });

        emit("directLogin", response);
        uni.setStorageSync("hasAgreedPrivacy", true);
        globalStore.hidePrivacyPopup();
      } catch (error) {
        console.error("直接登录失败:", error);
        uni.showToast({ title: "登录失败，请重试", icon: "none" });
      } finally {
        uni.hideLoading();
      }
    },
    fail: (err) => {
      console.log("login fail:", err);
      uni.showToast({ title: "获取登录信息失败", icon: "none" });
    },
  });
};

const handleGetPhoneNumber = async (e: any) => {
  if (!e.detail.code) {
    handleReject();
    return;
  }

  // 获取新的微信登录code进行注册
  uni.login({
    provider: "weixin",
    success: async (loginRes) => {
      try {
        uni.showLoading({ title: "登录中..." });

        // 新用户注册，使用新获取的loginCode和手机号授权code
        const response = await login({
          loginCode: loginRes.code,
          phoneCode: e.detail.code,
          deviceId: getDeviceId(),
        });

        emit("agree", response);
        uni.setStorageSync("hasAgreedPrivacy", true);
        globalStore.hidePrivacyPopup();
        uni.showToast({ title: "注册成功", icon: "success" });
      } catch (error) {
        console.error("注册失败:", error);
        uni.showToast({ title: "注册失败，请重试", icon: "none" });
      } finally {
        uni.hideLoading();
      }
    },
    fail: (err) => {
      console.error("获取微信登录信息失败:", err);
      uni.showToast({ title: "获取登录信息失败", icon: "none" });
    },
  });
};

const handleReject = () => {
  globalStore.hidePrivacyPopup();
  // emit("reject");
};

const handleClose = () => {
  emit("close");
};

const openPolicy = (
  type: "userAgreement" | "privacyPolicy" | "appUsageGuide"
) => {
  // TODO: 跳转到对应的协议页面
  console.log("open policy:", type);
  // uni.navigateTo({
  //     url: `/pages/policy/${type}`
  // });
};
</script>

<style lang="scss" scoped>
.privacy-popup-content {
  background-color: #fff;
  border-radius: 16rpx 16rpx 0 0;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.privacy-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.privacy-image {
  width: 200rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.privacy-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  text-align: left;
  margin-bottom: 40rpx;

  .link {
    color: var(--primary);
    text-decoration: none;
  }
}

.button-group {
  display: flex;
  justify-content: space-between;
  gap: 24rpx;
  width: 100%;
}

.reject-btn,
.agree-btn {
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  text-align: center;
}

.reject-btn {
  flex: 1;
  background-color: var(--bg-page);
  color: var(--text-info);
  border: none;
}

.agree-btn {
  flex: 3;
  background-color: var(--primary);
  color: #fff;
  border: none;
}
</style>
