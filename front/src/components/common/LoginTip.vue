<template>
  <view
    v-if="!isLogin && showTip"
    class="login-tip"
    :class="{ 'login-tip--fixed': isFixed }"
  >
    <view class="login-tip__content">
      <view class="login-tip__text">
        <text class="login-tip__title">{{ title }}</text>
        <text class="login-tip__subtitle">{{ subtitle }}</text>
      </view>
      <button class="login-tip__button" @tap="handleLogin">立即登录</button>
    </view>
    <view v-if="closable" class="login-tip__close" @tap="handleClose">
      <text class="i-carbon-close"></text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useUserStore } from "@/stores/user";
import { useGlobalStore } from "@/stores/global";

interface Props {
  /** 是否固定定位 */
  isFixed?: boolean;
  /** 是否可关闭 */
  closable?: boolean;
  /** 自定义提示文本 */
  title?: string;
  subtitle?: string;
}

const props = withDefaults(defineProps<Props>(), {
  isFixed: true,
  closable: true,
  title: "立即登录",
  subtitle: "享受更多专属服务",
});

const emit = defineEmits<{
  login: [];
  close: [];
}>();

const userStore = useUserStore();
const globalStore = useGlobalStore();

// 是否已登录
const isLogin = computed(() => userStore.isLogin);

// 是否显示提示
const showTip = computed(() => {
  // 如果全局隐藏登录提示，则不显示
  return !globalStore.hideLoginTip;
});

// 处理登录点击
const handleLogin = () => {
  emit("login");
  // 触发全局登录事件
  uni.$emit("onLogin");
};

// 处理关闭
const handleClose = () => {
  emit("close");
  // 临时隐藏登录提示（会话级别）
  globalStore.setHideLoginTip(true);
};
</script>

<style lang="scss" scoped>
.login-tip {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  margin: 20rpx;
  position: relative;
  overflow: hidden;
  border: 1rpx solid rgba(255, 255, 255, 0.1);

  &--fixed {
    position: fixed;
    bottom: 180rpx; /* 进一步增加底部距离 */
    left: 32rpx;
    right: 32rpx;
    z-index: 998; /* 低于弹窗，高于页面内容 */
    margin: 0;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
    animation: slide-up 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }

  &__content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 32rpx;
    position: relative;
  }

  &__text {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4rpx;
  }

  &__title {
    font-size: 28rpx;
    font-weight: 600;
    color: #ffffff;
    line-height: 1.3;
  }

  &__subtitle {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.3;
  }

  &__button {
    background: rgba(255, 255, 255, 0.95);
    color: #6a5acd;
    border-radius: 40rpx;
    padding: 12rpx 32rpx;
    font-size: 26rpx;
    font-weight: 600;
    margin-left: 24rpx;
    transition: all 0.2s ease-in-out;

  }

  &__close {
    position: absolute;
    top: 16rpx;
    right: 16rpx;
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;

    .i-carbon-close {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.7);
    }

  
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
