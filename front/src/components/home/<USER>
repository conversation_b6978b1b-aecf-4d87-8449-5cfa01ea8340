<template>
  <!-- 开发快捷登录悬浮入口 -->
  <view 
    v-if="isDevLoginEnabled && !userStore.isLogin" 
    class="dev-login-fab" 
    @click="showLoginPanel"
  >
    <text class="dev-icon">🔧</text>
    <text class="dev-text">开发登录</text>
  </view>

  <!-- 底部弹框 -->
  <tui-bottom-popup
    :show="showPanel"
    z-index="99999"
    @close="closeLoginPanel"
    :radius="true"
  >
    <view class="dev-login-panel">
      <!-- 拖拽指示器 -->
      <view class="drag-indicator"></view>
      
      <!-- 标题区域 -->
      <view class="panel-header">
        <view class="panel-title">🚀 开发快捷登录</view>
        <view class="panel-subtitle">选择测试账号，一键登录开始测试</view>
      </view>

      <!-- 测试账号列表 -->
      <view class="accounts-list">
        <view 
          v-for="account in testAccounts" 
          :key="account.id"
          class="account-card"
          @click="handleLogin(account)"
        >
          <view class="account-main">
            <view class="account-avatar">
              <text class="avatar-text">{{ account.nickname.charAt(account.nickname.length - 1) }}</text>
            </view>
            <view class="account-details">
              <view class="account-name">{{ account.nickname }}</view>
              <view class="account-phone">{{ account.mobile }}</view>
              <view class="account-tags">
                <text class="tag" :class="'tag-' + account.id">{{ account.roleDescription }}</text>
              </view>
            </view>
          </view>
          <view class="login-action">
            <text class="action-text">登录</text>
            <text class="i-carbon-chevron-right"></text>
          </view>
        </view>
      </view>

      <!-- 底部操作 -->
      <view class="panel-footer">
        <view class="footer-tip">
          <text class="tip-icon">⚠️</text>
          <text class="tip-text">仅开发环境可用，生产环境自动隐藏</text>
        </view>
        <view class="footer-actions">
          <button class="action-btn secondary" @click="closeLoginPanel">取消</button>
          <button class="action-btn primary" @click="useNormalLogin">正常登录</button>
        </view>
      </view>
    </view>
  </tui-bottom-popup>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { useGlobalStore } from '@/stores/global'

interface TestAccount {
  id: number
  nickname: string
  mobile: string
  roleDescription: string
  avatar: string
}

const userStore = useUserStore()
const globalStore = useGlobalStore()
const showPanel = ref(false)

// 检查是否启用开发登录
const isDevLoginEnabled = computed(() => {
  return import.meta.env.VITE_ENABLE_DEV_LOGIN === 'true' && import.meta.env.DEV
})

// 测试账号数据
const testAccounts = ref<TestAccount[]>([
  {
    id: 1,
    nickname: '测试用户-求职者',
    mobile: '***********',
    roleDescription: '普通求职者',
    avatar: '/static/images/avatar-male.png'
  },
  {
    id: 2,
    nickname: '测试企业-招聘方', 
    mobile: '***********',
    roleDescription: '企业用户',
    avatar: '/static/images/avatar-female.png'
  },
  {
    id: 3,
    nickname: '测试管理员',
    mobile: '***********', 
    roleDescription: '超级管理员',
    avatar: '/static/images/default-avatar.png'
  }
])

// 显示登录面板
const showLoginPanel = () => {
  showPanel.value = true
}

// 关闭登录面板
const closeLoginPanel = () => {
  showPanel.value = false
}

// 处理账号登录
const handleLogin = async (account: TestAccount) => {
  uni.showLoading({ title: '登录中...' })
  
  try {
    console.log('准备调用 devTestLogin，手机号:', account.mobile)
    
    // 动态导入以确保函数可用
    const { devTestLogin: devLoginFn } = await import('@/api/auth')
    console.log('devTestLogin 函数:', devLoginFn)
    
    const response = await devLoginFn(account.mobile)
    console.log('登录响应:', response)
    
    if (response.code === 0 && response.data) {
      // 设置用户信息
      userStore.setUserInfo(response.data.user, response.data.access_token)
      
      // 关闭面板
      closeLoginPanel()
      
      // 显示成功提示
      uni.showToast({
        title: `${account.nickname} 登录成功`,
        icon: 'success'
      })
      
      // 可选：刷新页面数据
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/home/<USER>'
        })
      }, 1000)
      
    } else {
      throw new Error(response.message || '登录失败')
    }
  } catch (error: any) {
    console.error('开发登录失败:', error)
    uni.showToast({
      title: error.message || '登录失败，请重试',
      icon: 'none'
    })
  } finally {
    uni.hideLoading()
  }
}

// 使用正常登录
const useNormalLogin = () => {
  closeLoginPanel()
  globalStore.showPrivacyPopup()
}
</script>

<style lang="scss" scoped>
/* 悬浮入口按钮 */
.dev-login-fab {
  position: fixed;
  left: 32rpx;
  bottom: 140rpx;
  z-index: 998;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50rpx;
  padding: 16rpx 24rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  

}

.dev-icon {
  font-size: 28rpx;
}

.dev-text {
  font-size: 24rpx;
  color: #fff;
  font-weight: 600;
}

/* 底部弹框 */
.dev-login-panel {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 0 0 env(safe-area-inset-bottom) 0;
  max-height: 80vh;
  overflow: hidden;
}

.drag-indicator {
  width: 60rpx;
  height: 8rpx;
  background: #e0e0e0;
  border-radius: 4rpx;
  margin: 16rpx auto 0;
}

.panel-header {
  text-align: center;
  padding: 32rpx 40rpx 24rpx;
}

.panel-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 12rpx;
}

.panel-subtitle {
  font-size: 28rpx;
  color: #666;
  opacity: 0.8;
}

/* 账号列表 */
.accounts-list {
  padding: 0 32rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.account-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  margin-bottom: 16rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 20rpx;
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
  

  &:last-child {
    margin-bottom: 0;
  }
}

.account-main {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
}

.account-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
}

.account-details {
  flex: 1;
}

.account-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 6rpx;
}

.account-phone {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.account-tags {
  display: flex;
  gap: 8rpx;
}

.tag {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.tag-1 {
  background: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.tag-2 {
  background: rgba(255, 152, 0, 0.1);
  color: #ff9800;
}

.tag-3 {
  background: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.login-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 20rpx;
}

.action-text {
  font-size: 26rpx;
  color: #667eea;
  font-weight: 600;
}

/* 底部操作区 */
.panel-footer {
  padding: 24rpx 32rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 24rpx;
}

.footer-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  margin-bottom: 24rpx;
  padding: 16rpx;
  background: #fff3cd;
  border-radius: 12rpx;
}

.tip-icon {
  font-size: 24rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #856404;
}

.footer-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  
  &.secondary {
    background: #f8f9fa;
    color: #666;
  }
  
  &.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
  }

}
</style>