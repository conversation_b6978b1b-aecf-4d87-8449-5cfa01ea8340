<template>
  <view class="address-selector">
    <tui-form-item
      :label="label"
      labelSize="30rpx"
      :labelColor="addressText ? 'var(--text-info)' : 'var(--text-secondary)'"
      asterisk
      arrow
      asteriskColor="#ff4757"
      @click="showSelector"
    >
      <tui-input
        :value="addressText"
        :placeholder="placeholder"
        :borderBottom="false"
        padding="0"
        color="var(--text-base)"
        placeholderStyle="color: var(--text-grey)"
        :disabled="true"
      />
    </tui-form-item>

    <!-- 省市区选择器 -->
    <tui-bottom-popup :show="showPicker" @close="closePicker">
      <view class="picker-container">
        <view class="picker-header">
          <text class="picker-cancel" @tap="closePicker">取消</text>
          <text class="picker-title">选择地址</text>
          <text class="picker-confirm" @tap="confirmSelect">确定</text>
        </view>

        <view class="picker-tabs">
          <view
            v-for="(tab, index) in tabs"
            :key="index"
            class="tab-item"
            :class="{ active: currentTabIndex === index }"
            @tap="switchTab(index)"
          >
            {{ tab.label }}
            <text v-if="tab.label !== '请选择'" class="tab-icon">✓</text>
          </view>
        </view>

        <scroll-view
          class="options-scroll"
          scroll-y
          :scroll-into-view="scrollIntoView"
        >
          <view class="options-list">
            <view
              v-for="(option, index) in currentOptions"
              :key="option.value"
              :id="`option-${index}`"
              class="option-item"
              :class="{ active: isOptionSelected(option) }"
              @tap="selectOption(option)"
            >
              {{ option.label }}
              <text v-if="isOptionSelected(option)" class="option-icon">✓</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </tui-bottom-popup>

    <!-- 详细地址输入 -->
    <tui-form-item
      v-if="showDetailInput"
      label="详细地址"
      labelSize="30rpx"
      labelColor="var(--text-secondary)"
      :bottomBorder="false"
    >
      <tui-input
        v-model="detailAddress"
        placeholder="请输入详细地址（街道、门牌号等）"
        :borderBottom="false"
        padding="0"
        color="var(--text-base)"
        placeholderStyle="color: var(--text-grey)"
        @input="onDetailAddressChange"
      />
    </tui-form-item>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { PROVINCE_CITY_AREA, type AreaItem } from "@/constants";

interface Props {
  label?: string;
  placeholder?: string;
  showDetailInput?: boolean;
  modelValue?: {
    province?: AreaItem;
    city?: AreaItem;
    area?: AreaItem;
    detail?: string;
  };
}

interface Emits {
  (e: "update:modelValue", value: Props["modelValue"]): void;
  (e: "change", value: Props["modelValue"]): void;
}

const props = withDefaults(defineProps<Props>(), {
  label: "所在地区",
  placeholder: "请选择省/市/区",
  showDetailInput: true,
  modelValue: () => ({}),
});

const emit = defineEmits<Emits>();

const showPicker = ref(false);
const currentTabIndex = ref(0);
const scrollIntoView = ref("");
const detailAddress = ref(props.modelValue?.detail || "");

// 选中的省市区
const selectedProvince = ref<AreaItem | null>(
  props.modelValue?.province || null
);
const selectedCity = ref<AreaItem | null>(props.modelValue?.city || null);
const selectedArea = ref<AreaItem | null>(props.modelValue?.area || null);

// 临时选中的省市区（确认前）
const tempProvince = ref<AreaItem | null>(selectedProvince.value);
const tempCity = ref<AreaItem | null>(selectedCity.value);
const tempArea = ref<AreaItem | null>(selectedArea.value);

// 标签页配置
const tabs = computed(() => [
  { label: tempProvince.value?.label || "请选择", level: 0 },
  { label: tempCity.value?.label || "请选择", level: 1 },
  { label: tempArea.value?.label || "请选择", level: 2 },
]);

// 当前选项列表
const currentOptions = computed(() => {
  if (currentTabIndex.value === 0) {
    return PROVINCE_CITY_AREA;
  } else if (currentTabIndex.value === 1) {
    return tempProvince.value?.children || [];
  } else {
    return tempCity.value?.children || [];
  }
});

// 地址文本显示
const addressText = computed(() => {
  const parts = [];
  if (selectedProvince.value) parts.push(selectedProvince.value.label);
  if (selectedCity.value) parts.push(selectedCity.value.label);
  if (selectedArea.value) parts.push(selectedArea.value.label);
  return parts.join(" ");
});

// 监听详细地址变化
watch(
  () => detailAddress.value,
  (newValue) => {
    onDetailAddressChange(newValue);
  }
);

const showSelector = () => {
  showPicker.value = true;
  // 重置临时选择
  tempProvince.value = selectedProvince.value;
  tempCity.value = selectedCity.value;
  tempArea.value = selectedArea.value;
  // 定位到当前选中的标签页
  if (selectedArea.value) {
    currentTabIndex.value = 2;
  } else if (selectedCity.value) {
    currentTabIndex.value = 1;
  } else {
    currentTabIndex.value = 0;
  }
};

const closePicker = () => {
  showPicker.value = false;
};

const switchTab = (index: number) => {
  currentTabIndex.value = index;
};

const isOptionSelected = (option: AreaItem) => {
  if (currentTabIndex.value === 0) {
    return tempProvince.value?.value === option.value;
  } else if (currentTabIndex.value === 1) {
    return tempCity.value?.value === option.value;
  } else {
    return tempArea.value?.value === option.value;
  }
};

const selectOption = (option: AreaItem) => {
  if (currentTabIndex.value === 0) {
    // 选择省份
    tempProvince.value = option;
    tempCity.value = null;
    tempArea.value = null;
    if (option.children && option.children.length > 0) {
      currentTabIndex.value = 1;
    }
  } else if (currentTabIndex.value === 1) {
    // 选择城市
    tempCity.value = option;
    tempArea.value = null;
    if (option.children && option.children.length > 0) {
      currentTabIndex.value = 2;
    }
  } else {
    // 选择区域
    tempArea.value = option;
  }
};

const confirmSelect = () => {
  selectedProvince.value = tempProvince.value;
  selectedCity.value = tempCity.value;
  selectedArea.value = tempArea.value;

  const value = {
    province: selectedProvince.value,
    city: selectedCity.value,
    area: selectedArea.value,
    detail: detailAddress.value,
  };

  emit("update:modelValue", value);
  emit("change", value);
  closePicker();
};

const onDetailAddressChange = (value: string) => {
  detailAddress.value = value;
  const currentValue = {
    province: selectedProvince.value,
    city: selectedCity.value,
    area: selectedArea.value,
    detail: value,
  };
  emit("update:modelValue", currentValue);
  emit("change", currentValue);
};
</script>

<style lang="scss" scoped>
.address-selector {
  // 组件样式继承表单样式
}

.picker-container {
  padding: 0;
  background: var(--bg-card);
  border-radius: 32rpx 32rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid var(--border-color);
  flex-shrink: 0;
}

.picker-cancel,
.picker-confirm {
  font-size: 32rpx;
  color: var(--primary);
  font-weight: 500;
}

.picker-title {
  font-size: 36rpx;
  font-weight: 600;
}

.picker-tabs {
  display: flex;
  padding: 0 32rpx;
  background: var(--bg-page);
  border-bottom: 1rpx solid var(--border-color);
  flex-shrink: 0;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 16rpx;
  font-size: 28rpx;
  color: var(--text-secondary);
  position: relative;
  transition: all 0.2s ease;

}

.tab-icon {
  margin-left: 8rpx;
  font-size: 20rpx;
  color: var(--success);
}

.options-scroll {
  flex: 1;
  height: 600rpx;
}

.options-list {
  padding: 16rpx 0;
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  font-size: 32rpx;

  transition: background-color 0.2s ease;


}

.option-icon {
  font-size: 28rpx;
  color: var(--success);
  font-weight: 600;
}
</style>
