import { computed } from 'vue';
import type { Reactive, Ref } from 'vue';

// 定义Picker选项的类型
type PickerOption = {
  label: string;
  value: any;
  [key: string]: any; // 允许其他属性
};

/**
 * 一个强大的、可复用的Vue3组合式函数，用于简化uni-app中picker组件的使用。
 * 它直接与表单的响应式数据集成，消除了手动watch同步的需要。
 *
 * @template T - 表单数据对象的类型
 * @param {PickerOption[]} options - 选择器的选项数组，每个选项应包含 label 和 value。
 * @param {Ref<T>} formRef - 整个表单数据的ref对象。
 * @param {keyof T} key - 当前picker所绑定的表单数据中的键。
 * @returns {object} 包含处理picker所需的所有计算属性和事件处理器。
 *
 * @example
 * // 在组件中
 * const formData = ref({ approval_mode: 'manual' });
 * const {
 *   selectedIndex,
 *   selectedText,
 *   pickerRange,
 *   onPickerChange
 * } = usePicker(GIG_APPROVAL_MODE_OPTIONS, formData, 'approval_mode');
 *
 * // 在模板中
 * <picker
 *   :value="selectedIndex"
 *   :range="pickerRange"
 *   @change="onPickerChange"
 * >
 *   <view>{{ selectedText }}</view>
 * </picker>
 */
export const usePicker = <T extends Record<string, any>>(
  options: PickerOption[],
  formRef: Ref<T> | Reactive<T>,
  key: keyof T
) => {
  // 输入验证
  if (!Array.isArray(options) || options.length === 0) {
    console.error(`usePicker: Invalid options array for key "${String(key)}". Expected non-empty array, got:`, options);
    throw new Error(`usePicker: Invalid options array for key "${String(key)}"`);
  }
  
  // 验证选项格式
  const invalidOptions = options.filter(opt => !opt || typeof opt.label !== 'string' || opt.value === undefined);
  if (invalidOptions.length > 0) {
    console.error(`usePicker: Invalid option format for key "${String(key)}". Each option must have 'label' (string) and 'value' properties. Invalid options:`, invalidOptions);
    throw new Error(`usePicker: Invalid option format for key "${String(key)}"`);
  }
  // 从选项派生出picker组件需要的range数组 (只包含文本)
  const pickerRange = computed(() => options.map(opt => opt.label));

  // 根据表单的当前值，动态计算出选中的索引
  const selectedIndex = computed(() => {
    const currentValue = formRef.value[key];
    const index = options.findIndex(opt => opt.value === currentValue);
    // 如果没有找到匹配项，返回-1，让picker显示为空或placeholder
    return index;
  });

  // 根据计算出的索引，获取当前选中的文本，用于显示
  const selectedText = computed(() => {
    const index = selectedIndex.value;
    if (index > -1 && options[index]) {
      return options[index].label;
    }
    return ''; // 如果没有选中项，返回空字符串
  });

  // 当picker的值发生变化时调用的事件处理器
  const onPickerChange = (event: any) => {
    const index = event.detail.value;
    if (index > -1 && index < options.length && options[index]) {
      // 直接修改响应式表单数据，无需watch
      formRef.value[key] = options[index].value;
    } else {
      console.warn(`usePicker: Invalid picker index ${index} for key "${String(key)}", options length: ${options.length}`);
    }
  };

  return {
    /**
     * 当前选中项的索引，用于绑定到 <picker> 的 value 属性。
     */
    selectedIndex,
    /**
     * 当前选中项的显示文本。
     */
    selectedText,
    /**
     * 用于 <picker> 的 range 属性的字符串数组。
     */
    pickerRange,
    /**
     * 用于绑定到 <picker> 的 @change 事件。
     */
    onPickerChange,
  };
};
