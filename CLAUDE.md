# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a cross-platform O2O local services application targeting tier 3-4 cities and county-level users. The project consists of a UniApp frontend (supports WeChat Mini Program, H5, App) and a Go backend with clean architecture.

**Core Features:**
- Job recruitment and job seeking
- Housing rental and sales (new/second-hand)
- Dating and social networking  
- Gig work services (housekeeping, repair, etc.)
- Real-time messaging
- Payment integration

## Architecture

### Frontend (front/)
- **Framework**: Vue 3 + TypeScript + UniApp CLI
- **State Management**: Pinia with persistence
- **UI Libraries**: ThorUI, UnoCSS, uni-ui
- **Network**: Alova + uni-network
- **Build Tool**: Vite

### Backend (backend/)
- **Framework**: Go 1.24 + Gin
- **Architecture**: Clean Architecture (Controller → Service → Repository)
- **Database**: PostgreSQL + GORM
- **Cache**: Redis
- **Dependency Injection**: Google Wire
- **Logging**: Zerolog with structured JSON logs
- **Config**: Viper with YAML configs

## Common Development Commands

### Frontend Commands (run from front/)
```bash
# Development
pnpm run dev:mp-weixin    # WeChat Mini Program

# Check
pnpm run type-check       # TypeScript type checking

# Package management
pnpm install              # Install dependencies
```

### Backend Commands (run from backend/)
```bash
# Development
make dev                  # Hot reload development with air
make run                  # Direct run without hot reload
make build               # Build binary
make test                # Run all tests

# Code Quality
make check               # Quick quality check (fmt, imports, vet, tidy)
make check-all          # Full quality check (includes static analysis, security)
make fmt                # Format code
make vet                # Static analysis
make staticcheck        # Advanced static analysis

# Database
make migrate-up         # Run database migrations
make migrate-down       # Rollback migrations

# Tools Installation
make install-tools      # Install development tools (air, staticcheck, etc.)
```

## Project Structure Key Points

### Frontend Structure
```
front/src/
├── pages/              # Page components organized by feature
├── components/         # Reusable components (common/, feature-specific/)
├── services/           # Business logic layer (auth.ts, gig.ts, upload.ts)
├── stores/            # Pinia stores (global.ts, user.ts, job.ts)
├── api/               # API interface definitions
├── utils/             # Utility functions organized by layers
│   ├── core/          # Core utilities (date, string, format, validation)
│   ├── business/      # Business-specific utilities (gig.ts)
│   ├── ui/            # UI interaction utilities (feedback, navigation)
│   ├── network/       # Network layer utilities (client, request, helpers)
│   └── index.ts       # Unified export
├── constants/         # Constants and static data (standards, common, static/)
└── types/             # TypeScript type definitions
```

### Backend Structure
```
backend/
├── cmd/server/        # Application entry point
├── internal/          # Private application code
│   ├── api/           # HTTP layer (controllers, middleware, routing)
│   ├── service/       # Business logic layer
│   ├── repository/    # Data access layer
│   ├── model/         # Database models (GORM structs)
│   ├── types/         # API request/response types
│   └── utils/         # Internal utilities
├── pkg/               # Reusable packages
│   ├── config/        # Configuration management
│   ├── database/      # Database connection
│   ├── cache/         # Redis cache
│   ├── logger/        # Structured logging
│   ├── jwt/           # JWT authentication
│   ├── response/      # Unified API responses
│   └── wechat/        # WeChat integration
└── configs/           # Configuration files
```

## Development Guidelines

### API Design Standards

#### Core Principles
1. **Resource-Oriented**: APIs focus on nouns (resources), not verbs (actions)
2. **Explicit Methods**: Use HTTP methods (`GET`, `POST`, `PUT`, `PATCH`, `DELETE`) to express operations
3. **Unified Case**: **Globally enforce `snake_case`** for URL paths, query parameters, and JSON data
4. **Stateless**: Every request must contain all necessary information
5. **Versioning**: All APIs must be versioned via path: `/api/v1/...`

#### URL Structure Convention

**Resource Naming:**
- **MUST** use plural nouns for resource collections
- ✅ Correct: `/gigs`, `/users`, `/job-posts`
- ❌ Incorrect: `/gig`, `/userList`, `/create_job_post`

**Path Parameters:**
- **MUST** use curly braces `{}` and `snake_case`
- ✅ Correct: `/gigs/{gig_id}/comments/{comment_id}`
- ❌ Incorrect: `/gigs/:gigId`

**Query Parameters:**
- **MUST** use `snake_case`
  - Example: `GET /gigs?sort=-created_at,title`
- **Filtering**: Use parameter names directly
  - Example: `GET /gigs?status=active&category_id=3`
- **Pagination**: `page` and `page_size`
  - Example: `GET /gigs?page=1&page_size=20`
- **Sparse Fieldsets**: `fields` parameter for bandwidth optimization
  - Example: `GET /gigs?fields=id,title,salary_min,salary_max`

#### JSON Response Standards

**Keys**: **MUST** use `snake_case`
**Timestamps**: **MUST** use `YYYY-MM-DD HH:mm:ss` format: `2025-07-15 21:07:50`

#### Frontend TypeScript Naming Convention

**Interfaces/Types**: **MUST** use `PascalCase`
- `Gig`: Core gig object
- `GigCreateRequest`: Request body for creating gig
- `GigUpdateRequest`: Request body for updating gig
- `PaginatedGigsResponse`: Paginated list response

**Variables/Properties**: **MUST** use `camelCase`
- Implement automatic `snake_case` ⇔ `camelCase` transformation in network layer
- Business logic should only deal with `camelCase`
- ✅ Correct: `const gigTitle = gig.jobTitle;`
- ❌ Incorrect: `const gig_title = gig.job_title;` (in business logic)

**Enums**: **MUST** use `PascalCase` for enum name and members
```typescript
export enum GigStatus {
  Active = 'active',
  Paused = 'paused',
  Closed = 'closed',
}
```

- **Type Definitions**: API types go in `backend/internal/types/`, NOT in model directory

### Backend Code Standards
- **Layered Architecture**: Strict Controller → Service → Repository separation
- **Error Handling**: Use `pkg/response` for consistent error responses
- **Database**: Always specify fields in GORM queries, avoid `SELECT *`
- **Logging**: Use structured logging with context (request_id, user_id)
- **Dependency Injection**: Use Google Wire for clean DI
- **Constants**: All constants and enums are defined in `backend/internal/constants/`
- **Paginate**: The pagination method uniformly uses the Paginate in the model. Use example: 'db.Scopes(Paginate(page,pageSize)).find (&users)'

#### DTO映射规范 (Data Transfer Object)

**核心原则**: 使用 `github.com/jinzhu/copier` 进行DTO转换，避免手动字段赋值

**Service层DTO转换标准**:
```go
// ✅ 推荐：使用copier进行基础转换
func (s *jobService) convertJobToResponse(job *model.Job, userID *uint) *types.JobResponse {
    var response types.JobResponse
    if err := copier.Copy(&response, job); err != nil {
        logger.Error("Failed to copy job to response", err, "job_id", job.ID)
        // Fallback to manual assignment for critical fields
        response.ID = job.ID
        response.Title = job.Title
        response.Status = job.Status
    }

    // Handle special fields that need custom logic
    response.Benefits = convertJSONToStringSlice(job.Benefits)
    response.SalaryText = generateSalaryText(job.SalaryMin, job.SalaryMax)
    
    return &response
}

// ✅ 推荐：创建操作使用copier
func (s *jobService) CreateJob(ctx context.Context, userID uint, req *types.CreateJobRequest) (*model.Job, error) {
    var job model.Job
    if err := copier.Copy(&job, req); err != nil {
        return nil, fmt.Errorf("创建职位数据复制失败: %w", err)
    }
    
    // Set additional fields not included in request
    job.UserID = userID
    job.Status = constants.JobStatusPendingReview
    
    return &job, nil
}

// ✅ 推荐：更新操作使用copier with options
func (s *jobService) applyJobUpdates(job *model.Job, req *types.UpdateJobRequest) {
    if err := copier.CopyWithOption(job, req, copier.Option{
        IgnoreEmpty: true,
        DeepCopy:    true,
    }); err != nil {
        logger.Error("Failed to copy job updates", err, "job_id", job.ID)
        // Fallback for critical fields
    }
}
```

**错误处理模式**:
- 使用 `logger.Error` 记录copier错误
- 提供fallback机制，手动赋值关键字段
- 对于更新操作，使用 `copier.CopyWithOption` 的 `IgnoreEmpty` 选项

#### Go方法命名规范

**Repository层命名规范** (基于Uber Go Style Guide):
```go
// ✅ 推荐：简洁明了的方法名
type UserRepository interface {
    Create(ctx context.Context, user *model.User) error
    Find(ctx context.Context, id uint) (*model.User, error)          // GetByID → Find
    FindByEmail(ctx context.Context, email string) (*model.User, error) // GetByEmail → FindByEmail
    Update(ctx context.Context, user *model.User) error
    Delete(ctx context.Context, id uint) error
    List(ctx context.Context, req *types.ListRequest) ([]*model.User, int64, error)
}

// Application相关方法简化
type GigRepository interface {
    CreateApp(ctx context.Context, app *model.GigApplication) error     // CreateApplication → CreateApp
    FindApp(ctx context.Context, id uint) (*model.GigApplication, error) // GetApplicationByID → FindApp
    UpdateApp(ctx context.Context, app *model.GigApplication) error     // UpdateApplication → UpdateApp
    GetUserApps(ctx context.Context, userID uint, page, pageSize int) ([]*model.GigApplication, int64, error) // GetUserApplications → GetUserApps
    GetApplications(ctx context.Context, gigID uint, page, pageSize int) ([]*model.GigApplication, int64, error) // GetGigApplications → GetApplications
}
```

**Service层命名规范**:
- **CRUD操作**: `CreateJob`, `GetJobByID`, `UpdateJob`, `DeleteJob`
- **业务操作**: `ApplyForGig`, `ReviewApplication`, `PublishJob`
- **查询操作**: `GetJobList`, `SearchJobs`, `GetNearbyJobs`
- **统计操作**: `GetUserStats`, `GetMonthlyStats`

**Controller层命名规范** (REST语义化):
- **资源操作**: `Publish`, `Detail`, `List`, `Delete`
- **业务操作**: `Apply`, `Review`, `Approve`
- **统计查询**: `GetStats`, `GetAnalytics`

#### 数据库查询优化规范

**字段选择规范**:
```go
// ✅ 推荐：明确指定需要的字段
func (r *jobRepository) Find(ctx context.Context, id uint) (*model.Job, error) {
    var job model.Job
    err := r.db.WithContext(ctx).
        Select("id", "title", "description", "status", "salary_min", "salary_max", 
               "work_location", "created_at", "updated_at").
        First(&job, id).Error
    return &job, err
}

// ❌ 避免：使用SELECT *
func (r *jobRepository) Find(ctx context.Context, id uint) (*model.Job, error) {
    var job model.Job
    err := r.db.WithContext(ctx).First(&job, id).Error  // 会选择所有字段
    return &job, err
}
```

**关联查询优化**:
```go
// ✅ 推荐：使用Preload指定字段，避免N+1查询
func (r *jobRepository) FindWithEnterprise(ctx context.Context, id uint) (*model.Job, error) {
    var job model.Job
    err := r.db.WithContext(ctx).
        Select("id", "enterprise_id", "title", "description", "status").
        Preload("Enterprise", func(db *gorm.DB) *gorm.DB {
            return db.Select("id", "name", "logo_url", "is_verified")
        }).
        First(&job, id).Error
    return &job, err
}

// ✅ 推荐：列表查询优化
func (r *jobRepository) GetList(ctx context.Context, req *types.ListRequest) ([]*model.Job, int64, error) {
    var jobs []*model.Job
    var total int64
    
    db := r.db.WithContext(ctx).Model(&model.Job{}).
        Select("id", "title", "salary_min", "salary_max", "work_location", "created_at").
        Preload("Enterprise", func(db *gorm.DB) *gorm.DB {
            return db.Select("id", "name", "logo_url")
        })
    
    // Count and pagination
    if err := db.Count(&total).Error; err != nil {
        return nil, 0, err
    }
    
    err := db.Scopes(model.Paginate(req.Page, req.PageSize)).
        Order("created_at DESC").
        Find(&jobs).Error
    return jobs, total, err
}
```

**索引和性能最佳实践**:
- 为常用查询条件添加数据库索引
- 使用 `Select` 指定具体字段，减少数据传输
- 合理使用 `Preload` 避免N+1查询问题
- 对于大数据量查询，实现游标分页而非偏移分页

## API 层设计规范 (Alova + useRequest 架构)

### API 模块组织模式 - 命名空间模式

采用**阿里字节系**大型互联网公司标准的命名空间模式，使用静态类组织 API：

#### 基本结构
```typescript
// ✅ 推荐：api/module.ts
class ModuleApi {
    // CRUD 操作
    static create = (data: CreateRequest) => request.Post('/module', data)
    static update = (id: number, data: UpdateRequest) => request.Put(`/module/${id}`, data)
    static delete = (id: number) => request.Delete(`/module/${id}`)
    static detail = (id: number) => request.Get(`/module/${id}`)
    
    // 列表查询
    static list = (params: ListRequest) => request.Get('/module', { params })
    static listMine = (params: ListRequest) => request.Get('/module/mine', { params })
    
    // 业务操作
    static someAction = (payload: ActionRequest) => request.Post('/module/action', payload)
}

export default ModuleApi
```

#### 导出和导入规范
- **默认导出类**：`export default ModuleApi`
- **Services层导入**：`import ModuleApi from '@/api/module'`

#### 方法命名规范
- **去除 `Api` 后缀**：`createGigApi` → `GigApi.create`
- **使用语义化动词**：
  - CRUD: `create`, `update`, `delete`, `detail`, `applicationsList`
  - 查询: `list`, `listMine`, `search`
  - 业务: `apply`, `review`, `approve`, `publish`

#### 功能分组规范
按业务逻辑分组，使用注释分隔：
```typescript
class GigApi {
    // ====================================================================
    // CRUD 操作
    // ====================================================================
    static create = ...
    static update = ...
    
    // ====================================================================
    // 列表查询  
    // ====================================================================
    static list = ...
    static listMine = ...
    
    // ====================================================================
    // 申请相关
    // ====================================================================
    static apply = ...
    static checkApplicationStatus = ...
}
```

### 统一请求配置

#### 请求实例规范
- **全部使用 Alova `request` 实例**
- **统一响应类型**：`ApiResponse<T>`
- **统一错误处理机制**：通过拦截器处理

#### 类型安全要求
```typescript
// ✅ 完整的类型定义
static create = (data: CreateGigRequest) => {
    return request.Post<ApiResponse<{ gigId: number }>>('/gigs', data)
}

// ✅ 导入类型
import type { ApiResponse } from '@/constants/response'
import type { CreateGigRequest, Gig } from '@/types/gig'
```

### Services 层架构规范 (Alova + useRequest)

#### Composable 函数设计模式
```typescript
// ✅ 标准 Composable 结构
export function useModuleAction() {
    const { loading, error, send } = useRequest(
        (payload: ActionRequest) => ModuleApi.action(payload),
        { immediate: false }
    ).onSuccess(() => {
        showSuccessToast('操作成功')
    })

    return { loading, error, send }
}
```

#### 配置选项最佳实践
```typescript
const { data, loading, error } = useRequest(
    () => ModuleApi.get(id.value),
    {
        immediate: id.value > 0,        // 条件触发
        watch: [id],                    // 响应式监听  
        initialData: { list: [] },      // 初始数据
        cacheFor: 5 * 60 * 1000,       // 缓存策略 (可选)
    }
)
```

#### 返回值规范
- **标准化接口**：`{ data, loading, error, send }`
- **语义化状态**：`isLoading`, `hasError`, `canSubmit`
- **方法命名**：动词形式 `create`, `update`, `delete`, `refresh`

#### 导入简化规范
```typescript
// ✅ 推荐：命名空间导入
import ModuleApi from '@/api/module'

// 使用
const { data } = useRequest(() => ModuleApi.create(formData))

// ❌ 避免：冗长的分别导入
import {
    createModuleApi,
    updateModuleApi,
    deleteModuleApi,
    // ... 10+ 个导入
} from '@/api/module'
```

### 最佳实践要点

#### 职责分离
- **API层**：纯请求定义，无业务逻辑
- **Services层**：业务逻辑封装，使用 `useRequest`
- **Pages层**：UI交互，调用 Services 层

#### 错误处理分层
- **拦截器层**：统一处理网络错误、认证错误
- **Services层**：处理业务特定错误和用户反馈
- **组件层**：处理UI相关的错误状态

#### 性能优化
- **合理使用缓存**：避免重复请求
- **条件请求**：使用 `immediate` 控制请求时机
- **请求合并**：利用 Alova 自动请求合并特性

### Frontend Standards

#### Architecture Layers (清晰的四层架构)
遵循 **Pages → Services → Utils → Constants** 的依赖流向：

1. **Pages层** - 页面组件，处理UI展示和用户交互
2. **Services层** - 业务逻辑封装，减少页面代码复杂度
3. **Utils层** - 纯工具函数，按功能分层组织
4. **Constants层** - 常量和静态数据定义

#### Services层规范
- **职责**：封装复杂业务逻辑，与Pinia store交互，处理业务级错误
- **文件组织**：
  - `services/auth.ts` - 认证相关业务逻辑
  - `services/gig.ts` - 零工业务判断和操作
  - `services/upload.ts` - 文件上传业务封装
- **命名规范**：使用动词 + 名词，如 `checkGigPermission()`, `handleUserLogin()`

#### Utils层规范 (按功能层次组织)
- **Core层** (`utils/core/`)：基础工具，无业务依赖
  - `date.ts` - 日期时间处理 (统一使用dayjs)
  - `string.ts` - 字符串处理和转换
  - `format.ts` - 数据格式化 (金额、距离、数量等)
  - `validation.ts` - 表单验证工具 (统一验证逻辑)

- **Business层** (`utils/business/`)：业务相关纯工具
  - `gig.ts` - 零工数据处理工具函数

- **UI层** (`utils/ui/`)：UI交互工具
  - `feedback.ts` - 用户反馈 (toast, loading, dialog)
  - `navigation.ts` - 页面跳转和路由处理

- **Network层** (`utils/network/`)：网络请求基础工具
  - `client.ts` - HTTP客户端配置 (Alova)
  - `request.ts` - 类型安全的请求封装
  - `helpers.ts` - API调用辅助工具 (简化版，避免过度封装)

#### 导入规范
- **统一导入**：优先从 `@/utils` 导入，保持导入路径一致
- **按需导入**：可直接从子模块导入，如 `@/utils/core/date`
- **禁止循环依赖**：严格按照架构层级导入，禁止反向依赖

#### 函数命名和组织规范
- **避免重复功能**：同一功能只在一个地方实现
- **职责单一**：每个函数只处理一个明确的功能
- **命名清晰**：函数名表达功能意图，避免缩写
- **类型完整**：所有函数都要有完整的TypeScript类型定义

#### 开发规范
- **Component Organization**: Break down large files into smaller components
- **State Management**: Use Pinia stores for global state
- **API Calls**: Use Alova for network requests with proper error handling
- **Styling**: Combine UnoCSS utilities with SCSS for complex styles
- **Constants**: Define all constants in `front/src/constants/`
- **Types**: Define all types in `front/src/types/`

#### 代码质量要求
- **简洁高效**：不重复封装函数，避免过度设计
- **易于维护**：团队成员能快速理解和定位功能
- **类型安全**：充分利用TypeScript的类型检查
- **向后兼容**：重构时提供兼容性导出，避免破坏性变更

#### 防重复函数检查规范 ⚠️

为避免重复造轮子和代码混乱，开发时必须遵循以下检查流程：

**1. 新增工具函数前的检查清单**
- [ ] 在 `utils/core/` 中搜索相似功能函数
- [ ] 在 `utils/business/` 中检查业务相关工具
- [ ] 在 `constants/standards.ts` 中查找标准化工具函数
- [ ] 使用 VSCode 全局搜索关键词（如：format、validate、get、is等）

**2. 函数命名唯一性原则**
- **同一功能只能有一个实现**：相同功能的函数只在一个地方定义
- **命名要清晰明确**：函数名必须准确表达功能，避免歧义
- **避免近似命名**：如 `formatSalary` 与 `formatGigSalary` 应该统一
- **使用别名兼容**：使用新函数替换掉旧函数

**3. 强制检查点**
开发新功能时，在以下时机进行重复检查：
- **编写工具函数时**：先搜索现有实现
- **导入函数时**：确认导入路径正确且唯一
- **Code Review时**：审查者必须检查是否有重复
- **重构时**：清理所有重复实现

**4. 重复函数处理策略**
发现重复函数时的处理顺序：
1. **评估功能**：比较两个函数的功能和实现质量
2. **选择保留**：保留功能完整、命名清晰、位置合理的版本
3. **创建别名**：为兼容性创建导出别名，不要直接删除
4. **更新引用**：逐步更新所有调用点到统一函数
5. **添加注释**：在删除的位置添加重定向注释

**5. 工具函数放置规则**
- **Core层**：纯工具函数，无业务依赖
- **Business层**：业务相关工具，可依赖Constants
- **UI层**：界面交互工具，可依赖Core
- **Network层**：网络请求工具，可依赖UI和Core

**6. 违规处理**
- 请勿封装功能重复、功能相近的工具函数，发现重复函数必须立即整改
- 重复实现将被视为代码质量问题
- 严重情况下需要重构相关模块

**示例检查命令：**
```bash
# 搜索可能重复的格式化函数
grep -r "export.*format" src/utils/
# 搜索验证函数
grep -r "export.*validate" src/utils/
# 搜索获取函数  
grep -r "export.*get[A-Z]" src/utils/
```


## Key Configuration Files

### Backend Config (backend/configs/config.yaml)
Contains database, Redis, JWT, payment (WeChat/Alipay), storage (Qiniu), and SMS (Aliyun) configurations.

### Frontend Config (front/vite.config.ts)
- Environment variables from `front/env/`
- Auto-imports for Vue and uni-app APIs
- UnoCSS for utility-first CSS
- Path aliases (`@` → `src/`)

## Testing & Quality

### Backend Testing
```bash
make test              # All tests
make test-coverage     # Coverage report (generates coverage.html)
make benchmark         # Performance benchmarks
```

### Code Quality Tools
- **gofmt**: Code formatting
- **go vet**: Basic static analysis
- **staticcheck**: Advanced static analysis
- **gosec**: Security analysis
- **errcheck**: Error handling verification

## WeChat Mini Program Notes
- Configured for Skyline rendering engine
- Pay attention to compatibility requirements
- Reference: https://developers.weixin.qq.com/miniprogram/dev/framework/runtime/skyline/introduction.html

## Database Operations
- Use migrations in `backend/migrations/`
- Follow proper indexing for query performance
- Wrap multiple operations in transactions
- Always handle `gorm.ErrRecordNotFound`

## Data Type Standards

### User ID Field Type
**IMPORTANT**: 
All user ID fields (userId, user_id, etc.) throughout the entire codebase MUST use `uint` type:
The way to obtain the userID is userID := GetUserID(ctx)

**Backend (Go):**
```go
// ✅ Correct
UserID uint `gorm:"not null;index" json:"user_id"`

// ❌ Wrong
UserID uint64 `gorm:"not null;index" json:"user_id"`
UserID int `gorm:"not null;index" json:"user_id"`
```

**Frontend (TypeScript):**
```typescript
// ✅ Correct
userId: number

// ❌ Wrong  
userId: string
```

**Why this standard:**
- BaseModel.ID uses `uint` type
- Foreign key relationships must match the referenced primary key type
- Prevents type mismatch errors in wire generation and GORM queries
- Ensures consistency across all modules (gig, user, etc.)

**Apply to all models:**
- User model primary key: `uint`
- All foreign key references to users: `uint`
- API request/response types: `number` (TypeScript)

### Category Removal Standard
The gig module does NOT use categories. Remove all category-related:
- Database tables and fields
- API endpoints and services  
- Frontend components and types
- Backend models and repositories

## Wire Dependency Injection
The backend uses Google Wire for dependency injection. Key files:
- `backend/internal/api/wire.go`: Main DI configuration
- Generated code in `wire_gen.go` files
- Provider sets in each layer (repository, service, controller)

## Frontend Color System & Styling Guidelines

### Color Variables (Use CSS Variables)
The project has a comprehensive color system defined in `front/src/styles/app.css` and `front/uno.config.ts`:
优先使用app.css的主色调，布局优先采用flex布局，使用unocss进行布局
uno.config.ts中已经预定义了很多可以直接使用的css属性


### Component Styling Guidelines

- Always use `var(--color-name)` for colors
- Maintain 4:1 contrast ratio for accessibility
- Use semantic color variables for consistent theming
- Follow the 8rpx grid system for spacing
- Prefer utility classes over custom CSS when possible
- Keep component styles modular and reusable

## 代码质量保障规范 🔍

### 强制执行的代码检测流程

**重要提醒**：每次完成代码编写/修改后，必须按照以下流程进行代码检测和审查，确保代码质量和系统稳定性。

#### 1. 立即检测阶段 ⚡

**在每次代码修改完成后立即执行：**

```bash
# 前端检测 (在 front/ 目录下执行)
pnpm run type-check              # TypeScript 类型检测
pnpm run lint                    # ESLint 代码规范检测
pnpm run build                   # 构建检测，确保无编译错误

# 后端检测 (在 backend/ 目录下执行)  
make check                       # 快速质量检查 (fmt, imports, vet, tidy)
make check-all                   # 完整质量检查 (包含静态分析、安全检查)
make test                        # 运行所有测试
make build                       # 构建检测
```

#### 2. 代码审查阶段 🔍

**对修改的代码进行系统性审查：**

- **类型安全审查**：
  - 确保所有接口定义正确
  - 检查类型推断是否准确
  - 验证泛型使用是否合理

- **逻辑完整性审查**：
  - 检查边界条件处理
  - 验证错误处理逻辑
  - 确认异步操作正确性

- **性能影响审查**：
  - 避免不必要的重复计算
  - 检查内存泄漏风险
  - 优化网络请求效率

- **安全性审查**：
  - 输入验证和清理
  - 权限检查完整性
  - 敏感信息保护

#### 3. 功能验证阶段 ✅

**确保功能正常工作：**

- **基础功能测试**：
  - 验证核心业务逻辑
  - 测试用户交互流程
  - 检查数据流完整性

- **边界情况测试**：
  - 异常输入处理
  - 网络异常恢复
  - 空数据状态显示

- **集成测试**：
  - 前后端接口对接
  - 第三方服务集成
  - 跨平台兼容性

#### 4. 问题修复循环 🔄

**发现问题时的处理流程：**

1. **问题分类**：
   - 类型错误 → 立即修复
   - 逻辑错误 → 分析根因后修复
   - 性能问题 → 优化后验证
   - 安全问题 → 高优先级修复

2. **修复验证**：
   - 重新执行检测流程
   - 确认问题完全解决
   - 避免引入新问题

3. **回归测试**：
   - 测试相关功能
   - 验证整体稳定性
   - 确保无副作用

### 违规处理原则

- **零容忍政策**：任何跳过检测流程的代码修改都不被接受
- **修复优先**：发现问题必须立即修复，不能延后处理
- **质量第一**：代码质量优先于开发速度
- **文档同步**：代码修改必须同步更新相关文档

### 检测工具配置

#### 前端工具链
- **TypeScript**: 严格模式，完整类型检查
- **ESLint**: 使用推荐规则集 + 项目自定义规则
- **Prettier**: 统一代码格式化
- **Vite**: 构建时类型检查和优化

#### 后端工具链
- **Go fmt**: 标准代码格式化
- **Go vet**: 静态分析检查
- **staticcheck**: 高级静态分析
- **gosec**: 安全漏洞检测
- **errcheck**: 错误处理检查

### 质量指标要求

- **类型覆盖率**: 100% (无 `any` 类型)
- **测试覆盖率**: 核心业务逻辑 ≥ 80%
- **构建成功率**: 100% (无警告)
- **静态分析**: 零问题通过
- **安全扫描**: 零高危漏洞

### 持续改进

定期评估和优化检测流程：
- 工具链升级和配置优化
- 新增自动化检测项目
- 团队培训和最佳实践分享
- 问题模式总结和预防措施

**记住**：良好的代码质量是项目成功的基石，严格的检测流程是代码质量的保障！